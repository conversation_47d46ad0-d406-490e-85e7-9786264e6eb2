package com.datayes.hdfs

import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * HDFS Shell Script Service 测试
 *
 * 测试HDFS shell脚本处理服务的功能
 */
class HdfsShellScriptServiceTest {

    private val logger = LoggerFactory.getLogger(HdfsShellScriptServiceTest::class.java)

    @Test
    fun `test HdfsShellScriptJob creation from ShellScript`() {
        // 创建测试数据
        val zipFilePath = "hdfs://hdfsCluster/share_ftp/public_project/test.zip"
        val shellScript = ShellScript(
            name = "test_script.sh",
            content = """#!/bin/bash
                |export HIVE_HOME=/opt/hive
                |hive -e "SELECT * FROM source_table WHERE date='2024-01-01'"
                |hive -e "INSERT INTO target_table SELECT col1, col2 FROM source_table"
            """.trimMargin(),
            sizeBytes = 150
        )

        // 测试创建HdfsShellScriptJob
        val job = HdfsShellScriptJob.fromShellScript(zipFilePath, shellScript)

        // 验证结果
        assertNotNull(job)
        assertTrue(job.jobId.startsWith("hdfs_"))
        assertTrue(job.jobName == "test_script")
        assertTrue(job.zipFilePath == zipFilePath)
        assertTrue(job.scriptName == "test_script.sh")
        assertTrue(job.scriptContent.contains("SELECT * FROM source_table"))
        assertTrue(job.scriptSizeBytes == 150)
        assertTrue(job.status == HdfsJobStatus.ACTIVE)

        logger.info("d4f8e2a9 | 成功创建HDFS作业: {}", job.jobId)
        logger.info("e7c3f5b1 | 作业名称: {}", job.jobName)
        logger.info("a2b9e6f4 | 脚本大小: {} bytes", job.scriptSizeBytes)
    }

    @Test
    fun `test HdfsShellScriptLineageConverter basic functionality`() {
        // 创建测试shell脚本作业
        val job = HdfsShellScriptJob(
            jobId = "hdfs_test123456789",
            jobName = "test_etl_script",
            zipFilePath = "hdfs://hdfsCluster/share_ftp/public_project/etl.zip",
            scriptName = "etl_process.sh",
            scriptContent = """#!/bin/bash
                |# ETL process script
                |export HIVE_HOME=/opt/hive
                |
                |# Extract data
                |hive -e "SELECT customer_id, order_date, amount FROM orders WHERE order_date >= '2024-01-01'"
                |
                |# Transform and load
                |hive -e "INSERT INTO monthly_summary 
                |         SELECT customer_id, 
                |                DATE_FORMAT(order_date, 'yyyy-MM') as month,
                |                SUM(amount) as total_amount
                |         FROM orders 
                |         WHERE order_date >= '2024-01-01'
                |         GROUP BY customer_id, DATE_FORMAT(order_date, 'yyyy-MM')"
            """.trimMargin(),
            scriptSizeBytes = 500
        )

        // 测试血缘转换
        val lineageResult = HdfsShellScriptLineageConverter.convertToLineage(job, mappings, x)

        // 验证结果
        assertNotNull(lineageResult)
        logger.info("f5c8e3a7 | 血缘转换成功: {}", lineageResult.success)
        logger.info("b1d6f9e2 | 警告数量: {}", lineageResult.warnings.size)
        logger.info("c4a7e8f3 | 错误数量: {}", lineageResult.errors.size)

        // 打印警告和错误信息
        lineageResult.warnings.forEach { warning ->
            logger.warn("警告: {}", warning)
        }
        lineageResult.errors.forEach { error ->
            logger.error("错误: {}", error)
        }

        // 如果成功，打印血缘信息
        if (lineageResult.success && lineageResult.lineage != null) {
            val lineage = lineageResult.lineage!!
            logger.info("9e2f7c4a | 作业ID: {}", lineage.jobId)
            logger.info("a8f3e6b9 | 作业名称: {}", lineage.jobName)
            logger.info("d7b4f2e8 | 源表数量: {}", lineage.tableLineage.sourceTables.size)
            logger.info("e3c9f5a2 | 目标表: {}", lineage.tableLineage.targetTable.tableName)
            logger.info("f6a8e4c7 | 血缘类型: {}", lineage.tableLineage.lineageType)
            logger.info("b9e2f7d4 | 列血缘数量: {}", lineage.columnLineages.size)

            // 打印源表信息
            lineage.tableLineage.sourceTables.forEachIndexed { index, table ->
                logger.info("c5f8e9a3 | 源表{}: {}.{}", index + 1, table.database.databaseName, table.tableName)
            }

            // 打印列血缘信息
            lineage.columnLineages.take(5).forEachIndexed { index, columnLineage ->
                logger.info(
                    "d2a7f4e6 | 列血缘{}: {} -> {}",
                    index + 1,
                    columnLineage.sourceColumn.columnName,
                    columnLineage.targetColumn.columnName
                )
            }
        }

        // 验证基本要求
        assertTrue(
            lineageResult.warnings.isNotEmpty() || lineageResult.success,
            "应该成功或至少有警告信息"
        )
    }

    @Test
    fun `test HdfsProcessingConfig default values`() {
        val config = HdfsProcessingConfig()

        // 验证默认配置
        assertTrue(config.hdfsBasePath == "/share_ftp")
        assertTrue(config.includePatterns.contains("*.zip"))
        assertTrue(config.enableBackupFiltering)
        assertTrue(config.maxScriptSizeBytes == 10 * 1024 * 1024) // 10MB

        logger.info("a4e7f2c9 | 默认HDFS基础路径: {}", config.hdfsBasePath)
        logger.info("b8f3e6a5 | 包含模式: {}", config.includePatterns)
        logger.info("c2d9f4e7 | 最大脚本大小: {} bytes", config.maxScriptSizeBytes)
        logger.info("d6a3f8b1 | 启用备份文件过滤: {}", config.enableBackupFiltering)
    }

    @Test
    fun `test complex shell script with multiple SQL statements`() {
        val complexScript = """#!/bin/bash
            |# Complex ETL script with multiple operations
            |
            |# Set variables
            |export DATE_PARTITION='2024-01-01'
            |export TARGET_DB='warehouse'
            |
            |# Load data (Hive command - should be filtered out)
            |hive -e "load data inpath '/project/public_project/spl/spl_hive_import.csv' overwrite into table spl_sit_hive.t_ca_alloc_dataset_detail"
            |
            |# Set Hive configuration (should be filtered out)
            |hive -e "set hive.exec.dynamic.partition=true"
            |
            |# Create staging table (valid SQL - should be kept)
            |hive -e "CREATE TABLE IF NOT EXISTS staging.customer_orders AS
            |         SELECT c.customer_id, c.customer_name, o.order_id, o.order_date, o.amount
            |         FROM customers c
            |         JOIN orders o ON c.customer_id = o.customer_id
            |         WHERE o.order_date = '${'$'}DATE_PARTITION'"
            |
            |# Show tables (Hive command - should be filtered out)
            |hive -e "show tables"
            |
            |# Data quality check (valid SQL - should be kept)
            |hive -e "SELECT COUNT(*) as record_count FROM staging.customer_orders"
            |
            |# Insert into final table (valid SQL - should be kept)
            |hive -e "INSERT OVERWRITE TABLE warehouse.daily_customer_summary
            |         PARTITION(date_partition='${'$'}DATE_PARTITION')
            |         SELECT customer_id,
            |                customer_name,
            |                COUNT(order_id) as order_count,
            |                SUM(amount) as total_amount,
            |                AVG(amount) as avg_amount
            |         FROM staging.customer_orders
            |         GROUP BY customer_id, customer_name"
            |
            |# Describe table (Hive command - should be filtered out)
            |hive -e "describe staging.customer_orders"
            |
            |# Cleanup (valid SQL - should be kept)
            |hive -e "DROP TABLE staging.customer_orders"
        """.trimMargin()

        val job = HdfsShellScriptJob(
            jobId = "hdfs_complex_test",
            jobName = "complex_etl",
            zipFilePath = "hdfs://hdfsCluster/share_ftp/complex.zip",
            scriptName = "complex_etl.sh",
            scriptContent = complexScript,
            scriptSizeBytes = complexScript.length
        )

        // 测试复杂脚本的血缘转换
        val lineageResult = HdfsShellScriptLineageConverter.convertToLineage(job, mappings, x)

        logger.info("e5f9c2a8 | 复杂脚本血缘转换结果:")
        logger.info("f7a4e6b3 | 成功: {}", lineageResult.success)
        logger.info("c8e2f9a6 | 警告数量: {}", lineageResult.warnings.size)
        logger.info("d1b5f3e9 | 错误数量: {}", lineageResult.errors.size)

        // 验证结果
        assertNotNull(lineageResult)

        // 详细日志输出
        lineageResult.warnings.forEach { warning ->
            logger.warn("警告: {}", warning)
        }

        if (lineageResult.lineage != null) {
            logger.info("a9f6e3c4 | 识别的血缘类型: {}", lineageResult.lineage!!.tableLineage.lineageType)
            logger.info("b4e7f2a8 | 源表数量: {}", lineageResult.lineage!!.tableLineage.sourceTables.size)
        }
    }

    @Test
    fun `test Hive command filtering in shell script`() {
        // 测试脚本包含各种Hive命令，只有真正的SQL应该被处理
        val scriptWithHiveCommands = """#!/bin/bash
            |# Script with mix of Hive commands and SQL
            |
            |# These should be FILTERED OUT (Hive commands):
            |hive -e "load data inpath '/data/file.csv' overwrite into table test_table"
            |hive -e "set hive.exec.dynamic.partition=true"
            |hive -e "show tables"
            |hive -e "describe test_table"
            |hive -e "use test_db"
            |hive -e "add jar /path/to/some.jar"
            |hive -e "msck repair table test_table"
            |
            |# These should be KEPT (valid SQL):
            |hive -e "SELECT * FROM source_table WHERE date='2024-01-01'"
            |hive -e "INSERT INTO target_table SELECT col1, col2 FROM source_table"
            |hive -e "CREATE TABLE new_table AS SELECT * FROM existing_table"
            |hive -e "DROP TABLE temp_table"
            |hive -e "UPDATE test_table SET col1='value' WHERE id=1"
        """.trimMargin()

        val job = HdfsShellScriptJob(
            jobId = "hdfs_filter_test",
            jobName = "hive_command_filter_test",
            zipFilePath = "hdfs://hdfsCluster/share_ftp/filter_test.zip",
            scriptName = "filter_test.sh",
            scriptContent = scriptWithHiveCommands,
            scriptSizeBytes = scriptWithHiveCommands.length
        )

        // 测试血缘转换
        val lineageResult = HdfsShellScriptLineageConverter.convertToLineage(job, mappings, x)

        logger.info("e8f5c9a2 | Hive命令过滤测试结果:")
        logger.info("d3a7f6b4 | 转换成功: {}", lineageResult.success)
        logger.info("f2e9c8a5 | 警告数量: {}", lineageResult.warnings.size)
        logger.info("a6b3f7e1 | 错误数量: {}", lineageResult.errors.size)

        // 打印所有警告（应该包含被过滤的Hive命令）
        lineageResult.warnings.forEach { warning ->
            logger.info("警告: {}", warning)
        }

        // 验证结果
        assertNotNull(lineageResult)

        // 应该有警告信息，因为一些Hive命令被过滤了
        assertTrue(lineageResult.warnings.isNotEmpty(), "应该有关于过滤Hive命令的警告")

        // 检查警告中是否包含预期的过滤信息
        val hiveCommandWarnings = lineageResult.warnings.filter {
            it.contains("跳过Hive命令") || it.contains("load data") || it.contains("show tables")
        }
        assertTrue(hiveCommandWarnings.isNotEmpty(), "应该有关于跳过Hive命令的具体警告")

        logger.info("c5f8e3a9 | 成功过滤了{}个Hive命令相关的警告", hiveCommandWarnings.size)

        // 如果有血缘信息，说明至少有一些有效的SQL被处理了
        if (lineageResult.lineage != null) {
            logger.info(
                "b7a4f6e2 | 成功处理了有效SQL，源表数量: {}",
                lineageResult.lineage!!.tableLineage.sourceTables.size
            )
        }
    }

    @Test
    fun `test change detection for duplicate processing`() {
        // 模拟相同的shell脚本作业被处理两次
        val scriptContent = """#!/bin/bash
            |hive -e "SELECT customer_id, order_date FROM orders WHERE date='2024-01-01'"
            |hive -e "INSERT INTO summary_table SELECT customer_id, COUNT(*) FROM orders GROUP BY customer_id"
        """.trimMargin()

        val job = HdfsShellScriptJob(
            jobId = "hdfs_duplicate_test",
            jobName = "duplicate_processing_test",
            zipFilePath = "hdfs://hdfsCluster/share_ftp/duplicate_test.zip",
            scriptName = "duplicate_test.sh",
            scriptContent = scriptContent,
            scriptSizeBytes = scriptContent.length
        )

        logger.info("f4e9c8a2 | 测试重复处理的变更检测")

        // 第一次处理 - 应该检测到变更
        val firstResult = HdfsShellScriptLineageConverter.convertToLineage(job, mappings, x)
        logger.info(
            "b2a7f5e8 | 第一次处理结果 - 成功: {}, 警告: {}, 错误: {}",
            firstResult.success, firstResult.warnings.size, firstResult.errors.size
        )

        // 第二次处理相同内容 - 应该无变更（如果有处理历史记录）
        val secondResult = HdfsShellScriptLineageConverter.convertToLineage(job,mappings, x)
        logger.info(
            "c6d3f8e1 | 第二次处理结果 - 成功: {}, 警告: {}, 错误: {}",
            secondResult.success, secondResult.warnings.size, secondResult.errors.size
        )

        // 验证两次处理的血缘内容应该相同
        if (firstResult.success && secondResult.success) {
            val firstLineage = firstResult.lineage!!
            val secondLineage = secondResult.lineage!!

            // 比较关键属性
            val sameJobId = firstLineage.jobId == secondLineage.jobId
            val sameSourceTableCount = firstLineage.tableLineage.sourceTables.size ==
                    secondLineage.tableLineage.sourceTables.size
            val sameOriginalSql = firstLineage.originalSql == secondLineage.originalSql

            assertTrue(sameJobId, "两次处理的jobId应该相同")
            assertTrue(sameSourceTableCount, "两次处理的源表数量应该相同")
            assertTrue(sameOriginalSql, "两次处理的原始SQL应该相同")

            logger.info(
                "a8f4e6c9 | 血缘内容验证通过 - jobId相同: {}, 源表数量相同: {}, SQL相同: {}",
                sameJobId, sameSourceTableCount, sameOriginalSql
            )
        }

        // 注意：这个测试主要验证血缘转换的一致性
        // 实际的变更检测需要在集成测试中验证，因为需要数据库支持
        logger.info("e5c2f9a7 | 重复处理测试完成，血缘转换保持一致")
    }
}