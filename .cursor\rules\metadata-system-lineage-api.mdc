---
description:
globs:
alwaysApply: false
---
# 系统间血缘表查询 API 架构规则

## API 设计原则

系统间血缘表查询采用分步骤查询架构，遵循数据为中心编程原则：

1. **验证阶段**: 首先验证元数据系统ID的有效性
2. **映射阶段**: 将元数据系统ID映射到血缘数据源ID
3. **查询阶段**: 使用血缘数据源ID进行实际的血缘关系查询

## 核心实现文件

- **Controller**: [MetadataController.kt](mdc:src/main/kotlin/com/datayes/metadata/MetadataController.kt)
- **Service**: [MetadataService.kt](mdc:src/main/kotlin/com/datayes/metadata/MetadataService.kt)
- **DTO定义**: [MetadataDto.kt](mdc:src/main/kotlin/com/datayes/metadata/MetadataDto.kt)

## API 端点规范

### findLineageTablesBetweenSystems

**路径**: `GET /api/v1/metadata/tables/between-systems`

**参数**:
- `sourceMetadataSystemId`: 源元数据系统ID (必需)
- `targetMetadataSystemId`: 目标元数据系统ID (必需) 
- `tableName`: 表名模糊匹配 (可选)
- `schema`: 模式名模糊匹配 (可选)

**返回**: `List<LineageTableBetweenSystemsDto>` (不分页)

## 数据映射逻辑

元数据系统到血缘数据源的映射基于以下匹配条件：
```sql
LOWER(lineage_datasources.db_type) = LOWER(metadata_data_source.DB_TYPE) AND
lineage_datasources.host = metadata_data_source.DB_URL AND
lineage_datasources.port = COALESCE(metadata_data_source.DB_PORT, 3306) AND
lineage_datasources.database_name = metadata_data_source.DB_NAME
```

## 关键方法

### MetadataService 核心方法

1. **`getLineageDatasourceIdsByMetadataSystemId()`**
   - 功能: 通过元数据系统ID获取匹配的血缘数据源ID列表
   - 返回: `List<Long>` - 血缘数据源ID列表

2. **`queryLineageTablesBetweenDatasources()`**
   - 功能: 基于血缘数据源ID查询表级血缘关系
   - 支持: 表名和模式名过滤
   - 返回: `List<LineageTableBetweenSystemsDto>`

3. **`createSimpleLineageTableBetweenSystemsRowMapper()`**
   - 功能: 创建优化的行映射器，预传入系统信息

## 重要设计决策

1. **无分页设计**: API返回完整结果列表，不支持分页
2. **分步查询**: 避免复杂的单一SQL，提高可维护性
3. **错误处理**: 分阶段验证，提供精确的错误信息
4. **性能优化**: 使用IN子句批量查询，减少数据库往返

## 相关表结构

- **metadata_system_info**: 元数据系统信息
- **metadata_data_source**: 元数据数据源
- **lineage_datasources**: 血缘数据源
- **lineage_tables**: 血缘表信息
- **lineage_relationships**: 血缘关系

## 使用示例

```kotlin
// Controller调用
val result = metadataService.findLineageTablesBetweenSystems(
    sourceMetadataSystemId = 1L,
    targetMetadataSystemId = 2L,
    tableName = "user",
    schema = "dwd"
)
```

## 注意事项

1. 此API为破坏性变更，移除了分页支持
2. 客户端需要更新调用方式
3. 适用于结果集相对较小的场景
4. 大数据量场景可能需要考虑异步查询或重新引入分页
