with  gpayendate as (
    select GrpPolicyNo, EnterAccDate from (
                                              select GrpPolicyNo, EnterAccDate,row_number()over(partition by GrpPolicyNo order by EnterAccDate desc) rn from urp_dws.increment_trans_LJAPolPay where btype='01' and bdtype='01' and gpflag='2'
                                          )temp where temp.rn=1
)
insert into urp_bui_prip.temp_LCGrpProduct
select * from (select
                   row_number() over(partition by trans.busino,lc.GrpPolicyNo,lc.GrpProductNo order by trans.busino,lc.GrpPolicyNo,lc.GrpProductNo) rownum1,
                   trans.busino busino,
                   '000052'          CompanyCode,
                   lc.GrpPolicyNo          GrpPolicyNo,
                   lc.GrpAppNo             grpapplicationformno,
                   lc.GrpPrtNo             GrpPrtNo,
                   lc.GrpProductNo         GrpProductNo,
                   lc.MainGrpProductNo     MainGrpProductNo,
                   lc.MainProductFlag      MainProductFlag,
                   lc.ProductCode          ProductCode,
                   lc.MainProductCode      MainProductCode,
                   substr(lc.ManageCom,1,4)           ManageCom,
                   lc.EffDate              EffDate,
                   (case when lc.Status in('0','1') and length(nvl(lc.PayBeginDate,''))=0 then  gd.EnterAccDate else lc.PayBeginDate end ) PayBeginDate,
                   date_add(lc.FinalPayDate,-1) FinalPayDate,
                   date_add(lc.CurPaidToDate,-1) CurPaidToDate,
                   date_add(lc.InvalidDate,-1) InvalidDate,
                   cast(cast(lc.PayMode as int) as string) PayMode,
                   (case when cast(cast(lc.PayMode as int) as string) = '-1' then '' else lc.PayTermType end) PayTermType,
                   (case when cast(cast(lc.PayMode as int) as string) = '-1' then -1 else cast(lc.PayTerm as int) end) PayTerm,
                   lc.InsurancePeriodFlag  InsurancePeriodFlag,
                   lc.InsurancePeriod      InsurancePeriod,
                   lc.Copies               Copies,
                   nvl(lc.Premium,0)              Premium,
                   cast(nvl(idt.cpolvalue,0) as decimal(16,2)) AccumPremium,
                   lc.BasicSumInsured      BasicSumInsured,
                   '0'     SpecificBusiness,
                   lc.SpecificBusinessCode SpecificBusinessCode,
                   lc.UWDate               UWDate,
                   lc.UWConclusion         UWConclusion,
                   lc.Status               Status,
                   trans.pushdate             pushdate
               from (select * from urp_dws.full_lcgrppol where nvl(status,'') <> '0') lc
                        inner join urp_bui_prip.temp_lcpoltransaction trans on trans.GrpPolicyNo = lc.GrpPolicyNo
                        left join  gpayendate gd
                                   on gd.GrpPolicyNo=trans.GrpPolicyNo
                        left join  (select grpproductno,sum(cpolvalue) cpolvalue from  urp_dws.view_polidt_000001 group by grpproductno) idt on idt.grpproductno = lc.grpproductno
                        inner join urp_bui_prip.busstype busstype on trans.busstype = busstype.busstype_id and busstype.tablename = 'LCGRPPRODUCT'
               where length(nvl(trans.GrpPolicyNo,''))>0) b where b.rownum1 = 1