package com.datayes.hdfs

/**
 * HDFS数据源配置类 (HDFS DataSource Configuration)
 *
 * 新版本：基于数据库的数据源配置管理
 * 替换了原有的硬编码配置方式，改为从数据库动态获取数据源映射关系
 */
class HdfsDataSourceConfig(val values: List<HdfsDatasourceMapping>) {

    fun getDatasourceByDatabaseName(databaseName: String): String? {

        for (mapping in values) {
            if (mapping.sourceJdbcUrl.endsWith("/" + databaseName.trim())) {
                return mapping.sourceJdbcUrl
            }
            if (mapping.targetJdbcUrl.endsWith("/" + databaseName.trim())) {
                return mapping.targetJdbcUrl
            }
        }

        return null
    }
}