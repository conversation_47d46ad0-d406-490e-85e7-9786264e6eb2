package com.datayes.hdfs

import com.datayes.lineage.*
import com.datayes.shell.ShellScriptParser
import com.datayes.sql.SqlParser
import com.datayes.sql.SqlParsingException
import com.datayes.sql.TableReference as SqlTableReference
import com.datayes.sql.ColumnReference as SqlColumnReference
import com.datayes.sql.ColumnMapping
import org.slf4j.LoggerFactory

/**
 * HDFS Shell Script 血缘转换器
 *
 * 将HDFS中的shell脚本转换为血缘信息，类似于DataExchangeJobLineageConverter
 * 主要功能：
 * 1. 从shell脚本中提取SQL语句
 * 2. 解析SQL获取表和列依赖关系
 * 3. 构建DataLineage对象
 */
object HdfsShellScriptLineageConverter {

    private val logger = LoggerFactory.getLogger(HdfsShellScriptLineageConverter::class.java)

    /**
     * 将HDFS shell脚本作业转换为数据血缘信息
     *
     * @param job HDFS shell脚本作业
     * @param hdfsDatasourceMappings HDFS数据源映射列表
     * @param createDatabaseInfoFromTableRef 创建数据库信息的函数，通常从HdfsShellScriptService传入
     * @return 血缘构建结果
     */
    fun convertToLineage(
        job: HdfsShellScriptJob, 
        hdfsDatasourceMappings: List<HdfsDatasourceMapping>,
        createDatabaseInfoFromTableRef: (SqlTableReference, MutableList<String>) -> DatabaseInfo
    ): LineageResult {
        val warnings = mutableListOf<String>()
        val errors = mutableListOf<String>()

        return try {
            logger.info("a5e3f8c2 | 开始转换shell脚本血缘: {}", job.jobId)

            // 1. 从shell脚本中提取SQL语句
            val extractedSqls = extractSqlFromShellScript(job.scriptContent, warnings)

            if (extractedSqls.isEmpty()) {
                errors.add("未从shell脚本中提取到任何SQL语句")
                return createEmptyLineageResult(warnings, errors)
            }

            logger.info("b7f4e9c1 | 从脚本{}中提取到{}条SQL语句", job.scriptName, extractedSqls.size)

            // 2. 解析每个SQL语句获取血缘信息
            val allSourceTables = mutableSetOf<SqlTableReference>()
            val allTargetTables = mutableSetOf<SqlTableReference>()
            val allColumnReferences = mutableListOf<SqlColumnReference>()
            val consolidatedSql = extractedSqls.joinToString("\n-- Next SQL --\n")

            for ((index, sql) in extractedSqls.withIndex()) {
                try {
                    val parseResult = parseSqlStatement(sql)
                    if (parseResult.isDataModification) {
                        // 数据修改语句：分别处理源表和目标表
                        allSourceTables.addAll(parseResult.sourceTables)
                        parseResult.targetTable?.let { allTargetTables.add(it) }
                        // 使用columnMappings来获取正确的源列信息，而不是被污染的sourceColumns
                        allColumnReferences.addAll(parseResult.columnMappings.map { it.sourceColumn })
                        logger.info(
                            "c2d6f9e4 | SQL {}(INSERT)解析成功，发现{}个源表引用，1个目标表",
                            index + 1, parseResult.sourceTables.size
                        )
                    } else {
                        // 查询语句：所有表都是源表
                        allSourceTables.addAll(parseResult.tables)
                        allColumnReferences.addAll(parseResult.columns)
                        logger.info(
                            "c2d6f9e4 | SQL {}(SELECT)解析成功，发现{}个表引用",
                            index + 1, parseResult.tables.size
                        )
                    }
                } catch (e: SqlParsingException) {
                    warnings.add("SQL解析失败 (SQL ${index + 1}): ${e.message}")
                    logger.warn("d8e3f5c7 | SQL解析失败: {}", e.message)
                    logger.warn("86258197 | SQL解析失败: {}", sql)
                }
            }

            if (allSourceTables.isEmpty() && allTargetTables.isEmpty()) {
                errors.add("所有SQL解析均失败，无法提取表血缘信息")
                return LineageResult(null, warnings, errors, false)
            }

            // 3. 构建血缘信息
            val lineageResult = buildLineageFromSeparatedTables(
                job = job,
                sourceTables = allSourceTables.toList(),
                targetTables = allTargetTables.toList(),
                columnReferences = allColumnReferences.toList(),
                consolidatedSql = consolidatedSql,
                warnings = warnings,
                errors = errors,
                hdfsDatasourceMappings = hdfsDatasourceMappings,
                createDatabaseInfoFromTableRef = createDatabaseInfoFromTableRef
            )

            logger.info(
                "e9f2c8a5 | 成功构建shell脚本血缘: {} ({}个源表)",
                job.jobId, lineageResult.lineage?.tableLineage?.sourceTables?.size ?: 0
            )

            lineageResult

        } catch (e: Exception) {
            logger.error("f3a8e6d2 | 转换shell脚本血缘时发生异常: {}", job.jobId, e)
            errors.add("转换过程中发生异常: ${e.message}")
            LineageResult(null, warnings, errors, false)
        }
    }

    /**
     * 从shell脚本内容中提取SQL语句
     */
    fun extractSqlFromShellScript(scriptContent: String, warnings: MutableList<String>): List<String> {
        return try {
            // 使用现有的ShellScriptParser提取SQL
            val rawSqls = ShellScriptParser.parseShellScriptForSql(scriptContent)
            logger.info("4c9e7f3a | 原始提取到{}条SQL语句", rawSqls.size)

            // 预处理和过滤SQL语句
            val processedSqls = rawSqls.mapNotNull { sql ->
                processRawSqlStatement(sql, warnings)
            }

            logger.debug("6e8f2a9c | 处理后剩余{}条有效SQL语句", processedSqls.size)
            processedSqls

        } catch (e: Exception) {
            warnings.add("shell脚本解析失败: ${e.message}")
            logger.warn("5d2a8f6e | shell脚本解析失败", e)
            emptyList()
        }
    }

    /**
     * 处理原始SQL语句，应用过滤和提取规则
     *
     * @param rawSql 原始SQL语句
     * @param warnings 警告信息收集器
     * @return 处理后的SQL语句，如果应该过滤掉则返回null
     */
    fun processRawSqlStatement(rawSql: String, warnings: MutableList<String>): String? {
        val trimmedSql = rawSql.trim()

        if (trimmedSql.isEmpty()) {
            return null
        }

        // 1. 过滤掉TRUNCATE语句
        if (trimmedSql.lowercase().startsWith("truncate")) {
            logger.debug("8f2e9c4a | 过滤掉TRUNCATE语句: {}", trimmedSql.take(50))
            return null
        }

        // 2. 清理SQL开头的Hive SET语句
        val cleanedSql = removeHiveSetStatements(trimmedSql, warnings)
        if (cleanedSql.isEmpty()) {
            logger.debug("4b8d2f1e | SQL只包含Hive SET语句，无实际数据操作: {}", trimmedSql.take(50))
            warnings.add("SQL只包含Hive配置语句，无实际数据操作: ${trimmedSql.take(50)}...")
            return null
        }

        // 3. 处理以"reload function"开头后跟INSERT的复合语句
        val normalizedSql = cleanedSql.replace("\n", " ").replace("\r", "")
        if (normalizedSql.lowercase().startsWith("reload function")) {
            logger.debug("3a7f5c8e | 检测到reload function复合语句")

            // 查找INSERT语句的开始位置
            val insertIndex = normalizedSql.lowercase().indexOf("insert")
            if (insertIndex > 0) {
                val insertStatement = normalizedSql.substring(insertIndex).trim()
                logger.debug("9c4e8f2a | 从reload function复合语句中提取INSERT: {}", insertStatement.take(100))
                warnings.add("从reload function复合语句中提取INSERT语句")

                // 验证提取的INSERT语句是否有效
                if (isValidSqlStatement(insertStatement, warnings)) {
                    return insertStatement
                } else {
                    warnings.add("提取的INSERT语句无效: ${insertStatement.take(50)}...")
                    return null
                }
            } else {
                logger.debug("5d6a9f4c | reload function语句中未找到INSERT部分")
                warnings.add("reload function语句中未找到INSERT部分: ${trimmedSql.take(50)}...")
                return null
            }
        }

        // 4. 对于其他SQL语句，使用原有的验证逻辑
        if (isValidSqlStatement(cleanedSql, warnings)) {
            return cleanedSql
        } else {
            return null
        }
    }

    /**
     * 移除SQL开头的Hive SET语句 (Remove Hive SET statements from the beginning of SQL)
     *
     * @param sql 包含SET语句的SQL
     * @param warnings 警告信息收集器
     * @return 清理后的SQL，不包含SET语句
     */
    private fun removeHiveSetStatements(sql: String, warnings: MutableList<String>): String {
        val lines = sql.split(';')
        val cleanedLines = mutableListOf<String>()
        var setStatementsCount = 0

        for (line in lines) {
            val trimmedLine = line.trim()
            if (trimmedLine.isEmpty()) {
                continue
            }

            if (trimmedLine.lowercase().startsWith("set ")) {
                setStatementsCount++
                logger.debug("7f3a9e2d | 过滤掉Hive SET语句: {}", trimmedLine.take(50))
            } else {
                // 遇到非SET语句，保留剩余的所有内容
                cleanedLines.add(trimmedLine)
                // 如果还有剩余的行，也要加上
                val remainingIndex = lines.indexOf(line)
                if (remainingIndex < lines.size - 1) {
                    cleanedLines.addAll(lines.subList(remainingIndex + 1, lines.size).map { it.trim() }
                        .filter { it.isNotEmpty() })
                }
                break
            }
        }

        if (setStatementsCount > 0) {
            warnings.add("过滤掉${setStatementsCount}个Hive配置语句")
        }

        return cleanedLines.joinToString("; ").trim()
    }

    /**
     * 判断是否为有效的SQL语句
     * 排除Hive命令和其他非SQL语句
     */
    private fun isValidSqlStatement(sql: String, warnings: MutableList<String>): Boolean {
        val trimmedSql = sql.trim().lowercase()

        // 空语句或过短语句
        if (trimmedSql.length < 10) {
            logger.debug("a3f7e2b8 | 跳过过短语句: {}", sql.take(50))
            return false
        }

        // Hive命令模式（非SQL）
        val hiveCommandPatterns = listOf(
            // load data inpath 命令
            Regex("^load\\s+data\\s+inpath\\s+", RegexOption.IGNORE_CASE),
            // add jar/file 命令
            Regex("^add\\s+(jar|file)\\s+", RegexOption.IGNORE_CASE),
            // set 命令
            Regex("^set\\s+\\w+\\s*=", RegexOption.IGNORE_CASE),
            // describe 命令
            Regex("^(desc|describe)\\s+", RegexOption.IGNORE_CASE),
            // show 命令
            Regex("^show\\s+", RegexOption.IGNORE_CASE),
            // explain 命令
            Regex("^explain\\s+", RegexOption.IGNORE_CASE),
            // use database 命令
            Regex("^use\\s+\\w+\\s*;?\\s*$", RegexOption.IGNORE_CASE),
            // msck repair 命令
            Regex("^msck\\s+repair\\s+", RegexOption.IGNORE_CASE),
            // analyze table 命令
            Regex("^analyze\\s+table\\s+", RegexOption.IGNORE_CASE)
        )

        // 检查是否匹配Hive命令模式
        for (pattern in hiveCommandPatterns) {
            if (pattern.containsMatchIn(trimmedSql)) {
                logger.debug("c5d8f9e2 | 跳过Hive命令: {}", sql.take(100))
                warnings.add("跳过Hive命令（非SQL）: ${sql.take(50)}...")
                return false
            }
        }

        // 有效的SQL语句模式 (TRUNCATE已在预处理阶段过滤掉)
        val validSqlPatterns = listOf(
            Regex("^select\\s+", RegexOption.IGNORE_CASE),
            Regex("^insert\\s+", RegexOption.IGNORE_CASE),
            Regex("^update\\s+", RegexOption.IGNORE_CASE),
            Regex("^delete\\s+", RegexOption.IGNORE_CASE),
            Regex("^create\\s+(table|view|database|schema)\\s+", RegexOption.IGNORE_CASE),
            Regex("^drop\\s+(table|view|database|schema)\\s+", RegexOption.IGNORE_CASE),
            Regex("^alter\\s+(table|view)\\s+", RegexOption.IGNORE_CASE),
            Regex("^with\\s+", RegexOption.IGNORE_CASE) // CTE (Common Table Expressions)
        )

        // 检查是否匹配有效SQL模式
        val isValidSql = validSqlPatterns.any { pattern ->
            pattern.containsMatchIn(trimmedSql)
        }

        if (!isValidSql) {
            logger.debug("f2a9e6c4 | 跳过无法识别的语句类型: {}", sql.take(100))
            warnings.add("跳过无法识别的语句类型: ${sql.take(50)}...")
        }

        return isValidSql
    }

    /**
     * 从已分离的源表和目标表构建血缘信息
     * 
     * @param createDatabaseInfoFromTableRef 创建数据库信息的函数，用于将SQL表引用转换为DatabaseInfo对象
     */
    private fun buildLineageFromSeparatedTables(
        job: HdfsShellScriptJob,
        sourceTables: List<SqlTableReference>,
        targetTables: List<SqlTableReference>,
        columnReferences: List<SqlColumnReference>,
        consolidatedSql: String,
        warnings: MutableList<String>,
        errors: MutableList<String>,
        hdfsDatasourceMappings: List<HdfsDatasourceMapping>,
        createDatabaseInfoFromTableRef: (SqlTableReference, MutableList<String>) -> DatabaseInfo
    ): LineageResult {

        if (sourceTables.isEmpty()) {
            warnings.add("未识别到源表信息")
        }

        // 使用传入的函数创建数据库信息
        fun createDatabaseInfoFromTableRefInternal(tableRef: SqlTableReference): DatabaseInfo {
            return createDatabaseInfoFromTableRef(tableRef, warnings)
        }

        // 构建表信息
        val sourceTableInfos = sourceTables.map { tableRef ->
            val dbInfo = createDatabaseInfoFromTableRefInternal(tableRef)
            TableInfo(
                schema = determineSchemaForDatabaseType(dbInfo.dbType, tableRef.schemaOrDatabase),
                tableName = tableRef.name,
                database = dbInfo
            )
        }

        // 如果没有明确的目标表，创建一个基于作业名称的虚拟目标表
        val targetTable = if (targetTables.isNotEmpty()) {
            val targetRef = targetTables.first()
            val dbInfo = createDatabaseInfoFromTableRefInternal(targetRef)
            TableInfo(
                schema = determineSchemaForDatabaseType(dbInfo.dbType, targetRef.schemaOrDatabase),
                tableName = targetRef.name,
                database = dbInfo
            )
        } else {
            // 创建虚拟目标表
            warnings.add("未识别到目标表，创建基于脚本名称的虚拟目标表")
            TableInfo(
                schema = "hdfs_scripts",
                tableName = job.jobName,
                database = createDefaultDatabase(job.jobName)
            )
        }

        // 构建表级血缘
        val tableLineage = TableLineage(
            sourceTables = sourceTableInfos,
            targetTable = targetTable,
            lineageType = determineLineageTypeFromSql(consolidatedSql)
        )

        // 构建列级血缘（简化处理）
        val columnLineages = buildColumnLineages(
            columnReferences = columnReferences,
            sourceTableInfos = sourceTableInfos,
            targetTable = targetTable,
            warnings = warnings
        )

        // 构建完整血缘信息
        val dataLineage = DataLineage(
            jobId = job.jobId,
            jobName = "HDFS Shell Script: ${job.jobName}",
            tableLineage = tableLineage,
            columnLineages = columnLineages,
            sourceDatabase = sourceTableInfos.firstOrNull()?.database ?: createDefaultDatabase(job.jobName),
            targetDatabase = targetTable.database,
            originalSql = consolidatedSql
        )

        return LineageResult(dataLineage, warnings, errors, true)
    }

    /**
     * 构建列级血缘关系
     */
    private fun buildColumnLineages(
        columnReferences: List<SqlColumnReference>,
        sourceTableInfos: List<TableInfo>,
        targetTable: TableInfo,
        warnings: MutableList<String>
    ): List<ColumnLineage> {

        // 简化处理：为每个列引用创建基本的血缘关系
        return columnReferences.mapIndexedNotNull { index, columnRef ->
            try {
                // 检查是否为复杂表达式 (Complex Expression)
                val complexExpressionInfo = analyzeColumnExpression(columnRef, index)

                if (complexExpressionInfo.isComplex) {
                    warnings.add("检测到复杂表达式 (${complexExpressionInfo.expressionType})，使用生成的列名: ${complexExpressionInfo.suggestedColumnName}")

                    // 使用第一个源表作为虚拟源表
                    val sourceTable = sourceTableInfos.firstOrNull()
                    if (sourceTable == null) {
                        warnings.add("无法为复杂表达式 ${complexExpressionInfo.suggestedColumnName} 找到源表")
                        return@mapIndexedNotNull null
                    }

                    val sourceColumn = ColumnInfo(
                        columnName = complexExpressionInfo.suggestedColumnName,
                        dataType = "unknown",
                        comment = "Computed from ${complexExpressionInfo.expressionType}: ${columnRef.name.take(200)}",
                        table = sourceTable
                    )

                    val targetColumn = ColumnInfo(
                        columnName = columnRef.alias ?: complexExpressionInfo.suggestedColumnName,
                        dataType = "unknown",
                        comment = null,
                        table = targetTable
                    )

                    ColumnLineage(
                        sourceColumn = sourceColumn,
                        targetColumn = targetColumn,
                        transformation = DataTransformation(
                            transformationType = complexExpressionInfo.transformationType,
                            description = complexExpressionInfo.description,
                            expression = columnRef.name.take(500) // 限制表达式长度
                        ),
                        columnIndex = index
                    )

                    null // ignore complex for now todo wujie

                } else {
                    // 处理简单列引用
                    val sourceTable = findSourceTableForColumn(columnRef, sourceTableInfos, warnings)

                    if (sourceTable == null) {
                        warnings.add("无法为列 ${columnRef.name} 找到对应的源表")
                        return@mapIndexedNotNull null
                    }

                    val sourceColumn = ColumnInfo(
                        columnName = columnRef.name,
                        dataType = "unknown", // SQL解析通常不包含类型信息
                        comment = null,
                        table = sourceTable
                    )

                    val targetColumn = ColumnInfo(
                        columnName = columnRef.alias ?: columnRef.name, // 优先使用别名，如total_amount
                        dataType = "unknown",
                        comment = null,
                        table = targetTable
                    )

                    ColumnLineage(
                        sourceColumn = sourceColumn,
                        targetColumn = targetColumn,
                        transformation = null, // 无转换的简单映射
                        columnIndex = index
                    )
                }

            } catch (e: Exception) {
                warnings.add("构建列血缘失败: ${columnRef.name.take(100)}, 错误: ${e.message}")
                null
            }
        }
    }

    /**
     * 从SQL确定血缘类型
     */
    private fun determineLineageTypeFromSql(sql: String): LineageType {
        val normalizedSql = sql.lowercase()

        return when {
            normalizedSql.contains("join") -> LineageType.JOIN
            normalizedSql.contains("group by") ||
                    normalizedSql.contains("count(") ||
                    normalizedSql.contains("sum(") ||
                    normalizedSql.contains("avg(") -> LineageType.AGGREGATION

            normalizedSql.contains("where") -> LineageType.FILTER
            normalizedSql.contains("case when") ||
                    normalizedSql.contains("substring") ||
                    normalizedSql.contains("concat") -> LineageType.COMPLEX_TRANSFORMATION

            normalizedSql.contains("select") &&
                    normalizedSql.contains("from") -> LineageType.SQL_QUERY

            normalizedSql.contains("insert") ||
                    normalizedSql.contains("create") -> LineageType.DATA_MOVEMENT

            else -> LineageType.SCRIPT_PROCESSING
        }
    }

    /**
     * 从JDBC URL解析数据库信息
     */
    private fun parseDatabaseInfoFromUrl(jdbcUrl: String, databaseName: String): DatabaseInfo {
        return when {
            jdbcUrl.contains("jdbc:mysql://") -> {
                // 格式: *******************************
                val urlParts = jdbcUrl.substringAfter("jdbc:mysql://").split("/")
                val hostPort = urlParts[0].split(":")
                val host = hostPort[0]
                val port = hostPort.getOrNull(1)?.toIntOrNull() ?: 3306

                DatabaseInfo(
                    dbType = "mysql",
                    host = host,
                    port = port,
                    databaseName = databaseName,
                    originalConnectionString = jdbcUrl
                )
            }

            jdbcUrl.contains("jdbc:oracle:thin:") -> {
                // 格式: **************************************
                val urlParts = jdbcUrl.substringAfter("jdbc:oracle:thin:@//").split("/")
                val hostPort = urlParts[0].split(":")
                val host = hostPort[0]
                val port = hostPort.getOrNull(1)?.toIntOrNull() ?: 1521

                DatabaseInfo(
                    dbType = "oracle",
                    host = host,
                    port = port,
                    databaseName = databaseName,
                    originalConnectionString = jdbcUrl
                )
            }

            jdbcUrl.contains("jdbc:hive2://") -> {
                // 格式: ************************,host2:port2/database
                val urlParts = jdbcUrl.substringAfter("jdbc:hive2://").split("/")
                val hosts = urlParts[0].split(",")
                val firstHost = hosts[0].split(":")
                val host = firstHost[0]
                val port = firstHost.getOrNull(1)?.toIntOrNull() ?: 10000

                DatabaseInfo(
                    dbType = "hive",
                    host = host,
                    port = port,
                    databaseName = databaseName,
                    originalConnectionString = jdbcUrl
                )
            }

            else -> {
                logger.warn("3a8f7e2d | 无法解析JDBC URL格式: $jdbcUrl，使用默认配置")
                createDefaultDatabase(jdbcUrl)
            }
        }
    }

    /**
     * 创建默认数据库信息
     */
    private fun createDefaultDatabase(key: String): DatabaseInfo {
        // return DatabaseInfo(
        //     dbType = "hive",
        //     host = "hdfs-cluster",
        //     port = 10000,
        //     databaseName = "default",
        //     originalConnectionString = "***************************************"
        // )
        throw IllegalStateException("Cannot create default database for $key")
    }

    /**
     * 解析SQL语句，自动检测是SELECT查询还是数据修改语句
     * 支持子查询作为临时表的处理，正确解析嵌套别名
     *
     * @param sql SQL语句
     * @return 统一的解析结果，包含表和列信息
     */
    fun parseSqlStatement(sql: String): ParsedSqlResult {
        val trimmedSql = sql.trim()

        // Step 1: 基础解析
        val basicResult = when {
            // 检测数据修改语句 (INSERT, UPDATE, DELETE)
            trimmedSql.startsWith("INSERT", ignoreCase = true) ||
                    trimmedSql.startsWith("UPDATE", ignoreCase = true) ||
                    trimmedSql.startsWith("DELETE", ignoreCase = true) -> {
                logger.debug("5b8c2f9a | 检测到数据修改语句，使用parseDataModification解析")
                val result = SqlParser.parseDataModification(sql)
                ParsedSqlResult(
                    tables = result.sourceTables + result.targetTable,
                    columns = result.sourceColumns,
                    isDataModification = true,
                    sourceTables = result.sourceTables,
                    targetTable = result.targetTable,
                    sourceColumns = result.sourceColumns,
                    columnMappings = result.columnMappings
                )
            }

            // 检测CTE语句（Common Table Expression），这些语句可能包含INSERT/UPDATE/DELETE
            trimmedSql.startsWith("WITH", ignoreCase = true) -> {
                logger.debug("2f9a4c8b | 检测到CTE语句，分析是否包含数据修改操作")

                // 检查CTE语句中是否包含INSERT/UPDATE/DELETE操作
                val sqlLowerCase = trimmedSql.lowercase()
                val isDataModification = sqlLowerCase.contains("insert into") ||
                        sqlLowerCase.contains("update ") ||
                        sqlLowerCase.contains("delete from")

                if (isDataModification) {
                    logger.debug("8e3d7f2a | CTE语句包含数据修改操作，使用parseDataModification解析")
                    val result = SqlParser.parseDataModification(sql)
                    ParsedSqlResult(
                        tables = result.sourceTables + result.targetTable,
                        columns = result.sourceColumns,
                        isDataModification = true,
                        sourceTables = result.sourceTables,
                        targetTable = result.targetTable,
                        sourceColumns = result.sourceColumns,
                        columnMappings = result.columnMappings
                    )
                } else {
                    logger.debug("4c7a9f3d | CTE语句仅包含查询操作，使用parse解析")
                    val result = SqlParser.parse(sql)
                    ParsedSqlResult(
                        tables = result.tables,
                        columns = result.columns,
                        isDataModification = false
                    )
                }
            }

            // 默认作为SELECT查询处理
            else -> {
                logger.debug("7e9d3f1c | 检测到查询语句，使用parse解析")
                val result = SqlParser.parse(sql)
                ParsedSqlResult(
                    tables = result.tables,
                    columns = result.columns,
                    isDataModification = false
                )
            }
        }

        // Step 2: 检测和处理子查询（仅在有明确子查询时进行）
        logger.debug("1f8e7a92 | 开始检测子查询，SQL长度: ${sql.length}")

        // 安全检查：只有在基础解析成功且有列映射时才进行子查询解析
        if (basicResult.columnMappings.isEmpty()) {
            logger.debug("5a9c3f84 | 基础解析没有列映射，跳过子查询解析")
            return basicResult
        }

        val subqueries = extractSubqueries(sql)
        if (subqueries.isEmpty()) {
            logger.debug("a3f8e7d2 | 未检测到子查询，返回基础解析结果")
            return basicResult
        }

        logger.debug("c9d4f8a1 | 检测到 ${subqueries.size} 个子查询，开始处理子查询解析")

        // Step 3: 构建子查询解析映射
        val subqueryResolutionMap = buildSubqueryResolutionMap(subqueries)

        // Step 4: 通过子查询解析列映射
        val enhancedColumnMappings = try {
            resolveColumnMappingsThroughSubqueries(
                basicResult.columnMappings,
                subqueryResolutionMap
            )
        } catch (e: Exception) {
            logger.warn("7f2e8a93 | 子查询解析失败，使用原始结果: ${e.message}")
            return basicResult
        }

        // Step 5: 安全检查 - 只有在子查询解析确实改善结果时才使用
        val shouldUseEnhancedMappings = validateEnhancedMappings(
            original = basicResult.columnMappings,
            enhanced = enhancedColumnMappings,
            subqueryMap = subqueryResolutionMap
        )

        if (shouldUseEnhancedMappings) {
            logger.debug("8e4f9c72 | 使用增强的子查询解析结果")
            return basicResult.copy(
                columnMappings = enhancedColumnMappings
            )
        } else {
            logger.debug("3a7d5f81 | 子查询解析未改善结果，使用原始解析结果")
            return basicResult
        }
    }

    /**
     * 统一的SQL解析结果
     */
    data class ParsedSqlResult(
        val tables: List<SqlTableReference>,
        val columns: List<SqlColumnReference>,
        val isDataModification: Boolean = false,
        val sourceTables: List<SqlTableReference> = emptyList(),
        val targetTable: SqlTableReference? = null,
        val sourceColumns: List<SqlColumnReference> = emptyList(),
        val columnMappings: List<ColumnMapping> = emptyList()
    )

    /**
     * 子查询信息
     */
    data class SubqueryInfo(
        val alias: String,                          // 子查询别名
        val sql: String,                           // 子查询SQL
        val outputColumns: List<SubqueryColumn>    // 子查询输出列
    )

    /**
     * 子查询输出列
     */
    data class SubqueryColumn(
        val outputName: String,                    // 输出列名（可能是别名）
        val sourceColumn: SqlColumnReference       // 原始源列引用
    )

    /**
     * 从SQL中提取子查询信息
     */
    fun extractSubqueries(sql: String): List<SubqueryInfo> {
        val subqueries = mutableListOf<SubqueryInfo>()

        try {
            logger.info("3c8f4e92 | 开始提取子查询，原始SQL长度: ${sql.length}")

            val matchList = findSubqueryMatches(sql)

            logger.debug("6e3a9f74 | 找到 ${matchList.size} 个匹配项")

            for ((index, match) in matchList.withIndex()) {
                logger.debug("9b4f7e82 | 处理匹配项 $index:")
                logger.debug("1a5c8f93 |   组1 (子查询): '${match.groupValues[1].take(50)}...'")
                logger.debug("3f7e9a85 |   组2 (别名): '${match.groupValues[2]}'")

                val rawSubquerySql = match.groupValues[1].trim()
                val alias = match.groupValues[2].trim()

                if (rawSubquerySql.isNotEmpty() && alias.isNotEmpty()) {
                    // 尝试清理子查询SQL
                    val subquerySql = cleanSubquerySql(rawSubquerySql)

                    logger.debug("f4e8a9c3 | 发现子查询: alias='$alias'")

                    // 解析子查询以获取其输出列
                    val outputColumns = parseSubqueryOutputColumns(subquerySql)

                    if (outputColumns.isNotEmpty()) {
                        subqueries.add(
                            SubqueryInfo(
                                alias = alias,
                                sql = subquerySql,
                                outputColumns = outputColumns
                            )
                        )
                        logger.debug("5e8a2f97 | 成功创建子查询信息: $alias, 输出列数: ${outputColumns.size}")
                    } else {
                        logger.warn("8c4f2d91 | 子查询解析后没有输出列: $alias")
                    }
                } else {
                    logger.warn("4a9f3e82 | 匹配项无效: rawSql='${rawSubquerySql.take(50)}', alias='$alias'")
                }
            }

        } catch (e: Exception) {
            logger.warn("6d2f8a94 | 子查询提取失败: ${e.message}", e)
        }

        logger.debug("7c4e9f83 | 子查询提取完成，总数: ${subqueries.size}")
        return subqueries
    }

    /**
     * 从SQL中查找子查询匹配项
     */
    internal fun findSubqueryMatches(sql: String): List<MatchResult> {
        // 预处理：移除多余的空白字符
        val normalizedSql = sql.replace(Regex("\\s+"), " ").trim()
        logger.info("4f9e2a83 | 标准化后SQL长度: ${normalizedSql.length}")

        // 使用手动括号平衡来正确提取子查询
        return findSubqueriesWithParenthesesBalancing(normalizedSql)
    }

    /**
     * 简化的MatchResult实现
     */
    private class SimpleMatchResult(
        override val range: IntRange,
        override val value: String,
        override val groupValues: List<String>
    ) : MatchResult {
        override val groups: MatchGroupCollection = object : MatchGroupCollection {
            override val size: Int = groupValues.size
            override fun get(index: Int): MatchGroup? = null
            override fun contains(element: MatchGroup?): Boolean = false
            override fun containsAll(elements: Collection<MatchGroup?>): Boolean = false
            override fun isEmpty(): Boolean = false
            override fun iterator(): Iterator<MatchGroup?> = emptyList<MatchGroup?>().iterator()
        }

        override fun next(): MatchResult? = null
    }

    /**
     * 使用括号平衡算法查找子查询，正确处理嵌套括号
     */
    private fun findSubqueriesWithParenthesesBalancing(sql: String): List<MatchResult> {
        val matches = mutableListOf<MatchResult>()

        // 查找 "from (" 模式的开始位置
        val fromPattern = Regex("""\bfrom\s*\(\s*""", RegexOption.IGNORE_CASE)
        val fromMatches = fromPattern.findAll(sql)

        for (fromMatch in fromMatches) {
            val fromEndPos = fromMatch.range.last + 1

            // 检查是否是 SELECT 子查询
            val remainingSQL = sql.substring(fromEndPos).trimStart()
            if (!remainingSQL.startsWith("select", ignoreCase = true)) {
                continue
            }

            // 使用括号平衡找到匹配的结束括号
            val subqueryEndPos = findMatchingClosingParenthesis(sql, fromMatch.range.last)
            if (subqueryEndPos == -1) {
                logger.warn("8e2f9c74 | 无法找到匹配的结束括号，位置: ${fromMatch.range.last}")
                continue
            }

            // 提取子查询 SQL (去掉最外层的括号)
            val subqueryStartPos = fromEndPos
            val subquerySql = sql.substring(subqueryStartPos, subqueryEndPos).trim()

            // 查找子查询别名
            val aliasStartPos = subqueryEndPos + 1
            val aliasMatch = findSubqueryAlias(sql, aliasStartPos)
            if (aliasMatch == null) {
                logger.warn("5a7c9e24 | 无法找到子查询别名，位置: $aliasStartPos")
                continue
            }

            // 创建匹配结果
            val fullMatchStart = fromMatch.range.first
            val fullMatchEnd = aliasMatch.range.last
            val fullMatch = sql.substring(fullMatchStart, fullMatchEnd + 1)

            // 创建简化的MatchResult，只实现必要的方法
            val matchResult = SimpleMatchResult(
                range = fullMatchStart..fullMatchEnd,
                value = fullMatch,
                groupValues = listOf(fullMatch, subquerySql, aliasMatch.value)
            )

            matches.add(matchResult)
            logger.debug("f3d8a6c1 | 成功提取子查询: alias='${aliasMatch.value}', SQL长度=${subquerySql.length}")
        }

        logger.debug("2d8f5c91 | 使用括号平衡算法找到 ${matches.size} 个子查询")
        return matches
    }

    /**
     * 查找匹配的结束括号位置
     */
    private fun findMatchingClosingParenthesis(sql: String, openParenPos: Int): Int {
        var depth = 1
        var pos = openParenPos + 1
        var inSingleQuote = false
        var inDoubleQuote = false

        while (pos < sql.length && depth > 0) {
            val char = sql[pos]
            val prevChar = if (pos > 0) sql[pos - 1] else ' '

            when {
                // 处理转义字符
                prevChar == '\\' -> {
                    // 跳过转义字符
                }

                // 处理单引号字符串
                char == '\'' && !inDoubleQuote -> {
                    inSingleQuote = !inSingleQuote
                }

                // 处理双引号字符串  
                char == '"' && !inSingleQuote -> {
                    inDoubleQuote = !inDoubleQuote
                }

                // 只在字符串外才处理括号
                !inSingleQuote && !inDoubleQuote -> {
                    when (char) {
                        '(' -> depth++
                        ')' -> depth--
                    }
                }
            }

            if (depth == 0) {
                return pos
            }

            pos++
        }

        return -1 // 未找到匹配的括号
    }

    /**
     * 查找子查询别名
     */
    private fun findSubqueryAlias(sql: String, startPos: Int): SimpleAliasMatch? {
        if (startPos >= sql.length) return null

        val remainingSQL = sql.substring(startPos)
        val aliasPattern = Regex("""\s*([a-zA-Z_]\w*)(?=\s|${'$'}|,|\)|;)""")

        return aliasPattern.find(remainingSQL)?.let { match ->
            SimpleAliasMatch(
                range = (startPos + match.range.first)..(startPos + match.range.last),
                value = match.groupValues[1]
            )
        }
    }

    /**
     * 简化的别名匹配结果
     */
    private data class SimpleAliasMatch(
        val range: IntRange,
        val value: String
    )

    /**
     * 清理子查询SQL，移除多余内容
     */
    private fun cleanSubquerySql(rawSql: String): String {
        var cleaned = rawSql.trim()

        // 移除末尾的多余内容（如果有的话）
        // 例如: "SELECT ... FROM table WHERE ..." 后面可能跟着外层查询的部分

        // 简单策略：确保以合理的SQL关键字结束
        val validEndings =
            listOf(")", ";", "\\s+WHERE\\s+", "\\s+GROUP\\s+BY\\s+", "\\s+ORDER\\s+BY\\s+", "\\s+HAVING\\s+")

        // 这里可以添加更复杂的SQL解析逻辑，现在保持简单
        return cleaned
    }

    /**
     * 解析子查询的输出列
     */
    fun parseSubqueryOutputColumns(subquerySql: String): List<SubqueryColumn> {
        val outputColumns = mutableListOf<SubqueryColumn>()

        try {
            // 解析子查询
            val subqueryResult = SqlParser.parse(subquerySql)

            // 为每个列创建输出列映射
            subqueryResult.columns.forEach { columnRef ->
                val outputName = columnRef.alias ?: columnRef.name
                outputColumns.add(
                    SubqueryColumn(
                        outputName = outputName,
                        sourceColumn = columnRef
                    )
                )
            }
            logger.info("b8e3f7c2 | 子查询输出列: ${outputColumns.map { "${it.sourceColumn.name} AS ${it.outputName}" }}")
        } catch (e: Exception) {
            logger.warn("9a4f2d83 | 子查询解析失败: ${e.message}")
            logger.warn("60f56750 | 子查询SQL: $subquerySql")
        }

        return outputColumns
    }

    /**
     * 构建子查询解析映射
     */
    private fun buildSubqueryResolutionMap(subqueries: List<SubqueryInfo>): Map<String, SubqueryInfo> {
        return subqueries.associateBy { it.alias }
    }

    /**
     * 通过子查询解析列映射，建立正确的数据血缘链
     */
    private fun resolveColumnMappingsThroughSubqueries(
        originalMappings: List<ColumnMapping>,
        subqueryMap: Map<String, SubqueryInfo>
    ): List<ColumnMapping> {

        logger.debug("2f8c4e95 | 开始解析列映射，原始映射: ${originalMappings.size}, 子查询映射: ${subqueryMap.size}")

        if (subqueryMap.isEmpty()) {
            logger.debug("7a3d9f86 | 子查询映射为空，返回原始映射")
            return originalMappings
        }

        val resolvedMappings = originalMappings.map { mapping ->
            logger.debug("5c8e2f74 | 处理映射: ${mapping.sourceColumn.tablePrefix}.${mapping.sourceColumn.name} -> ${mapping.targetColumnName}")
            val resolved = resolveColumnMappingThroughSubqueries(mapping, subqueryMap, mutableSetOf())
            logger.debug("9d4a6f83 | 解析结果: ${resolved.sourceColumn.tablePrefix}.${resolved.sourceColumn.name} -> ${resolved.targetColumnName}")
            resolved
        }

        return resolvedMappings
    }

    /**
     * 验证增强的列映射是否确实改善了结果
     * 只有在子查询解析提供明确改善时才使用增强结果
     */
    private fun validateEnhancedMappings(
        original: List<ColumnMapping>,
        enhanced: List<ColumnMapping>,
        subqueryMap: Map<String, SubqueryInfo>
    ): Boolean {

        // 基本验证：增强结果不应该丢失列映射
        if (enhanced.size < original.size) {
            logger.debug("4f7e8a92 | 增强结果丢失了列映射，使用原始结果")
            return false
        }

        // 如果没有子查询映射，直接使用原始结果
        if (subqueryMap.isEmpty()) {
            logger.debug("2c9d5f83 | 无子查询映射，使用原始结果")
            return false
        }

        // 检查是否有实际的解析改进
        var hasActualImprovement = false

        for (i in original.indices) {
            val originalMapping = original[i]
            val enhancedMapping = if (i < enhanced.size) enhanced[i] else continue

            // 如果源列发生了有意义的变化（通过子查询解析）
            if (originalMapping.sourceColumn.name != enhancedMapping.sourceColumn.name) {
                // 验证这个变化是否合理
                val tablePrefix = originalMapping.sourceColumn.tablePrefix
                if (tablePrefix != null && subqueryMap.containsKey(tablePrefix)) {
                    val subqueryInfo = subqueryMap[tablePrefix]!!

                    // 检查原始列名是否在子查询输出中
                    val hasMatchingOutput = subqueryInfo.outputColumns.any {
                        it.outputName == originalMapping.sourceColumn.name
                    }

                    if (hasMatchingOutput) {
                        logger.debug("6a8f4e93 | 发现有效的子查询解析: ${originalMapping.sourceColumn.name} -> ${enhancedMapping.sourceColumn.name}")
                        hasActualImprovement = true
                    }
                }
            }
        }

        logger.debug("8d3f7a94 | 子查询解析是否有改进: $hasActualImprovement")
        return hasActualImprovement
    }

    /**
     * 解析单个列映射通过子查询链，使用循环检测防止无限递归
     */
    private fun resolveColumnMappingThroughSubqueries(
        mapping: ColumnMapping,
        subqueryMap: Map<String, SubqueryInfo>,
        visitedAliases: MutableSet<String>
    ): ColumnMapping {

        val sourceColumn = mapping.sourceColumn
        val tablePrefix = sourceColumn.tablePrefix

        // 如果没有表前缀，或者表前缀不是子查询别名，直接返回原映射
        if (tablePrefix == null || !subqueryMap.containsKey(tablePrefix)) {
            return mapping
        }

        // 循环检测：如果已经访问过这个别名，避免无限递归
        if (visitedAliases.contains(tablePrefix)) {
            logger.warn("2f9e4a73 | 检测到循环引用，停止递归解析: $tablePrefix")
            return mapping
        }

        visitedAliases.add(tablePrefix)

        val subqueryInfo = subqueryMap[tablePrefix]!!

        logger.debug("e7c9f2a8 | 解析列映射通过子查询: ${sourceColumn.name} (表前缀: $tablePrefix)")

        // 在子查询输出列中查找匹配的列
        val matchingSubqueryColumn = subqueryInfo.outputColumns.find {
            it.outputName == sourceColumn.name
        }

        if (matchingSubqueryColumn != null) {
            logger.debug("3f8d6c92 | 找到子查询列映射: ${sourceColumn.name} -> ${matchingSubqueryColumn.sourceColumn.name}")

            // 创建新的列映射，使用子查询中的实际源列
            val resolvedSourceColumn = matchingSubqueryColumn.sourceColumn

            // 递归解析：如果源列还有表前缀且也是子查询，继续解析
            val finalMapping = if (resolvedSourceColumn.tablePrefix != null &&
                subqueryMap.containsKey(resolvedSourceColumn.tablePrefix) &&
                !visitedAliases.contains(resolvedSourceColumn.tablePrefix)
            ) {
                // 创建临时映射进行递归解析
                val tempMapping = ColumnMapping(
                    sourceColumn = resolvedSourceColumn,
                    targetColumnName = mapping.targetColumnName,
                    targetColumnIndex = mapping.targetColumnIndex
                )
                resolveColumnMappingThroughSubqueries(tempMapping, subqueryMap, visitedAliases)
            } else {
                // 直接使用解析后的源列
                ColumnMapping(
                    sourceColumn = resolvedSourceColumn,
                    targetColumnName = mapping.targetColumnName,
                    targetColumnIndex = mapping.targetColumnIndex
                )
            }

            logger.debug("a5d8f7c4 | 最终解析结果: ${finalMapping.sourceColumn.tablePrefix}.${finalMapping.sourceColumn.name} -> ${finalMapping.targetColumnName}")
            return finalMapping

        } else {
            logger.warn("4f9e2a83 | 在子查询 '$tablePrefix' 中未找到列 '${sourceColumn.name}'")
            logger.warn("7c3d9f84 | 可用的子查询输出列: ${subqueryInfo.outputColumns.map { it.outputName }}")
            logger.debug("6e8a2f94 | 尝试分析列名不匹配的原因...")

            // 分析为什么找不到列的详细信息
            subqueryInfo.outputColumns.forEach { outputCol ->
                logger.debug("9c3f7e81 | 子查询输出列详情: outputName='${outputCol.outputName}', sourceColumn='${outputCol.sourceColumn.name}', sourceTablePrefix='${outputCol.sourceColumn.tablePrefix}'")
            }

            return mapping
        }
    }

    /**
     * 分析列表达式，判断是否为复杂表达式并提供相应的处理信息
     */
    private fun analyzeColumnExpression(columnRef: SqlColumnReference, index: Int): ColumnExpressionInfo {
        val expression = columnRef.name
        val expressionLower = expression.lowercase()

        // 检测字面量值（应该被过滤掉）
        if (isLiteralValue(expression)) {
            return ColumnExpressionInfo(
                isComplex = true,  // 标记为复杂以便被过滤
                suggestedColumnName = "literal_$index",
                expressionType = "Literal Value",
                transformationType = TransformationType.NONE,
                description = "字面量值，应被过滤"
            )
        }

        // 如果有别名且表达式很复杂，优先使用别名
        val suggestedColumnName = columnRef.alias ?: run {
            when {
                // CASE WHEN 表达式
                expressionLower.contains("case") && expressionLower.contains("when") -> "case_when_expr_$index"

                // 日期函数
                expressionLower.contains("datediff") -> "date_diff_$index"
                expressionLower.contains("date_add") || expressionLower.contains("date_sub") -> "date_calc_$index"
                expressionLower.contains("to_date") || expressionLower.contains("from_unixtime") -> "date_convert_$index"

                // 聚合函数
                expressionLower.contains("count(") -> "count_result_$index"
                expressionLower.contains("sum(") -> "sum_result_$index"
                expressionLower.contains("avg(") || expressionLower.contains("average(") -> "avg_result_$index"
                expressionLower.contains("max(") -> "max_result_$index"
                expressionLower.contains("min(") -> "min_result_$index"

                // 字符串函数
                expressionLower.contains("concat") -> "concat_result_$index"
                expressionLower.contains("substring") || expressionLower.contains("substr") -> "substr_result_$index"
                expressionLower.contains("trim") || expressionLower.contains("ltrim") || expressionLower.contains("rtrim") -> "trim_result_$index"

                // 数学函数
                expressionLower.contains("round") -> "round_result_$index"
                expressionLower.contains("ceil") || expressionLower.contains("floor") -> "math_result_$index"

                // 类型转换
                expressionLower.contains("cast(") -> "cast_result_$index"

                // 窗口函数
                expressionLower.contains("row_number") -> "row_num_$index"
                expressionLower.contains("rank") -> "rank_result_$index"
                expressionLower.contains("over(") -> "window_func_$index"

                // 默认情况
                else -> "computed_column_$index"
            }
        }

        // 判断表达式类型和是否复杂
        val isComplex = expression.contains("(") ||  // 函数调用
                expression.contains("CASE", ignoreCase = true) ||  // CASE 表达式
                expression.contains("WHEN", ignoreCase = true) ||
                expression.contains("THEN", ignoreCase = true) ||
                expression.contains("+") || expression.contains("-") ||  // 算术运算
                expression.contains("*") || expression.contains("/") ||
                expression.length > 50  // 长表达式

        if (!isComplex) {
            return ColumnExpressionInfo(
                isComplex = false,
                suggestedColumnName = expression,
                expressionType = "Simple Column",
                transformationType = TransformationType.NONE,
                description = "直接列映射"
            )
        }

        // 识别表达式类型
        val (expressionType, transformationType, description) = when {
            expressionLower.contains("case") && expressionLower.contains("when") ->
                Triple("CASE Expression", TransformationType.CONDITIONAL, "条件判断表达式")

            expressionLower.contains("count") || expressionLower.contains("sum") ||
                    expressionLower.contains("avg") || expressionLower.contains("max") ||
                    expressionLower.contains("min") ->
                Triple("Aggregate Function", TransformationType.AGGREGATION, "聚合函数计算")

            expressionLower.contains("datediff") || expressionLower.contains("date_add") ||
                    expressionLower.contains("date_sub") || expressionLower.contains("to_date") ||
                    expressionLower.contains("from_unixtime") ->
                Triple("Date Function", TransformationType.FUNCTION, "日期函数计算")

            expressionLower.contains("cast(") ->
                Triple("Type Conversion", TransformationType.TYPE_CAST, "数据类型转换")

            expressionLower.contains("concat") || expressionLower.contains("substring") ||
                    expressionLower.contains("trim") ->
                Triple("String Function", TransformationType.FUNCTION, "字符串函数处理")

            expression.contains("+") || expression.contains("-") ||
                    expression.contains("*") || expression.contains("/") ->
                Triple("Arithmetic Expression", TransformationType.EXPRESSION, "算术表达式计算")

            expressionLower.contains("(") ->
                Triple("Function Call", TransformationType.FUNCTION, "函数调用")

            else ->
                Triple("Complex Expression", TransformationType.EXPRESSION, "复杂表达式计算")
        }

        return ColumnExpressionInfo(
            isComplex = true,
            suggestedColumnName = suggestedColumnName,
            expressionType = expressionType,
            transformationType = transformationType,
            description = description
        )
    }

    /**
     * 为列引用找到正确的源表，处理表别名解析
     */
    private fun findSourceTableForColumn(
        columnRef: SqlColumnReference,
        sourceTableInfos: List<TableInfo>,
        warnings: MutableList<String>
    ): TableInfo? {
        val tablePrefix = columnRef.tablePrefix

        // 1. 无前缀 - 使用第一个表（通常是主表）
        if (tablePrefix == null) {
            return sourceTableInfos.firstOrNull()
        }

        // 2. 直接表名匹配（无alias情况）
        val directMatch = sourceTableInfos.find { table ->
            tablePrefix == table.tableName ||
                    tablePrefix == "${table.schema}.${table.tableName}"
        }
        if (directMatch != null) {
            return directMatch
        }

        // 3. 处理表别名 - 智能匹配策略
        // 当tablePrefix是单字符别名时，尝试从列名推断正确的表
        if (tablePrefix.length == 1) {
            val candidateTable = findTableByColumnNameHeuristic(
                columnRef.name,
                sourceTableInfos,
                tablePrefix,
                warnings
            )
            if (candidateTable != null) {
                warnings.add("6b8d3f72 | 通过列名启发式匹配找到表: ${candidateTable.tableName} for alias '$tablePrefix'")
                return candidateTable
            }
        }

        // 4. 尝试别名到表名的常见模式匹配
        val patternMatch = findTableByAliasPattern(tablePrefix, sourceTableInfos)
        if (patternMatch != null) {
            warnings.add("3e7f2a94 | 通过别名模式匹配找到表: ${patternMatch.tableName} for alias '$tablePrefix'")
            return patternMatch
        }

        // 5. 兜底策略 - 记录详细信息以便调试
        warnings.add("9c4a8f67 | 无法解析表别名 '$tablePrefix' for 列 '${columnRef.name}', 可用表: ${sourceTableInfos.map { it.tableName }}")

        // 不再默认返回第一个表，而是返回null让调用方处理
        return null
    }

    /**
     * 基于列名的启发式表匹配
     * 某些列名可能暗示它们来自特定的表
     */
    private fun findTableByColumnNameHeuristic(
        columnName: String,
        sourceTableInfos: List<TableInfo>,
        alias: String,
        warnings: MutableList<String>
    ): TableInfo? {
        // 移除硬编码的列名模式匹配，让系统更通用
        // 可以在这里实现更智能的列名-表名匹配逻辑，比如：
        // 1. 基于列名和表名的词汇相似度
        // 2. 基于数据库schema信息
        // 3. 基于机器学习的表-列关联模型

        // 目前返回null，让调用方处理
        return null
    }

    /**
     * 基于别名模式的表匹配
     * 尝试将别名映射到实际表名
     */
    private fun findTableByAliasPattern(alias: String, sourceTableInfos: List<TableInfo>): TableInfo? {
        // 移除硬编码的别名模式匹配，让系统更通用
        // 可以在这里实现更智能的别名解析逻辑，比如：
        // 1. 基于别名首字母匹配表名
        // 2. 基于别名长度和表名相似度
        // 3. 基于别名在SQL中的出现顺序

        // 目前返回null，让调用方处理
        return null
    }

    /**
     * 检测是否为字面量值
     */
    private fun isLiteralValue(expression: String): Boolean {
        val trimmed = expression.trim()

        return when {
            // 字符串字面量：'value' 或 "value"
            (trimmed.startsWith("'") && trimmed.endsWith("'")) ||
                    (trimmed.startsWith("\"") && trimmed.endsWith("\"")) -> true

            // 数字字面量：整数或小数
            trimmed.matches(Regex("^[+-]?\\d+(\\.\\d+)?$")) -> true

            // 布尔字面量
            trimmed.lowercase() in listOf("true", "false") -> true

            // NULL 字面量
            trimmed.lowercase() == "null" -> true

            // 其他常见字面量模式
            else -> false
        }
    }

    /**
     * 列表达式分析结果信息
     */
    private data class ColumnExpressionInfo(
        val isComplex: Boolean,
        val suggestedColumnName: String,
        val expressionType: String,
        val transformationType: TransformationType,
        val description: String
    )

    /**
     * 创建空血缘结果
     */
    private fun createEmptyLineageResult(
        warnings: MutableList<String>,
        errors: MutableList<String>
    ): LineageResult {
        warnings.add("创建空血缘结果")
        return LineageResult(null, warnings, errors, false)
    }

    /**
     * 根据数据库类型确定实际的schema值
     * 默认情况下，a.b中的a被视为数据库名，不是schema
     * PostgreSQL和Oracle明确支持schema概念时才保留作为schema
     */
    private fun determineSchemaForDatabaseType(dbType: String, schemaOrDatabase: String?): String? {
        return when (dbType.lowercase()) {
            "postgresql", "oracle" -> {
                // PostgreSQL和Oracle明确支持schema概念，保持原有逻辑
                schemaOrDatabase
            }

            else -> {
                // 其他所有数据库类型（Hive/MySQL等），默认将a.b中的a视为数据库名
                null
            }
        }
    }
}
