### What was changed

Optimized `ShellScriptParser.substituteVariables` to accept a configurable `maxIterations` parameter and limited iterations to **3** when substituting variables inside large SQL blocks in `parseShellScriptForSql`.

### Why the change was necessary

Production logs revealed `java.lang.OutOfMemoryError: Java heap space` during variable substitution inside large Hive SQL extractions. Repeated full-string replacements across up to 10 iterations created multiple large `String` instances, exhausting the JVM heap.

### How it was implemented

1. Extended `substituteVariables` signature: `maxIterations` (default = 10) allows callers to tune iteration depth.
2. Removed hard-coded iteration constant inside the method and reused the parameter throughout the loop.
3. In `parseShellScriptForSql`, lowered iteration count to **3** for Hive SQL substitution to avoid unnecessary additional passes over very large strings.
4. Added inline documentation explaining the rationale and memory-usage considerations.

### Follow-up tasks / TODOs

- Monitor heap usage after deployment; further reduce iterations or refactor to single-pass substitution if required.
- Consider pre-resolving shell variable map to make single-pass substitution safe for *all* call-sites. 