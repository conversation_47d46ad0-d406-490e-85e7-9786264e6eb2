package com.datayes.task

import com.datayes.dataexchange.DataExchangeJobService
import com.datayes.dataexchange.DataExchangeJobPersistenceRepository
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Pageable
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.sql.ResultSet
import java.time.LocalDateTime
import java.util.*

/**
 * 手动触发血缘任务服务 (Manual Lineage Trigger Task Service)
 *
 * 实现手动触发血缘任务的核心业务逻辑，包括任务创建、执行和查询功能
 * 遵循函数式核心，命令式外壳的架构原则
 */
@Service
@Transactional
class ManualLineageTriggerTaskService(
    private val manualTaskRepository: ManualLineageTriggerTaskRepository,
    private val manualTaskCustomRepository: ManualLineageTriggerTaskCustomRepository,
    private val dataExchangeJobService: DataExchangeJobService,
    private val dataExchangeJobPersistenceRepository: DataExchangeJobPersistenceRepository,
    private val jdbcTemplate: JdbcTemplate
) {

    private val logger = LoggerFactory.getLogger(ManualLineageTriggerTaskService::class.java)

    /**
     * 批量手动触发血缘任务 (Batch trigger manual lineage tasks)
     *
     * 异步执行多个数据源的血缘任务
     */
    fun triggerBatchManualLineageTasks(request: BatchManualLineageTriggerRequest): BatchManualLineageTriggerResponse {
        logger.info("1a2b3c | 批量创建手动触发血缘任务，数据源IDs: ${request.datasourceIds}, 用户: ${request.triggerUser}")

        // 1. 验证请求参数
        if (request.datasourceIds.isEmpty()) {
            throw IllegalArgumentException("数据源ID列表不能为空")
        }

        val taskUuids = mutableListOf<String>()
        val validDatasourceIds = mutableListOf<Long>()

        // 2. 为每个数据源创建任务记录
        for (datasourceId in request.datasourceIds) {
            try {
                // 验证数据源是否存在
                validateDatasourceExists(datasourceId)

                val taskUuid = UUID.randomUUID().toString()
                val task = ManualLineageTriggerTask(
                    taskUuid = taskUuid,
                    datasourceId = datasourceId,
                    triggerUser = request.triggerUser,
                    taskStatus = ManualTaskStatus.PENDING
                )

                val savedTask = manualTaskRepository.save(task)
                taskUuids.add(taskUuid)
                validDatasourceIds.add(datasourceId)

                logger.info("4d5e6f | 成功创建批量任务，数据源ID: $datasourceId, 任务UUID: $taskUuid, 任务ID: ${savedTask.id}")

                // 3. 异步执行血缘任务
                executeLineageTaskAsync(taskUuid)

            } catch (e: Exception) {
                logger.error("7g8h9i | 创建数据源 $datasourceId 的任务失败", e)
                // 继续处理其他数据源，不中断整个批量操作
            }
        }

        if (taskUuids.isEmpty()) {
            throw IllegalStateException("没有成功创建任何任务")
        }

        return BatchManualLineageTriggerResponse(
            taskUuids = taskUuids,
            datasourceIds = validDatasourceIds,
            triggerUser = request.triggerUser,
            totalTasks = taskUuids.size,
            message = "成功创建 ${taskUuids.size} 个血缘任务，正在异步执行中"
        )
    }

    /**
     * 手动触发血缘任务 (Trigger manual lineage task)
     */
    fun triggerManualLineageTask(request: ManualLineageTriggerRequest): ManualTaskSummary {
        val taskUuid = UUID.randomUUID().toString()

        logger.info("7a8b9c | 创建手动触发血缘任务，数据源ID: ${request.datasourceId}, 任务UUID: $taskUuid")

        // 1. 验证数据源是否存在
        validateDatasourceExists(request.datasourceId)

        // 2. 创建任务记录
        val task = ManualLineageTriggerTask(
            taskUuid = taskUuid,
            datasourceId = request.datasourceId,
            triggerUser = request.triggerUser,
            taskStatus = ManualTaskStatus.PENDING
        )

        val savedTask = manualTaskRepository.save(task)
        logger.info("d4e5f6 | 成功创建手动触发任务，任务ID: ${savedTask.id}")

        // 3. 同步执行血缘任务
        executeLineageTask(taskUuid)

        // 4. 获取执行完成后的任务信息
        val completedTask = manualTaskRepository.findByTaskUuid(taskUuid)
            ?: throw IllegalStateException("任务执行后未找到: $taskUuid")

        // 5. 获取数据源名称（简化实现）
        val datasourceName = getDatasourceName(request.datasourceId)

        return ManualTaskSummary(
            id = completedTask.id,
            taskUuid = completedTask.taskUuid,
            datasourceId = completedTask.datasourceId,
            datasourceName = datasourceName,
            triggerUser = completedTask.triggerUser,
            taskStatus = completedTask.taskStatus,
            successCount = completedTask.successCount,
            failureCount = completedTask.failureCount,
            totalCount = completedTask.totalCount,
            executionTimeMs = completedTask.executionTimeMs,
            createdAt = completedTask.createdAt,
            completedAt = completedTask.completedAt
        )
    }

    /**
     * 手动触发血缘任务（增强版） (Trigger manual lineage task with enhanced response)
     * 
     * 返回包含实际执行任务详情的完整响应
     */
    fun triggerManualLineageTaskWithDetails(request: ManualLineageTriggerRequest): ManualTaskTriggerResponse {
        val taskUuid = UUID.randomUUID().toString()

        logger.info("enh123 | 创建增强版手动触发血缘任务，数据源ID: ${request.datasourceId}, 任务UUID: $taskUuid")

        // 1. 验证数据源是否存在
        validateDatasourceExists(request.datasourceId)

        // 2. 创建任务记录
        val task = ManualLineageTriggerTask(
            taskUuid = taskUuid,
            datasourceId = request.datasourceId,
            triggerUser = request.triggerUser,
            taskStatus = ManualTaskStatus.PENDING
        )

        val savedTask = manualTaskRepository.save(task)
        logger.info("enh456 | 成功创建增强版手动触发任务，任务ID: ${savedTask.id}")

        // 3. 获取将要执行的血缘任务列表
        val lineageTasks = findLineageTasksByDatasourceChain(request.datasourceId)
        logger.info("enh789 | 找到 ${lineageTasks.size} 个待执行的血缘任务")

        // 4. 同步执行血缘任务
        executeLineageTask(taskUuid)

        // 5. 获取执行完成后的任务信息
        val completedTask = manualTaskRepository.findByTaskUuid(taskUuid)
            ?: throw IllegalStateException("任务执行后未找到: $taskUuid")

        // 6. 获取数据源名称
        val datasourceName = getDatasourceName(request.datasourceId)

        // 7. 构建任务摘要
        val taskSummary = ManualTaskSummary(
            id = completedTask.id,
            taskUuid = completedTask.taskUuid,
            datasourceId = completedTask.datasourceId,
            datasourceName = datasourceName,
            triggerUser = completedTask.triggerUser,
            taskStatus = completedTask.taskStatus,
            successCount = completedTask.successCount,
            failureCount = completedTask.failureCount,
            totalCount = completedTask.totalCount,
            executionTimeMs = completedTask.executionTimeMs,
            createdAt = completedTask.createdAt,
            completedAt = completedTask.completedAt
        )

        // 8. 获取执行的血缘任务详情
        val executedLineageTasks = getExecutedLineageTaskDetails(lineageTasks, taskUuid)

        // 9. 构建完整响应
        val message = when (completedTask.taskStatus) {
            ManualTaskStatus.SUCCESS -> "任务执行成功，处理了 ${completedTask.totalCount} 个血缘任务"
            ManualTaskStatus.FAILED -> "任务执行失败：${completedTask.errorMessage ?: "未知错误"}"
            else -> "任务执行完成，状态：${completedTask.taskStatus}"
        }

        return ManualTaskTriggerResponse(
            taskSummary = taskSummary,
            executedTask = completedTask,
            executedLineageTasks = executedLineageTasks,
            message = message
        )
    }

    /**
     * 异步执行血缘任务 (Execute lineage task asynchronously)
     */
    @Async
    fun executeLineageTaskAsync(taskUuid: String) {
        executeLineageTask(taskUuid)
    }

    /**
     * 执行血缘任务 (Execute lineage task)
     */
    private fun executeLineageTask(taskUuid: String) {
        val startTime = System.currentTimeMillis()

        try {
            logger.info("123abc | 开始执行手动血缘任务，UUID: $taskUuid")

            // 1. 更新任务状态为运行中
            manualTaskCustomRepository.updateTaskStatus(
                taskUuid = taskUuid,
                status = ManualTaskStatus.RUNNING,
                startedAt = LocalDateTime.now()
            )

            // 2. 获取任务信息
            val task = manualTaskRepository.findByTaskUuid(taskUuid)
                ?: throw IllegalStateException("任务不存在: $taskUuid")

            // 3. 使用数据源链方式获取相关的血缘任务
            val lineageTasks = findLineageTasksByDatasourceChain(task.datasourceId)
            logger.info("456def | 通过数据源链找到 ${lineageTasks.size} 个相关的血缘任务")

            val successResults = mutableListOf<TaskResultItem>()
            val failureResults = mutableListOf<TaskResultItem>()
            var successCount = 0
            var failureCount = 0

            // 4. 处理血缘任务
            for (lineageTask in lineageTasks) {
                val taskStartTime = LocalDateTime.now()
                val taskStartTimeMs = System.currentTimeMillis()
                
                // 生成本次执行的ID
                val executionId = "${taskUuid}_${lineageTask.id}_${System.currentTimeMillis()}"
                
                // 更新任务开始执行时间和执行ID
                updateLineageTaskExecutionStart(lineageTask.id, taskStartTime, executionId, taskUuid)
                
                try {
                    logger.info("789ghi | 处理血缘任务: ${lineageTask.jobKey}")

                    // 根据任务类型执行相应的血缘处理
                    val result = when (lineageTask.taskType) {
                        TaskType.DATA_EXCHANGE_PLATFORM -> processDataExchangeLineageTask(lineageTask)
                        TaskType.BIG_DATA_PLATFORM -> processHdfsShellScriptLineageTask(lineageTask)
                        else -> throw IllegalArgumentException("不支持的任务类型: ${lineageTask.taskType}")
                    }

                    // 计算处理耗时
                    val processingTimeMs = System.currentTimeMillis() - taskStartTimeMs

                    // 更新任务成功完成状态
                    updateLineageTaskExecutionSuccess(
                        taskId = lineageTask.id,
                        completedAt = LocalDateTime.now(),
                        processingTimeMs = processingTimeMs,
                        hasChanges = result.hasChanges ?: false,
                        executionId = executionId
                    )

                    successResults.add(
                        TaskResultItem(
                            itemType = lineageTask.taskType.name,
                            itemId = lineageTask.jobKey!!,
                            itemName = lineageTask.taskName,
                            message = "血缘处理成功: ${result.message}"
                        )
                    )
                    successCount++

                    logger.info("abc123 | 血缘任务 ${lineageTask.jobKey} 处理成功，耗时: ${processingTimeMs}ms, 有变更: ${result.hasChanges}")

                } catch (e: Exception) {
                    logger.error("def456 | 血缘任务处理失败: ${lineageTask.jobKey}", e)

                    // 计算处理耗时（即使失败也要记录）
                    val processingTimeMs = System.currentTimeMillis() - taskStartTimeMs

                    // 更新任务失败状态
                    updateLineageTaskExecutionFailure(
                        taskId = lineageTask.id,
                        completedAt = LocalDateTime.now(),
                        errorMessage = e.message,
                        processingTimeMs = processingTimeMs,
                        executionId = executionId
                    )

                    failureResults.add(
                        TaskResultItem(
                            itemType = lineageTask.taskType.name,
                            itemId = lineageTask.jobKey!!,
                            itemName = lineageTask.taskName,
                            message = "血缘处理失败: ${e.message}"
                        )
                    )
                    failureCount++
                }
            }

            val totalCount = successCount + failureCount
            val executionTime = System.currentTimeMillis() - startTime

            // 5. 更新任务结果
            manualTaskCustomRepository.updateTaskResults(
                taskUuid = taskUuid,
                successCount = successCount,
                failureCount = failureCount,
                totalCount = totalCount,
                successResults = successResults,
                failureResults = failureResults
            )

            // 6. 更新任务状态为完成
            val finalStatus = if (failureCount == 0) ManualTaskStatus.SUCCESS else ManualTaskStatus.SUCCESS
            manualTaskCustomRepository.updateTaskStatus(
                taskUuid = taskUuid,
                status = finalStatus,
                completedAt = LocalDateTime.now(),
                executionTimeMs = executionTime
            )

            logger.info("ghi789 | 手动血缘任务执行完成，UUID: $taskUuid, 成功: $successCount, 失败: $failureCount")

        } catch (e: Exception) {
            logger.error("jkl012 | 手动血缘任务执行失败，UUID: $taskUuid", e)

            // 更新任务状态为失败
            manualTaskCustomRepository.updateTaskStatus(
                taskUuid = taskUuid,
                status = ManualTaskStatus.FAILED,
                completedAt = LocalDateTime.now(),
                executionTimeMs = System.currentTimeMillis() - startTime,
                errorMessage = e.message
            )
        }
    }

    /**
     * 查询手动任务列表 (Query manual task list)
     */
    @Transactional(readOnly = true)
    fun queryManualTasks(criteria: ManualTaskQueryCriteria, pageable: Pageable): ManualTaskListResponse {
        logger.info("mno345 | 查询手动任务列表，条件: $criteria")

        val page = manualTaskCustomRepository.findTasksWithCriteria(criteria, pageable)

        val summaries = page.content.map { detail ->
            ManualTaskSummary(
                id = detail.id,
                taskUuid = detail.taskUuid,
                datasourceId = detail.datasourceId,
                datasourceName = detail.datasourceName,
                triggerUser = detail.triggerUser,
                taskStatus = detail.taskStatus,
                successCount = detail.successCount,
                failureCount = detail.failureCount,
                totalCount = detail.totalCount,
                executionTimeMs = detail.executionTimeMs,
                createdAt = detail.createdAt,
                completedAt = detail.completedAt
            )
        }

        return ManualTaskListResponse(
            tasks = summaries,
            totalElements = page.totalElements,
            totalPages = page.totalPages,
            currentPage = page.number,
            size = page.size
        )
    }

    /**
     * 根据UUID获取任务详情 (Get task detail by UUID)
     */
    @Transactional(readOnly = true)
    fun getTaskDetail(taskUuid: String): ManualTaskDetailResponse? {
        logger.info("pqr678 | 获取任务详情，UUID: $taskUuid")
        return manualTaskCustomRepository.findTaskDetailByUuid(taskUuid)
    }

    /**
     * 取消手动任务 (Cancel manual task)
     */
    fun cancelManualTask(taskUuid: String): Boolean {
        logger.info("stu901 | 取消手动任务，UUID: $taskUuid")

        val task = manualTaskRepository.findByTaskUuid(taskUuid)
            ?: throw IllegalArgumentException("任务不存在: $taskUuid")

        // 只有PENDING或RUNNING状态的任务可以取消
        if (task.taskStatus != ManualTaskStatus.PENDING && task.taskStatus != ManualTaskStatus.RUNNING) {
            throw IllegalStateException("任务状态不允许取消: ${task.taskStatus}")
        }

        val updateCount = manualTaskCustomRepository.updateTaskStatus(
            taskUuid = taskUuid,
            status = ManualTaskStatus.CANCELLED,
            completedAt = LocalDateTime.now(),
            errorMessage = "任务已被手动取消"
        )

        return updateCount > 0
    }

    /**
     * 验证数据源是否存在 (Validate datasource exists)
     */
    private fun validateDatasourceExists(datasourceId: Long) {
        // 这里应该调用数据源服务验证数据源是否存在
        // 简化实现，假设数据源存在
        logger.debug("vwx234 | 验证数据源是否存在，ID: $datasourceId")

        // 通过查询相关作业来验证数据源的有效性
        // val jobs = dataExchangeJobService.findDataExchangeJobsByDatasourceId(datasourceId)
        // if (jobs.isEmpty()) {
        //     logger.warn("yzab567 | 数据源ID $datasourceId 没有找到相关的数据交换作业")
        // }
    }

    /**
     * 获取数据源相关统计信息 (Get datasource related statistics)
     */
    @Transactional(readOnly = true)
    fun getDatasourceTaskStatistics(datasourceId: Long): DatasourceTaskStatistics {
        logger.info("cdef890 | 获取数据源任务统计，ID: $datasourceId")

        val tasks = manualTaskRepository.findByDatasourceId(datasourceId)

        val totalTasks = tasks.size
        val successfulTasks = tasks.count { it.taskStatus == ManualTaskStatus.SUCCESS }
        val failedTasks = tasks.count { it.taskStatus == ManualTaskStatus.FAILED }
        val runningTasks = tasks.count { it.taskStatus == ManualTaskStatus.RUNNING }
        val pendingTasks = tasks.count { it.taskStatus == ManualTaskStatus.PENDING }

        val lastExecutedTask = tasks
            .filter { it.completedAt != null }
            .maxByOrNull { it.completedAt!! }

        return DatasourceTaskStatistics(
            datasourceId = datasourceId,
            totalTasks = totalTasks,
            successfulTasks = successfulTasks,
            failedTasks = failedTasks,
            runningTasks = runningTasks,
            pendingTasks = pendingTasks,
            lastExecutedAt = lastExecutedTask?.completedAt,
            averageExecutionTimeMs = tasks
                .mapNotNull { it.executionTimeMs }
                .takeIf { it.isNotEmpty() }
                ?.average()?.toLong()
        )
    }

    /**
     * 通过数据源链查找血缘任务 (Find lineage tasks by datasource chain)
     *
     * 实现链路: datasourceid -> lineage_datasources -> lineage_tables -> lineage_relationships -> lineage_tasks
     */
    private fun findLineageTasksByDatasourceChain(datasourceId: Long): List<LineageTask> {
        logger.info("rst901 | 通过数据源链查找血缘任务，datasourceId: $datasourceId")

        val sql = """
            SELECT DISTINCT lt.*
            FROM lineage_tasks lt
            INNER JOIN lineage_relationships lr ON lt.id = lr.task_id
            INNER JOIN lineage_tables st ON lr.source_table_id = st.id
            INNER JOIN lineage_tables tt ON lr.target_table_id = tt.id
            WHERE (st.datasource_id = ? OR tt.datasource_id = ?)
              AND lr.is_active = true
              AND lt.is_enabled = true
            ORDER BY lt.created_at DESC
        """.trimIndent()

        return try {
            val tasks = jdbcTemplate.query(sql, LineageTaskRowMapper(), datasourceId, datasourceId)
            logger.info("uvw234 | 通过数据源链找到 ${tasks.size} 个血缘任务")
            tasks
        } catch (e: Exception) {
            logger.error("xyz567 | 通过数据源链查找血缘任务失败", e)
            emptyList()
        }
    }

    /**
     * 处理数据交换血缘任务 (Process Data Exchange Lineage Task)
     */
    private fun processDataExchangeLineageTask(lineageTask: LineageTask): LineageTaskProcessingResult {
        logger.info("abc890 | 处理数据交换血缘任务，jobKey: ${lineageTask.jobKey}")

        val jobKey = lineageTask.jobKey ?:             return LineageTaskProcessingResult(
                success = false,
                message = "任务缺少作业键",
                processedCount = 0,
                hasChanges = false
            )

        return try {
            // 解析数据交换作业键 (格式: readerJobId_writeJobId)
            val jobKeyParts = jobKey.split("_")
            if (jobKeyParts.size < 2) {
                return LineageTaskProcessingResult(
                    success = false,
                    message = "数据交换作业键格式错误: $jobKey",
                    processedCount = 0,
                    hasChanges = false
                )
            }

            // 根据作业键直接查找对应的数据交换作业
            val job = dataExchangeJobPersistenceRepository.findDataExchangeJobByIds(jobKeyParts[0], jobKeyParts[1])
            if (job == null) {
                logger.warn("def123 | 找不到对应的数据交换作业: $jobKey")
                return LineageTaskProcessingResult(
                    success = true,
                    message = "找不到对应的数据交换作业，跳过处理",
                    processedCount = 0,
                    hasChanges = false
                )
            }

            // 处理血缘
            val result = dataExchangeJobService.processJobLineageWithChangeDetection(job)
            logger.info("ghi456 | 数据交换作业处理完成: ${job.readerJobId}_${job.writeJobId}, 有变更: ${result.hasChanges}")

            LineageTaskProcessingResult(
                success = true,
                message = "成功处理数据交换作业，处理结果: ${result.processingResult}",
                processedCount = 1,
                hasChanges = result.hasChanges
            )
            
        } catch (e: Exception) {
            logger.error("jkl789 | 数据交换血缘任务处理失败: $jobKey", e)
            LineageTaskProcessingResult(
                success = false,
                message = "处理失败: ${e.message}",
                processedCount = 0,
                hasChanges = false
            )
        }
    }

    /**
     * 处理HDFS Shell脚本血缘任务 (Process HDFS Shell Script Lineage Task)
     */
    private fun processHdfsShellScriptLineageTask(lineageTask: LineageTask): LineageTaskProcessingResult {
        logger.info("jkl789 | 处理HDFS Shell脚本血缘任务，jobKey: ${lineageTask.jobKey}")
        logger.info("mno012 | 注意：HDFS Shell脚本血缘处理功能尚未完全实现")

        // TODO: 实现以下逻辑：
        // 1. 根据 job_key 获取 HDFS Shell 脚本内容
        // 2. 解析脚本中的SQL语句
        // 3. 分析SQL语句的表依赖关系
        // 4. 生成血缘关系并保存到数据库

        return LineageTaskProcessingResult(
            success = true,
            message = "HDFS Shell脚本血缘任务处理占位符实现",
            processedCount = 1,
            hasChanges = false // TODO: 实现真实的变更检测逻辑
        )
    }

    /**
     * 获取数据源名称 (Get datasource name)
     */
    private fun getDatasourceName(datasourceId: Long): String? {
        return try {
            // 简化实现，返回默认格式
            "Datasource-$datasourceId"
        } catch (e: Exception) {
            logger.warn("def123 | 获取数据源名称失败，ID: $datasourceId", e)
            null
        }
    }

    /**
     * 更新血缘任务执行开始状态 (Update lineage task execution start status)
     */
    private fun updateLineageTaskExecutionStart(
        taskId: Long, 
        executedAt: LocalDateTime, 
        executionId: String,
        batchId: String
    ) {
        try {
            val sql = """
                UPDATE lineage_tasks 
                SET executed_at = ?, 
                    last_execution_id = ?,
                    batch_id = ?,
                    execution_count = execution_count + 1,
                    updated_at = ?
                WHERE id = ?
            """.trimIndent()
            
            val updateCount = jdbcTemplate.update(
                sql,
                executedAt,
                executionId,
                batchId,
                LocalDateTime.now(),
                taskId
            )
            
            if (updateCount > 0) {
                logger.debug("lineage_task_update | 成功更新血缘任务执行开始时间，taskId: $taskId, executionId: $executionId")
            } else {
                logger.warn("lineage_task_update | 未找到要更新的血缘任务，taskId: $taskId")
            }
        } catch (e: Exception) {
            logger.error("lineage_task_update | 更新血缘任务执行开始时间失败，taskId: $taskId", e)
        }
    }

    /**
     * 更新血缘任务执行成功状态 (Update lineage task execution success status)
     */
    private fun updateLineageTaskExecutionSuccess(
        taskId: Long, 
        completedAt: LocalDateTime,
        processingTimeMs: Long,
        hasChanges: Boolean,
        executionId: String
    ) {
        try {
            val sql = """
                UPDATE lineage_tasks 
                SET completed_at = ?, 
                    error_message = NULL, 
                    processing_time_ms = ?,
                    has_changes = ?,
                    last_execution_id = ?,
                    updated_at = ?
                WHERE id = ?
            """.trimIndent()
            
            val updateCount = jdbcTemplate.update(
                sql,
                completedAt,
                processingTimeMs,
                hasChanges,
                executionId,
                LocalDateTime.now(),
                taskId
            )
            
            if (updateCount > 0) {
                logger.debug("lineage_task_update | 成功更新血缘任务完成状态，taskId: $taskId, 耗时: ${processingTimeMs}ms")
            } else {
                logger.warn("lineage_task_update | 未找到要更新的血缘任务，taskId: $taskId")
            }
        } catch (e: Exception) {
            logger.error("lineage_task_update | 更新血缘任务完成状态失败，taskId: $taskId", e)
        }
    }

    /**
     * 更新血缘任务执行失败状态 (Update lineage task execution failure status)
     */
    private fun updateLineageTaskExecutionFailure(
        taskId: Long, 
        completedAt: LocalDateTime, 
        errorMessage: String?,
        processingTimeMs: Long,
        executionId: String
    ) {
        try {
            val sql = """
                UPDATE lineage_tasks 
                SET completed_at = ?, 
                    error_message = ?, 
                    processing_time_ms = ?,
                    has_changes = 0,
                    last_execution_id = ?,
                    updated_at = ?
                WHERE id = ?
            """.trimIndent()
            
            val updateCount = jdbcTemplate.update(
                sql,
                completedAt,
                errorMessage?.take(1000), // 限制错误信息长度，避免数据库字段溢出
                processingTimeMs,
                executionId,
                LocalDateTime.now(),
                taskId
            )
            
            if (updateCount > 0) {
                logger.debug("lineage_task_update | 成功更新血缘任务失败状态，taskId: $taskId, 耗时: ${processingTimeMs}ms")
            } else {
                logger.warn("lineage_task_update | 未找到要更新的血缘任务，taskId: $taskId")
            }
        } catch (e: Exception) {
            logger.error("lineage_task_update | 更新血缘任务失败状态失败，taskId: $taskId", e)
        }
    }

    /**
     * 获取执行的血缘任务详情 (Get executed lineage task details)
     */
    private fun getExecutedLineageTaskDetails(
        lineageTasks: List<LineageTask>, 
        taskUuid: String
    ): List<ExecutedLineageTaskDetail> {
        return lineageTasks.map { task ->
            ExecutedLineageTaskDetail(
                taskId = task.id,
                taskName = task.taskName,
                taskType = task.taskType.name,
                jobKey = task.jobKey,
                sourceType = task.sourceType.name,
                sourceIdentifier = task.sourceIdentifier,
                taskStatus = task.taskStatus.name,
                processingTimeMs = task.processingTimeMs,
                hasChanges = task.hasChanges,
                errorMessage = task.errorMessage,
                executionId = task.lastExecutionId,
                executedAt = task.executedAt,
                completedAt = task.completedAt
            )
        }
    }
}

/**
 * 血缘任务处理结果 (Lineage Task Processing Result)
 */
data class LineageTaskProcessingResult(
    val success: Boolean,
    val message: String,
    val processedCount: Int,
    val hasChanges: Boolean? = null,
    val errorDetails: String? = null
)

/**
 * 血缘任务行映射器 (Lineage Task Row Mapper)
 */
private class LineageTaskRowMapper : RowMapper<LineageTask> {
    override fun mapRow(rs: ResultSet, rowNum: Int): LineageTask {
        return LineageTask(
            id = rs.getLong("id"),
            taskName = rs.getString("task_name"),
            taskType = TaskType.valueOf(rs.getString("task_type")),
            sourceType = SourceType.valueOf(rs.getString("source_type")),
            sourceIdentifier = rs.getString("source_identifier"),
            sourceContent = rs.getString("source_content"),
            taskStatus = TaskStatus.valueOf(rs.getString("task_status")),
            scheduleType = ScheduleType.valueOf(rs.getString("schedule_type")),
            cronExpression = rs.getString("cron_expression"),
            isEnabled = rs.getBoolean("is_enabled"),
            createdBy = rs.getString("created_by"),
            executedAt = rs.getTimestamp("executed_at")?.toLocalDateTime(),
            completedAt = rs.getTimestamp("completed_at")?.toLocalDateTime(),
            errorMessage = rs.getString("error_message"),
            createdAt = rs.getTimestamp("created_at").toLocalDateTime(),
            updatedAt = rs.getTimestamp("updated_at").toLocalDateTime(),
            jobKey = rs.getString("job_key"),
            processingTimeMs = rs.getObject("processing_time_ms") as? Long,
            hasChanges = rs.getObject("has_changes") as? Boolean ?: false,
            batchId = rs.getString("batch_id"),
            executionCount = rs.getInt("execution_count"),
            lastExecutionId = rs.getString("last_execution_id")
        )
    }
}

/**
 * 数据源任务统计信息 (Datasource Task Statistics)
 */
data class DatasourceTaskStatistics(
    val datasourceId: Long,
    val totalTasks: Int,
    val successfulTasks: Int,
    val failedTasks: Int,
    val runningTasks: Int,
    val pendingTasks: Int,
    val lastExecutedAt: LocalDateTime?,
    val averageExecutionTimeMs: Long?
)