# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DGP Lineage Collector is a Spring Boot application written in Kotlin that collects and analyzes data lineage from data exchange platforms and SQL scripts. It extracts table/column dependencies from SQL queries and transforms them into standardized lineage models.

## Build Commands

```bash
# Build the project
./mvnw clean compile

# Run tests
./mvnw test

# Run a single test class
./mvnw test -Dtest=SqlParserTest

# Package application
./mvnw clean package

# Run application
./mvnw spring-boot:run
```

## Architecture

### Core Package Structure
- `com.datayes.sql/` - SQL parsing and analysis
  - `SqlParser.kt` - SQL parsing engine using JSQLParser
  - `SqlParseResult.kt` - Parse result data structures
  - `SqlParsingException.kt` - SQL parsing error handling
- `com.datayes.lineage/` - Core lineage domain
  - `Lineage.kt` - Core immutable lineage data models
  - `LineageService.kt` - Business logic for lineage operations
  - `LineageRepository.kt` - Data access for lineage entities
  - `LineageController.kt` - REST API endpoints
- `com.datayes.dataexchange/` - Data exchange platform integration
  - `DataExchangeJobService.kt` - Business logic service layer
  - `DataExchangeJobRepository.kt` - Data access layer
  - `DataExchangeJobLineageConverter.kt` - Transforms jobs to lineage models
  - `DataExchangeJobDataSource.kt` - Secondary datasource configuration
- `com.datayes.task/` - Lineage task management
  - `LineageTaskService.kt` - Task management business logic
  - `LineageTaskRepository.kt` - Task data access layer
  - `LineageTaskController.kt` - Task REST API endpoints
- `com.datayes.shell/` - Shell script parsing for Hive SQL extraction
- `com.datayes.git/` - Git repository utilities

### Key Design Patterns
- **Functional Core, Imperative Shell** - Core parsing logic is pure, side effects in shell
- **Data-Oriented Design** - Immutable Kotlin data classes throughout
- **Dual Datasource Configuration** - Separate connections for main app and data exchange platform

### Database Configuration
The application uses two MySQL datasources:
- Main database: `***********:3306/dgp` 
- Data Exchange platform: `***********:3306/dc_data_exc`

Configuration is in `DataExchangeJobDataSource.kt` with separate `JdbcTemplate` beans.

### SQL Parsing Engine
The `SqlParser` class handles complex SQL scenarios:
- JOIN operations with table aliases
- Subqueries and nested selects  
- Wildcard column selection (`*`)
- Multiple database dialects via JDBC URL parsing

## Testing

Tests use JUnit 5 with minimal Mockito usage. Key test classes:
- `SqlParserTest.kt` - Comprehensive SQL parsing scenarios
- `LineageConverterTest.kt` - Lineage transformation logic
- `DataModelTest.kt` - Data model validation
- `ShellScriptParserTest.kt` - Shell script parsing tests
- `GitUtilsTest.kt` - Git utility function tests
- `HdfsReaderTest.kt` - HDFS connectivity and file operations

### Testing Philosophy
- **Use mocks as little as possible** - Prefer real implementations and actual services
- **Integration/E2E tests** - Use the real development server, not mocks
- **Database testing** - Never delete or update data before/after tests; assume data exists
- **Real services** - Test against actual databases, HDFS clusters, and external APIs when possible
- **Mocks only when necessary** - Use mocks only for external dependencies that cannot be easily controlled or are expensive to set up

### Configuration Table Design Pattern
For small configuration tables (low row count, rarely changing data):
- **Entity Design** - Use Kotlin immutable data classes with Spring Data JDBC annotations
- **Repository Pattern** - Create repository interfaces extending Spring Data JDBC `CrudRepository` for auto-generated query methods
- **Testable Functions** - Design service functions to accept `List<ConfigEntity>` parameters instead of injecting repositories directly
- **Testing Benefits** - This approach enables easy unit testing without database dependencies by passing test data as lists
- **Example**: `HdfsDatasourceMapping` entity with `HdfsDatasourceMappingRepository` and functions accepting `List<HdfsDatasourceMapping>`

### REST API Integration Testing Rules
- **Integration test naming** - Integration test classes must end with `IT` suffix (e.g., `LineageSystemsIT`)
- **Real HTTP requests only** - Always use RestAssured for actual HTTP calls, never mock controllers
- **External application startup** - Tests assume the application is already running, do NOT use @SpringBootTest
- **Manual application management** - Boot the application manually before running integration tests
- **Base class pattern** - Extend `RestApiIntegrationTestBase` which configures RestAssured base URL
- **RestAssured + AssertJ validation** - Use RestAssured for HTTP calls and AssertJ for complex data assertions
- **Multi-layered validation** - Validate HTTP status → response structure → data types → business logic
- **JsonPath for complex validation** - Use JsonPath extraction for detailed JSON response validation
- **Type-safe assertions** - Use explicit generic types to avoid Kotlin inference issues (e.g., `hasSize<Any>(greaterThan(0))`)
- **Unique log prefixes** - Each log message has a unique UUID prefix for traceability

## Technology Stack

- **Framework**: Spring Boot 3.4.4
- **Language**: Kotlin 1.9.25 with Java 17
- **Build**: Maven with wrapper
- **SQL Parser**: JSQLParser 5.2
- **Database**: MySQL (dual datasources)
- **JSON**: Jackson with Kotlin module
- **Big Data**: Hadoop 2.7.2-TBDS (HDFS file operations only)
- **Git**: Eclipse JGit 7.2.1
- **Testing**: JUnit 5, Mockito

## Error Handling Rules
- **Never hide errors from API users** - Do NOT catch exceptions and return empty results or success responses
- **Fail fast and explicitly** - Let exceptions propagate to controllers so proper HTTP error codes are returned
- **Log errors for debugging** - Log exceptions for internal debugging but still throw them for API consumers
- **Use meaningful error messages** - Provide clear error information to help users understand what went wrong
- **No silent fallbacks with default values** - Never create default/fallback values that mask configuration errors; throw exceptions instead to force proper configuration
- **Avoid `!!` operator for null assertions** - Use `error()` with descriptive messages instead of `foo!!` to make error messages more clear and intentional

## Code Simplicity Rules
- **Keep data structures minimal** - Only add fields that are actually needed and used
- **Avoid speculative features** - Don't add fields "just in case" they might be useful later
- **Remove unused fields** - If a field is not being used, remove it to keep the code clean
- **Simple is better than complex** - Prefer simple, focused data structures over complex ones with many optional fields

## REST API Design Rules
- **Use query parameters over path variables** - Prefer `@RequestParam` over `@PathVariable` for user input to avoid URL encoding issues
- **Avoid complex path variables** - File paths, IDs with special characters should be query parameters
- **Keep URLs simple** - Use `/api/resource/action?param=value` instead of `/api/resource/{complexParam}/action`

## Data Transfer Object (DTO) Design Rules
- **Avoid DTO proliferation** - The functional core pattern should reduce DTOs, not multiply them
- **Create DTOs only for actual data transformation** - Not for HTTP convenience or API wrapping
- **Consolidate duplicates** - Merge similar DTOs (TableInfoDto, TableLineageDto, TableNodeDto → single TableInfo)
- **Eliminate single-use DTOs** - Remove classes created for one specific endpoint or operation
- **No generic CRUD DTOs** - Avoid CreateXxxRequest, UpdateXxxRequest patterns; use domain-specific commands
- **Remove HTTP wrapper DTOs** - Use entities directly or create focused transformation classes
- **Avoid visualization over-engineering** - Use generic data structures instead of domain-specific graph DTOs
- **Keep entity classes separate** - Don't create DTOs that duplicate existing entity structures
- **Focus on core domain** - DTOs should represent actual business data transformations, not API convenience

### DTO Anti-patterns to Avoid:
- Multiple variations of the same concept (TableDto, TableInfoDto, TableDetailsDto, TableSummaryDto)
- Single-use visualization DTOs (EdgeVisualStyleDto, SuggestedPositionDto)
- Generic operation responses (OperationResponse, ApiResponse wrappers)
- Premature abstraction for "flexibility"

## Tooling for Shell Interactions
Is it about finding FILES? use `fd`  
Is it about finding TEXT/strings? use `rg`  
Is it about finding CODE STRUCTURE? use `ast-grep`  
Is it about SELECTING from multiple results? pipe to `fzf`  
Is it about interacting with JSON? use `jq`  
Is it about interacting with YAML or XML? use `yq`