package com.datayes.script

import com.datayes.auth.AuthUser
import java.time.LocalDateTime

/**
 * 脚本上传响应 (Script Upload Response)
 * 
 * UC-1: 脚本上传成功后的响应数据
 */
data class ScriptUploadResponse(
    val scriptId: Long,
    val scriptName: String,
    val scriptType: ScriptType,
    val fileSize: Long?,
    val analysisStatus: AnalysisStatus,
    val uploadUser: String,
    val createdAt: LocalDateTime
)

/**
 * 脚本摘要信息 (Script Summary)
 * 
 * UC-2: 脚本列表查询时返回的脚本摘要信息
 */
data class ScriptSummary(
    val id: Long,
    val scriptName: String,
    val scriptType: ScriptType,
    val fileSize: Long?,
    val uploadUser: String,
    val analysisStatus: AnalysisStatus,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val authUser: AuthUser?
)

/**
 * 脚本列表响应 (Script List Response)
 * 
 * UC-2: 分页脚本列表查询响应
 */
data class ScriptListResponse(
    val content: List<ScriptSummary>,
    val page: Int,
    val size: Int,
    val totalElements: Long,
    val totalPages: Int
)

/**
 * 脚本详情响应 (Script Detail Response)
 * 
 * 脚本详细信息查询响应，包含完整的脚本内容
 */
data class ScriptDetailResponse(
    val id: Long,
    val scriptName: String,
    val scriptType: ScriptType,
    val filePath: String?,
    val fileSize: Long?,
    val fileHash: String?,
    val uploadUser: String,
    val analysisStatus: AnalysisStatus,
    val analysisResult: String?,
    val temporaryLineageId: String?,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime
)

/**
 * 脚本影响分析响应 (Script Impact Analysis Response)
 * 
 * UC-3: 脚本影响分析结果响应
 */
data class ScriptImpactAnalysisResponse(
    val scriptId: Long,
    val scriptName: String,
    val scriptType: ScriptType,
    val analysisStatus: AnalysisStatus,
    val analysisResult: String,
    val temporaryLineageId: String?,
    val analyzedAt: LocalDateTime
)

/**
 * 脚本分析触发响应 (Script Analysis Trigger Response)
 * 
 * 手动触发脚本分析的响应
 */
data class ScriptAnalysisTriggerResponse(
    val scriptId: Long,
    val message: String,
    val triggeredAt: LocalDateTime
) 