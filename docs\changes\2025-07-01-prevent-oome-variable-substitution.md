### What was changed

Introduced a **safety threshold** inside `ShellScriptParser.substituteVariables` to abort variable substitution once the intermediate or final string exceeds **~50 MB**.

Key modifications:

1. Added `maxOutputLength` parameter (default **50 000 000** chars) to the method signature.
2. Implemented pre-check and post-check guards that:
   • Skip further substitution when the current string already exceeds the threshold.
   • Truncate and return a partially-substituted result if a single replacement pass crosses the threshold.
3. Logged detailed warnings with unique trace IDs (`9ecb12d0`, `1f5a7d34`) to simplify production diagnostics.

### Why the change was necessary

A `java.lang.OutOfMemoryError` surfaced in production when variable substitution spiralled, creating strings close to **2 GB**. The previous optimisation (capping iterations) wasn't sufficient for self-referential or exponentially expanding variables. The new guard prevents runaway memory usage.

### How it was implemented

- Extended the function signature while keeping full binary compatibility through a default value.
- Inserted **early-exit** checks before and after each replacement cycle.
- When the post-check detects an oversize result, the method now **truncates** the string to the threshold and returns immediately, ensuring downstream components can still process a reasonably sized string.

### Follow-up tasks / TODOs

- Consider analysing variable graphs up-front to detect self-references and short-circuit even earlier.
- Monitor heap usage; adjust `maxOutputLength` or make it externally configurable if needed. 