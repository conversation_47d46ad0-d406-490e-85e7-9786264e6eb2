package com.datayes.storage

import org.springframework.data.repository.CrudRepository
import org.springframework.stereotype.Repository

/**
 * S3配置数据访问层 (S3 Configuration Repository)
 */
@Repository
interface S3ConfigRepository : CrudRepository<S3Config, Long> {
    
    /**
     * 根据配置名称查找S3配置 (Find S3 config by config name)
     */
    fun findByConfigName(configName: String): S3Config?
    
    /**
     * 查找活跃的S3配置 (Find active S3 configurations)
     */
    fun findByIsActiveTrue(): List<S3Config>
    
    /**
     * 查找默认的活跃配置 (Find default active configuration)
     */
    fun findByConfigNameAndIsActiveTrue(configName: String): S3Config?
}