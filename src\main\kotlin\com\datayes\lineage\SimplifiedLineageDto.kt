package com.datayes.lineage

/**
 * 简化的血缘管理数据传输对象 (Simplified Lineage Management DTOs)
 * 
 * 提供更直观的CRUD操作接口，自动处理数据源、表和列的创建
 */

/**
 * 列映射详情 (Column Mapping Details)
 */
data class ColumnMappingRequest(
    val sourceColumnName: String,              // 源列名
    val sourceDataType: String? = null,        // 源列数据类型
    val sourceColumnComment: String? = null,   // 源列注释
    val sourceIsPrimaryKey: Boolean? = null,   // 源列是否为主键
    val sourceIsNullable: Boolean? = null,     // 源列是否可为空
    val sourceDefaultValue: String? = null,    // 源列默认值
    val sourceColumnOrder: Int? = null,        // 源列顺序

    val targetColumnName: String,              // 目标列名
    val targetDataType: String? = null,        // 目标列数据类型
    val targetColumnComment: String? = null,   // 目标列注释
    val targetIsPrimaryKey: Boolean? = null,   // 目标列是否为主键
    val targetIsNullable: Boolean? = null,     // 目标列是否可为空
    val targetDefaultValue: String? = null,    // 目标列默认值
    val targetColumnOrder: Int? = null         // 目标列顺序
)

/**
 * 表引用信息 (Table Reference Information)
 * 支持两种方式指定表：1) 通过详细信息自动创建 2) 通过已存在的databaseId和tableId直接引用
 */
data class TableReference(
    // 方式1：通过详细信息自动创建表
    val datasourceName: String? = null,         // 数据源名称
    val dbType: String? = null,                 // 数据库类型
    val host: String? = null,                   // 主机
    val port: Int? = null,                      // 端口
    val databaseName: String? = null,           // 数据库名
    val customJdbcUrl: String? = null,          // 自定义JDBC URL，如果提供则从中解析host和port
    val schemaName: String? = null,             // schema名
    val tableName: String? = null,              // 表名

    // 方式2：通过已存在的ID直接引用
    val databaseId: Long? = null,               // 数据库ID（已存在）
    val tableId: Long? = null                   // 表ID（已存在）
) {
    /**
     * 验证表引用是否有效
     * 必须使用两种方式之一：详细信息创建 或 ID引用
     */
    fun isValid(): Boolean {
        val hasDetailedInfo = datasourceName != null && dbType != null && databaseName != null && tableName != null
        val hasIdReference = databaseId != null && tableId != null
        return hasDetailedInfo || hasIdReference
    }

    /**
     * 是否使用ID引用方式
     */
    fun isIdReference(): Boolean = databaseId != null && tableId != null

    /**
     * 是否使用详细信息创建方式
     */
    fun isDetailedReference(): Boolean = datasourceName != null && dbType != null && databaseName != null && tableName != null
}

/**
 * 创建血缘关系请求 (Create Lineage Relationship Request)
 */
data class CreateLineageRequest(
    // 源表信息 - 支持两种方式
    val source: TableReference,

    // 目标表信息 - 支持两种方式  
    val target: TableReference,

    // 列映射
    val columns: List<ColumnMappingRequest> = emptyList(), // 列映射列表

    // 操作信息
    val updateBy: String                      // 操作人
)

/**
 * 更新血缘关系请求 (Update Lineage Relationship Request)
 */
data class UpdateLineageRequest(
    val tableRelationshipId: Long,            // 表级血缘关系ID
    val columns: List<ColumnMappingRequest> = emptyList(), // 列映射列表
    val updateBy: String                      // 操作人
)

/**
 * 删除血缘关系请求 (Delete Lineage Relationship Request)
 */
data class DeleteLineageRequest(
    val tableRelationshipIdList: List<Long>   // 表级血缘关系ID列表
)

/**
 * 血缘操作响应 (Lineage Operation Response)
 */
data class LineageOperationResponse(
    val success: Boolean,                     // 操作是否成功
    val message: String,                      // 操作消息
    val tableRelationshipId: Long? = null,   // 表级血缘关系ID（创建时返回）
    val affectedTableRelationships: Int = 0, // 影响的表级关系数
    val affectedColumnRelationships: Int = 0, // 影响的列级关系数
    val errors: List<String> = emptyList(),   // 错误信息列表
    val warnings: List<String> = emptyList()  // 警告信息列表
)

/**
 * 血缘关系详情响应 (Lineage Relationship Details Response)
 */
data class LineageRelationshipDetails(
    val tableRelationshipId: Long,           // 表级血缘关系ID
    val sourceDatasourceName: String,        // 源数据源名称
    val sourceTableName: String,             // 源表名
    val sourceSchemaName: String? = null,    // 源schema名
    val targetDatasourceName: String,        // 目标数据源名称
    val targetTableName: String,             // 目标表名
    val targetSchemaName: String? = null,    // 目标schema名
    val columns: List<ColumnMappingDetails>,  // 列映射详情
    val createdBy: String? = null,           // 创建人
    val createdAt: String? = null,           // 创建时间
    val lastUpdatedBy: String? = null,       // 最后更新人
    val lastUpdatedAt: String? = null        // 最后更新时间
)

/**
 * 列映射详情 (Column Mapping Details)
 */
data class ColumnMappingDetails(
    val columnRelationshipId: Long,          // 列级血缘关系ID
    val sourceColumnName: String,            // 源列名
    val sourceDataType: String? = null,      // 源列数据类型
    val targetColumnName: String,            // 目标列名
    val targetDataType: String? = null       // 目标列数据类型
)