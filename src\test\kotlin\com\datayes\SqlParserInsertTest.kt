package com.datayes

import com.datayes.sql.SqlParser
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName

class SqlParserInsertTest {

    @Test
    @DisplayName("Should parse INSERT INTO SELECT statement with complex subqueries")
    fun testParseInsertIntoSelectWithComplexSubqueries() {
        // Given
        val sqlQuery = """
            insert into urp_bui_prip.temp_lcpoltransaction
            select * from 
            (select 
            row_number() over(partition by a.busino order by a.busino) rownum1,
            a.* from (select 
             concat(
                (case
                               when length(nvl(trans.policyno,''))>0 then
                                trans.policyno
                               when length(nvl(trans.grppolicyno,''))>0 then
                                trans.grppolicyno
                   else ''
                            end),
                (case
                when trans.btype = '02' then  
                             trans.PayNo
                            when trans.btype in ('04') then 
                             trans.EdorAcceptNo
                            when trans.btype in ('05','98') then 
                             trans.ClaimNo
                when concat(trans.btype,trans.bdtype) in ('0701','1001') then 
                             trans.ActuGetNo
                when concat(trans.btype,trans.bdtype) = '0704' then 
                             trans.EdorAcceptNo
                when trans.btype = '08' then 
                 trans.divyear
                when trans.btype = '09' then 
                             trans.tempfeeno
                when trans.btype = '14' then 
                             trans.ReInsuranceContNo
                when trans.btype = '16' then
                 concat(trans.WDNo,trans.EdorAcceptNo,trans.TransFromRegion)
                             when concat(trans.btype,trans.bdtype) in ('1801','2005') then
                 'ch'
                 else ''
                          end),
                          from_unixtime(unix_timestamp(trans.TransDate,'yyyy-MM-dd'),'yyyyMMdd'),
                          trans.btype, trans.bdtype) busino,
                   trans.companycode companycode,
                   trans.grppolicyno grppolicyno,
                   trans.policyno policyno,
                   (case when trans.gpflag = '__HIVE_DEFAULT_PARTITION__' then '' else trans.gpflag end) gpflag,
                mapping.targetcode busstype,
                (case 
               when trans.btype in( '01','02')
                then '100'
               when trans.btype = '05'
                then '200'
               when trans.btype in('07','09','17')
                then '300'
               when trans.btype = '18'
                then '400'
               when trans.btype = '04'
                then '500'
               else '999'
                end) TransTypeCorrBussType,
                   nvl(trans.transMoney,0) TransAmnt,
                   trans.TransDate TransDate,
                    (case when length(nvl(trans.AccNo,''))=0 then null else trans.AccBank end ) BankCode,
                   (case when length(nvl(trans.AccNo,''))=0 then null else trans.AccBankName end) BankName,
                   (case when length(nvl(trans.AccBankName,''))=0 or length(nvl(trans.AccBank,''))=0 then NULL else trans.AccNo end)BankAccNo,
                   trans.AccName AccName,
                (case when trans.PayWay='01' then ljap.CertType when trans.PayWay='02' then lget.CertType else '' end) CertType,
                (case when trans.PayWay='01' then ljap.CertNo when trans.PayWay='02' then lget.CertNo else '' end) CertNo, 
                   trans.ClaimNo ClaimNo,
                   trans.EdorAcceptNo EndorAcceptNo,
                   trans.EdorNo EndorsementNo,
                (case when length(nvl(trans.grppolicyno,''))>0 then lpgr.AppPolDate when length(nvl(trans.policyno,''))>0 then lped.AppPolDate else '' end) EndorsementApplicationDate,
                (case when length(nvl(trans.grppolicyno,''))>0 then '' when length(nvl(trans.policyno,''))>0 then lped.ConsumerInitiatedFlag else '' end) ConsumerInitiatedFlag,
                (case when length(nvl(trans.grppolicyno,''))>0 then '' when length(nvl(trans.policyno,''))>0 then '01' else '' end) PreserveAppCode,
                   '' DataField1,
                   '' DataField2,
                trans.TempfeeNo TempfeeNo,
                trans.ActuGetNo ActuGetNo,
                trans.ReInsuranceContNo ReInsuranceContNo,
                trans.ReInsurerCode ReInsurerCode,
                trans.payno payno,
                trans.ClaimBackDate ClaimBackDate,
                trans.btype,
                trans.bdtype,
                date_add(transdate,1) pushdate
              from (select * from  urp_mid.URM_TransInfo  where TransDate = date_add(current_timestamp, -1) 
              and concat(btype,bdtype) ='9801') trans
              inner join urp_bui_prip.ldcodemapping mapping on mapping.basiccode = concat(trans.btype, trans.bdtype) and mapping.codetype = 'pr_busstype' 
              inner join (select ClaimNo from urp_mid.urm_llclaimpolicy where TransDate = date_add(current_timestamp, -1) and IsDeleted <> '1') llcla on llcla.ClaimNo = trans.ClaimNo
              left join (select lja.CertType, lja.CertNo, lja.PolicyNo,lja.GrpPolicyNo
                           from urp_mid.URM_LJAPolPay lja where lja.TransDate = date_add(current_timestamp, -1) and lja.IsDeleted <> '1') ljap
                on ljap.PolicyNo = trans.policyno and ljap.GrpPolicyNo = trans.grppolicyno and length(nvl(ljap.PolicyNo,''))>0 and length(nvl(ljap.GrpPolicyNo,''))>0
              left join (select lge.CertType, lge.CertNo, lge.PolicyNo,lge.GrpPolicyNo,lge.btype,lge.bdtype
                           from urp_mid.URM_LJAPolGet lge where lge.TransDate = date_add(current_timestamp, -1) and lge.IsDeleted <> '1') lget
                on lget.PolicyNo = trans.policyno and lget.GrpPolicyNo = trans.grppolicyno and lget.btype = trans.btype and lget.bdtype = trans.bdtype and length(nvl(lget.PolicyNo,''))>0 and length(nvl(lget.GrpPolicyNo,''))>0 and length(nvl(lget.btype,''))>0 and length(nvl(lget.bdtype,''))>0
              left join (select lpe.AppPolDate,
                                lpe.ConsumerInitiatedFlag,
                                lpe.AppPolType,
                                lpe.EdorAcceptNo
                           from urp_mid.URM_LPEdoritem lpe where lpe.TransDate = date_add(current_timestamp, -1) and lpe.IsDeleted <> '1') lped
                on lped.EdorAcceptNo = trans.EdorAcceptNo and length(nvl(lped.EdorAcceptNo,''))>0 
              left join (select lpg.AppPolDate,
                                lpg.EdorAcceptNo
                           from urp_mid.URM_LPGrpEdoritem lpg where lpg.TransDate = date_add(current_timestamp, -1) and lpg.IsDeleted <> '1') lpgr
                on lpgr.EdorAcceptNo = trans.EdorAcceptNo and length(nvl(lpgr.EdorAcceptNo,''))>0 ) a ) b where b.rownum1 = 1
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("temp_lcpoltransaction")
        assertThat(result.targetTable.schemaOrDatabase).isEqualTo("urp_bui_prip")

        // Verify source tables are identified
        assertThat(result.sourceTables).isNotEmpty

        // Check if main source tables are found
        val sourceTableNames = result.sourceTables.map { "${it.schemaOrDatabase}.${it.name}" }
        assertThat(sourceTableNames).contains("urp_mid.URM_TransInfo")
        assertThat(sourceTableNames).contains("urp_bui_prip.ldcodemapping")
        assertThat(sourceTableNames).contains("urp_mid.urm_llclaimpolicy")
        assertThat(sourceTableNames).contains("urp_mid.URM_LJAPolPay")
        assertThat(sourceTableNames).contains("urp_mid.URM_LJAPolGet")
        assertThat(sourceTableNames).contains("urp_mid.URM_LPEdoritem")
        assertThat(sourceTableNames).contains("urp_mid.URM_LPGrpEdoritem")

        // Verify column mappings
        assertThat(result.columnMappings).isNotEmpty

        println("82a1f4e3 | Successfully parsed INSERT INTO SELECT statement")
        println("82a1f4e3 | Target table: ${result.targetTable.schemaOrDatabase}.${result.targetTable.name}")
        println("82a1f4e3 | Source tables count: ${result.sourceTables.size}")
        println("82a1f4e3 | Column mappings count: ${result.columnMappings.size}")
    }

    @Test
    @DisplayName("Should parse simple INSERT INTO SELECT statement")
    fun testParseSimpleInsertIntoSelect() {
        // Given
        val sqlQuery = """
            INSERT INTO target_db.target_table 
            SELECT * FROM source_db.source_table
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("target_table")
        assertThat(result.targetTable.schemaOrDatabase).isEqualTo("target_db")

        // Verify source tables
        assertThat(result.sourceTables).hasSize(1)
        assertThat(result.sourceTables[0].name).isEqualTo("source_table")
        assertThat(result.sourceTables[0].schemaOrDatabase).isEqualTo("source_db")

        // Verify column mappings
        assertThat(result.columnMappings).hasSize(1)
        assertThat(result.columnMappings[0].sourceColumn.name).isEqualTo("*")
        assertThat(result.columnMappings[0].sourceColumn.isWildcard).isTrue
    }

    @Test
    @DisplayName("Should parse INSERT INTO SELECT with multiple JOINs")
    fun testParseInsertIntoSelectWithJoins() {
        // Given
        val sqlQuery = """
            INSERT INTO analytics.user_summary
            SELECT u.id, u.name, p.total_amount
            FROM users.user_profiles u
            INNER JOIN orders.purchases p ON u.id = p.user_id
            LEFT JOIN loyalty.rewards r ON u.id = r.user_id
            WHERE u.active = 1
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("user_summary")
        assertThat(result.targetTable.schemaOrDatabase).isEqualTo("analytics")

        // Verify source tables are identified
        assertThat(result.sourceTables).isNotEmpty
        val sourceTableNames = result.sourceTables.map { "${it.schemaOrDatabase}.${it.name}" }
        assertThat(sourceTableNames).contains("users.user_profiles")
        assertThat(sourceTableNames).contains("orders.purchases")
        assertThat(sourceTableNames).contains("loyalty.rewards")
    }

    @Test
    @DisplayName("Should parse INSERT INTO SELECT with ROW_NUMBER window function and subquery")
    fun testParseInsertIntoSelectWithRowNumberAndSubquery() {
        // Given
        val sqlQuery = """
            insert into urp_bui_prip.temp_LCProduct_03
            select a.* from (
                select name, age
                from b
            ) a
            """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("temp_LCProduct_03")
        assertThat(result.targetTable.schemaOrDatabase).isEqualTo("urp_bui_prip")

        // Verify source tables are identified (table b)
        assertThat(result.sourceTables).hasSize(1)
        assertThat(result.sourceTables[0].name).isEqualTo("b")

        // Verify that a.* was expanded to the specific columns from the subquery
        assertThat(result.sourceColumns).hasSize(2)
        
        // Check that we have the expanded columns: name and age
        val columnNames = result.sourceColumns.map { it.name }
        assertThat(columnNames).containsExactly("name", "age")
        
        // Verify the columns have the correct table prefix 'a' (the subquery alias)
        result.sourceColumns.forEach { column ->
            assertThat(column.tablePrefix).isEqualTo("a")
            assertThat(column.isWildcard).isFalse()
        }
        
        // Verify column mappings
        assertThat(result.columnMappings).hasSize(2)
        assertThat(result.columnMappings[0].sourceColumn.name).isEqualTo("name")
        assertThat(result.columnMappings[1].sourceColumn.name).isEqualTo("age")
        
        println("7b9f2a81 | Successfully parsed INSERT with subquery alias expansion")
        println("7b9f2a81 | Expanded a.* to columns: ${'$'}columnNames")
        println("7b9f2a81 | Column mappings: ${'$'}{result.columnMappings.map { it.sourceColumn.name }}")
    }
    
    @Test
    @DisplayName("Should extract detailed column lineage from complex LCProduct SQL")
    fun testParseComplexLCProductColumnLineage() {
        // Given - Read SQL from test resources
        val sqlContent = this::class.java.classLoader
            .getResourceAsStream("shell/urp_bui_prip.temp_LCProduct_03.sql")
            ?.bufferedReader()
            ?.use { it.readText() }
            ?: throw IllegalStateException("Could not find SQL file in test resources")

        // When
        val result = SqlParser.parseDataModification(sqlContent)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("temp_LCProduct_03")
        assertThat(result.targetTable.schemaOrDatabase).isEqualTo("urp_bui_prip")

        // Verify source tables are identified
        assertThat(result.sourceTables).isNotEmpty
        val sourceTableNames = result.sourceTables.map { "${it.schemaOrDatabase}.${it.name}" }
        
        // Check main source tables
        assertThat(sourceTableNames).contains("urp_dws.full_lcpol")
        assertThat(sourceTableNames).contains("urp_bui_prip.temp_lcpoltransaction")
        assertThat(sourceTableNames).contains("urp_bui_prip.busstype")
        assertThat(sourceTableNames).contains("urp_dws.full_trans_cashvalue")

        // Verify we have column mappings for the complex transformations
        assertThat(result.columnMappings).isNotEmpty
        
        // Verify specific column lineage examples
        val columnMappings = result.columnMappings.associateBy { it.targetColumnName }
        
        // Check some key column mappings
        assertThat(columnMappings).containsKey("busino")
        assertThat(columnMappings).containsKey("CompanyCode") 
        assertThat(columnMappings).containsKey("PolicyNo")
        assertThat(columnMappings).containsKey("ProductNo")
        assertThat(columnMappings).containsKey("GPFlag")
        
        // Verify complex transformations are captured
        val businoMapping = columnMappings["busino"]
        assertThat(businoMapping?.sourceColumn?.tablePrefix).isEqualTo("trans")
        
        val companyCodeMapping = columnMappings["CompanyCode"]
        assertThat(companyCodeMapping?.sourceColumn?.name).isEqualTo("CompanyCode")
        assertThat(companyCodeMapping?.sourceColumn?.originalExpression).contains("'000052'")
        
        val policyNoMapping = columnMappings["PolicyNo"]
        assertThat(policyNoMapping?.sourceColumn?.tablePrefix).isEqualTo("lcp")
        assertThat(policyNoMapping?.sourceColumn?.name).isEqualTo("PolicyNo")
        
        // Verify CASE expression mappings
        val grpPolicyNoMapping = columnMappings["GrpPolicyNo"]
        assertThat(grpPolicyNoMapping?.sourceColumn?.originalExpression).contains("case when")
        
        // Verify function call mappings like substr(lcp.ManageCom,1,4)
        val manageComMapping = columnMappings["ManageCom"]
        assertThat(manageComMapping?.sourceColumn?.originalExpression).contains("substr")
        
        // Verify CAST expressions
        val premiumMapping = columnMappings["Premium"]
        assertThat(premiumMapping?.sourceColumn?.originalExpression).contains("cast")
        assertThat(premiumMapping?.sourceColumn?.originalExpression).contains("decimal")
        
        // Verify JOIN-based columns
        val cashValueMapping = columnMappings["CashValue"]
        assertThat(cashValueMapping?.sourceColumn?.tablePrefix).isEqualTo("cash")
        
        println("4c7d9a12 | Successfully parsed complex LCProduct SQL")
        println("4c7d9a12 | Source tables: $sourceTableNames")
        println("4c7d9a12 | Total column mappings: ${result.columnMappings.size}")
        println("4c7d9a12 | Sample column lineages:")
        listOf("busino", "CompanyCode", "PolicyNo", "GrpPolicyNo", "ManageCom", "Premium", "CashValue")
            .forEach { colName ->
                val mapping = columnMappings[colName]
                if (mapping != null) {
                    println("4c7d9a12 |   $colName <- ${mapping.sourceColumn.originalExpression}")
                }
            }
    }
}