package com.datayes.shell

import com.datayes.script.ScriptAnalysisService
import com.datayes.script.ScriptType
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

/**
 * Unit tests for ScriptAnalysisService parsing logic
 */
class ScriptAnalysisServiceTest {

    @Test
    fun `should parse shell script and extract table lineage from dwd_billing_accountcode script`() {
        // 4a1d8f9e | 读取测试用的shell脚本文件
        val scriptContent = this::class.java.getResource("/shell/dwd_billing_accountcode.sh")!!.readText()

        // 4a1d8f9e | 调用纯函数进行脚本解析
        val result = ScriptAnalysisService.parseScriptToLineage(
            scriptContent = scriptContent,
            scriptType = ScriptType.SHELL,
            scriptName = "dwd_billing_accountcode.sh"
        )

        // 4a1d8f9e | 验证解析成功
        assertThat(result.success).isTrue()
        assertThat(result.errors).isEmpty()

        // 4a1d8f9e | 验证提取到SQL语句
        assertThat(result.consolidatedSql).isNotEmpty()
        assertThat(result.consolidatedSql).contains("insert overwrite table dwd.dwd_billing_accountcode")

        // 4a1d8f9e | 验证表血缘信息
        assertThat(result.sourceTables).isNotEmpty()
        assertThat(result.targetTables).isNotEmpty()

        // 4a1d8f9e | 验证目标表
        assertThat(result.targetTables).hasSize(1)
        val targetTable = result.targetTables[0]
        assertThat(targetTable.schemaOrDatabase).isEqualTo("dwd")
        assertThat(targetTable.name).isEqualTo("dwd_billing_accountcode")

        // 4a1d8f9e | 验证源表
        assertThat(result.sourceTables).hasSizeGreaterThan(0)
        val sourceTableNames = result.sourceTables.map { "${it.schemaOrDatabase}.${it.name}" }
        assertThat(sourceTableNames).contains("ms_ods_ebs.hpods_coa")
        assertThat(sourceTableNames).contains("dwd.dim_dictionary")

        // 4a1d8f9e | 验证列映射信息
        assertThat(result.columnMappings).isNotEmpty()

        // 4a1d8f9e | 验证可能的警告信息（如果有的话）
        println("4a1d8f9e | Warnings: ${result.warnings}")
        println("4a1d8f9e | Source tables found: $sourceTableNames")
        println("4a1d8f9e | Target table: ${targetTable.schemaOrDatabase}.${targetTable.name}")
        println("4a1d8f9e | Column mappings count: ${result.columnMappings.size}")
    }

    @Test
    fun `should handle empty SQL script gracefully`() {
        // 4a1d8f9e | 测试空SQL脚本
        val result = ScriptAnalysisService.Companion.parseScriptToLineage(
            scriptContent = "-- This is just a comment\n-- No actual SQL here",
            scriptType = ScriptType.SQL,
            scriptName = "empty.sql"
        )

        // 4a1d8f9e | 验证解析失败但不报错
        assertThat(result.success).isFalse()
        assertThat(result.warnings).contains("未从脚本中提取到任何SQL语句")
        assertThat(result.sourceTables).isEmpty()
        assertThat(result.targetTables).isEmpty()
        assertThat(result.errors).isEmpty()
    }

    @Test
    fun `should parse simple INSERT SQL statement`() {
        // 4a1d8f9e | 测试简单的INSERT语句
        val sqlContent = """
            INSERT INTO target_db.target_table 
            SELECT col1, col2, col3 
            FROM source_db.source_table 
            WHERE status = 'active';
        """.trimIndent()

        val result = ScriptAnalysisService.Companion.parseScriptToLineage(
            scriptContent = sqlContent,
            scriptType = ScriptType.SQL,
            scriptName = "simple_insert.sql"
        )

        // 4a1d8f9e | 验证解析成功
        assertThat(result.success).isTrue()
        assertThat(result.errors).isEmpty()

        // 4a1d8f9e | 验证表血缘
        assertThat(result.targetTables).hasSize(1)
        assertThat(result.sourceTables).hasSize(1)

        val targetTable = result.targetTables[0]
        assertThat(targetTable.schemaOrDatabase).isEqualTo("target_db")
        assertThat(targetTable.name).isEqualTo("target_table")

        val sourceTable = result.sourceTables[0]
        assertThat(sourceTable.schemaOrDatabase).isEqualTo("source_db")
        assertThat(sourceTable.name).isEqualTo("source_table")

        // 4a1d8f9e | 验证列映射
        assertThat(result.columnMappings).isNotEmpty()
    }

    @Test
    fun `should handle malformed SQL gracefully`() {
        // 4a1d8f9e | 测试格式错误的SQL
        val malformedSql = """
            SELCT * FORM table_name WHRE condition = 'value';
        """.trimIndent()

        val result = ScriptAnalysisService.parseScriptToLineage(
            scriptContent = malformedSql,
            scriptType = ScriptType.SQL,
            scriptName = "malformed.sql"
        )

        // 4a1d8f9e | 验证处理结果
        assertThat(result.success).isFalse()
        assertThat(result.warnings).isNotEmpty()
    }

    @Test
    fun `should parse dwd_insure_lccont_grp script and extract complex joins`() {
        // 8a4d9f2e | 读取dwd_insure_lccont_grp.sh脚本文件
        val scriptContent = this::class.java.getResource("/shell/dwd_insure_lccont_grp.sh")!!.readText()

        // 8a4d9f2e | 解析脚本获取血缘信息
        val result = ScriptAnalysisService.parseScriptToLineage(
            scriptContent = scriptContent,
            scriptType = ScriptType.SHELL,
            scriptName = "dwd_insure_lccont_grp.sh"
        )

        // 8a4d9f2e | 验证解析成功
        assertThat(result.success).isTrue()
        assertThat(result.errors).isEmpty()

        // 8a4d9f2e | 验证目标表
        assertThat(result.targetTables).hasSize(1)
        val targetTable = result.targetTables[0]
        assertThat(targetTable.schemaOrDatabase).isEqualTo("dwd")
        assertThat(targetTable.name).isEqualTo("dwd_insure_lccont_grp")

        // 8a4d9f2e | 验证源表包含预期的表
        val sourceTableNames = result.sourceTables.map { "${it.schemaOrDatabase}.${it.name}" }
        assertThat(sourceTableNames).contains("ms_ods_lis.full_lccont")
        assertThat(sourceTableNames).contains("dorado_ins.full_trade_management_agency")
        assertThat(sourceTableNames).contains("dwd.edw_bankname")
        assertThat(sourceTableNames).contains("ms_ods_cms.lacom")
        assertThat(sourceTableNames).contains("ms_ods_lis.full_lcelectronicsignresult")
        assertThat(sourceTableNames).contains("dwd.edw_address")
        assertThat(sourceTableNames).contains("ms_ods_lis.full_lccontstate")
        assertThat(sourceTableNames).contains("ms_ods_lis.full_lcpol")
        assertThat(sourceTableNames).contains("ms_ods_lis.full_ljapayperson")
        assertThat(sourceTableNames).contains("ms_ods_lis.full_lpcont")
        assertThat(sourceTableNames).contains("dwd.step01_insure_lccont_grp")
        assertThat(sourceTableNames).contains("dwd.step02_insure_lccont_grp")
        assertThat(sourceTableNames).contains("ms_ods_lis.full_lcgrpcont")

        // 8a4d9f2e | 验证SQL语句结构
        assertThat(result.consolidatedSql).contains("insert overwrite table dwd.dwd_insure_lccont_grp")
        assertThat(result.consolidatedSql).contains("from ms_ods_lis.full_lccont a")
        assertThat(result.consolidatedSql).contains("left join dorado_ins.full_trade_management_agency b")
        assertThat(result.consolidatedSql).contains("left join dwd.edw_bankname c")
        assertThat(result.consolidatedSql).contains("left join ms_ods_cms.lacom d")

        // 8a4d9f2e | 验证Hive优化设置被正确过滤掉（不应该在consolidatedSql中出现）
        assertThat(result.consolidatedSql).doesNotContain("set hive.optimize.skewjoin=true")
        assertThat(result.consolidatedSql).doesNotContain("set hive.groupby.skewindata = true")
        assertThat(result.consolidatedSql).doesNotContain("set hive.cbo.enable=true")
        
        // 8a4d9f2e | 验证警告信息包含有关过滤Hive配置语句的信息
        val warningText = result.warnings.joinToString(" ")
        assertThat(warningText).containsAnyOf("过滤掉Hive配置语句", "过滤掉", "个Hive配置语句")

        // 8a4d9f2e | 验证复杂的列映射
        assertThat(result.columnMappings).isNotEmpty()
        
        // 8a4d9f2e | 验证特定的列映射关键字
        assertThat(result.consolidatedSql).contains("a.prtno")
        assertThat(result.consolidatedSql).contains("as proposal_no")
        assertThat(result.consolidatedSql).contains("a.contno")
        assertThat(result.consolidatedSql).contains("as contract_no")
        assertThat(result.consolidatedSql).contains("a.grpcontno")
        assertThat(result.consolidatedSql).contains("as group_contract_no")

        // 8a4d9f2e | 验证子查询和窗口函数
        assertThat(result.consolidatedSql).contains("count(1) over(partition by a.prtno)")
        assertThat(result.consolidatedSql).contains("row_number() over(partition by")

        // 8a4d9f2e | 输出调试信息
        println("8a4d9f2e | Target table: ${targetTable.schemaOrDatabase}.${targetTable.name}")
        println("8a4d9f2e | Source tables count: ${sourceTableNames.size}")
        println("8a4d9f2e | Unique source tables: ${sourceTableNames.toSet().size}")
        println("8a4d9f2e | Column mappings found: ${result.columnMappings.size}")
        println("8a4d9f2e | First 5 source tables: ${sourceTableNames.take(5)}")
    }

    @Test
    @Disabled("Not yet implemented") // todo wujie
    fun `should parse shell script with multiple SQL statements`() {
        // 4a1d8f9e | 测试包含多个SQL语句的shell脚本
        val shellContent = """
            #!/bin/bash
            export table_name="test_table"
            
            hive -e "
            CREATE TABLE IF NOT EXISTS temp.staging_table AS
            SELECT * FROM source.raw_data;
            "
            
            hive -e "
            INSERT OVERWRITE TABLE final.processed_table
            SELECT 
                id,
                name,
                processed_date
            FROM temp.staging_table
            WHERE processed_date >= '2024-01-01';
            "
        """.trimIndent()

        val result = ScriptAnalysisService.parseScriptToLineage(
            scriptContent = shellContent,
            scriptType = ScriptType.SHELL,
            scriptName = "multi_sql.sh"
        )

        // 4a1d8f9e | 验证解析成功
        assertThat(result.success).isTrue()
        assertThat(result.errors).isEmpty()

        // 4a1d8f9e | 验证提取了多个表
        assertThat(result.sourceTables).isNotEmpty()
        assertThat(result.targetTables).isNotEmpty()

        // 4a1d8f9e | 验证包含期望的表名
        val allTableNames = (result.sourceTables + result.targetTables).map { "${it.schemaOrDatabase}.${it.name}" }
        assertThat(allTableNames).contains("source.raw_data")
        assertThat(allTableNames).contains("final.processed_table")

        println("4a1d8f9e | Found tables: $allTableNames")
    }

    @Test
    fun `should parse UPDATE statement correctly`() {
        // Given
        val sqlContent = """
            update foo set age = 1
        """.trimIndent()

        // When
        val result = ScriptAnalysisService.parseScriptToLineage(
            scriptContent = sqlContent,
            scriptType = ScriptType.SQL,
            scriptName = "update_test.sql"
        )

        // Then
        assertThat(result.success).isTrue()
        assertThat(result.errors).isEmpty()

        // Verify target table (UPDATE modifies the table)
        assertThat(result.targetTables).hasSize(1)
        val targetTable = result.targetTables[0]
        assertThat(targetTable.schemaOrDatabase).isNull()
        assertThat(targetTable.name).isEqualTo("foo")

        // Verify source tables should be empty (UPDATE doesn't read from other tables)
        assertThat(result.sourceTables).isEmpty()

        // Verify consolidated SQL contains the update statement
        assertThat(result.consolidatedSql).contains("update foo set age = 1")

        // Verify column mappings exist
        assertThat(result.columnMappings).isNotEmpty()
    }
}
