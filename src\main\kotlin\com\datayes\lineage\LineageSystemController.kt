package com.datayes.lineage

import com.datayes.ApiResponse
import com.datayes.scheduler.CronJobSchedulerService
import com.datayes.task.SystemLineageCollectionRequest
import com.datayes.task.TaskType
import com.datayes.util.CronExpressionUtil
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 系统血缘收集控制器 (System Lineage Collection Controller)
 *
 * 提供系统特定的血缘收集 REST API 接口
 */
@Tag(name = "LineageSystem", description = "系统血缘收集接口")
@RestController
@RequestMapping("/api/v1/lineage/systems")
@CrossOrigin(origins = ["*"])
class LineageSystemController(
    private val asyncLineageService: AsyncLineageService,
    private val lineageService: LineageService,
    private val cronJobSchedulerService: CronJobSchedulerService,
    private val jobStatusManager: JobStatusManager
) {

    private val logger = LoggerFactory.getLogger(LineageSystemController::class.java)

    /**
     * 触发系统特定的血缘收集 (Trigger system-specific lineage collection)
     *
     * @param systemType 系统类型
     * @param request 血缘收集请求
     * @return 血缘收集任务提交结果 (immediate response for task submission)
     */
    @Operation(summary = "触发系统血缘收集", description = "根据系统类型异步触发血缘收集任务，立即返回任务提交结果")
    @PostMapping("/{systemType}/collect")
    fun collectSystemLineage(
        @Parameter(description = "系统类型", example = "DATA_EXCHANGE_PLATFORM")
        @PathVariable systemType: String,
        @RequestBody request: SystemLineageCollectionRequest
    ): ResponseEntity<ApiResponse<String>> {
        logger.info("eb2a9f47 | 接收到系统血缘收集请求: systemType=$systemType, executedBy=${request.executedBy}")

        return try {
            // 验证系统类型 (validate system type)
            val taskType = validateAndParseSystemType(systemType)

            // 检查是否已有同类型任务在运行 (check if job of same type is already running)
            if (jobStatusManager.isJobRunning(systemType)) {
                val runningJobInfo = jobStatusManager.getRunningJobInfo(systemType)
                logger.warn("d9f3e2a7 | 任务已在运行中，无法重复提交: systemType=$systemType, 当前执行者=$runningJobInfo?.executedBy, 请求执行者=${request.executedBy}")
                return ResponseEntity.ok(ApiResponse.error("系统血缘收集任务正在运行中，请等待当前任务完成后再试"))
            }

            // 注册任务状态 (register job status)
            val registered = jobStatusManager.registerRunningJob(systemType, request.executedBy)
            if (!registered) {
                logger.warn("c8e4f1b3 | 任务注册失败，可能有并发请求: systemType=$systemType, executedBy=${request.executedBy}")
                return ResponseEntity.ok(ApiResponse.error("系统血缘收集任务正在运行中，请等待当前任务完成后再试"))
            }

            // 在后台异步执行系统血缘收集 (execute system lineage collection in background)
            asyncLineageService.processSystemLineageInBackground(taskType, systemType, request)

            logger.info("a5c8d2f9 | 系统血缘收集任务已提交到后台处理: systemType=$systemType, executedBy=${request.executedBy}")

            // 立即返回任务提交成功的响应 (return immediate response for successful task submission)
            ResponseEntity.ok(ApiResponse.success("任务提交成功，正在后台处理中"))

        } catch (e: UnsupportedOperationException) {
            logger.warn("c4d5e8f1 | 不支持的系统类型: systemType=$systemType", e)
            ResponseEntity.status(HttpStatus.OK)
                .body(ApiResponse.error("不支持的系统类型: $systemType"))

        } catch (e: IllegalArgumentException) {
            logger.warn("f7a2b9c6 | 请求参数错误: systemType=$systemType", e)
            ResponseEntity.status(HttpStatus.OK)
                .body(ApiResponse.error("请求参数错误: ${e.message}"))

        } catch (e: Exception) {
            logger.error("d8e3f7a4 | 系统血缘收集任务提交失败: systemType=$systemType", e)
            // 如果任务提交失败，需要清理已注册的任务状态
            jobStatusManager.unregisterRunningJob(systemType)
            ResponseEntity.status(HttpStatus.OK)
                .body(ApiResponse.error("系统血缘收集任务提交失败: ${e.message}"))
        }
    }


    /**
     * 获取支持的系统类型列表 (Get supported system types)
     *
     * @return 支持的系统类型列表
     */
    @Operation(summary = "获取支持的系统类型", description = "返回当前支持的系统类型列表")
    @GetMapping("/types")
    fun getSupportedSystemTypes(): ResponseEntity<ApiResponse<List<SystemTypeInfo>>> {
        return try {
            val supportedTypes = listOf(
                SystemTypeInfo(
                    type = "DATA_EXCHANGE_PLATFORM",
                    name = "数据交互平台",
                    description = "数据交互平台系统的血缘收集",
                    supported = true
                ),
                SystemTypeInfo(
                    type = "BASH_SCRIPT",
                    name = "Bash脚本",
                    description = "Bash脚本系统的血缘收集",
                    supported = false
                ),
                SystemTypeInfo(
                    type = "MANUAL_IMPORT",
                    name = "手动导入",
                    description = "手动导入系统的血缘收集",
                    supported = false
                ),
                SystemTypeInfo(
                    type = "EXCEL_IMPORT",
                    name = "Excel导入",
                    description = "Excel导入系统的血缘收集",
                    supported = false
                )
            )

            ResponseEntity.ok(
                ApiResponse.success(
                    data = supportedTypes,
                    message = "获取支持的系统类型成功"
                )
            )

        } catch (e: Exception) {
            logger.error("获取支持的系统类型时发生错误", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取支持的系统类型失败: ${e.message}"))
        }
    }

    /**
     * 查询所有系统信息 (Query all system information with filtering)
     *
     * @param systemName 系统名称模糊搜索 (optional)
     * @param systemStatus 系统状态过滤 (optional)
     * @return 系统信息列表
     */
    @GetMapping("/info")
    fun getAllSystems(
        @RequestParam(required = false) systemName: String?,
        @RequestParam(required = false) systemStatus: String?
    ): ResponseEntity<ApiResponse<List<SystemInfo>>> {
        return try {
            logger.info("f8c3a2b7 | 接收到系统信息查询请求: systemName=$systemName, systemStatus=$systemStatus")

            val systems = lineageService.findAllSystems(systemName, systemStatus)

            logger.info("d5e9f1c4 | 系统信息查询完成: 共查询到${systems.size}个系统")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = systems,
                    message = "查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("a7b2c8d3 | 查询系统信息时发生错误", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 查询系统内容 (Get system contents)
     *
     * UC-02 & UC-03: 查看系统关联的元数据数据源和表级血缘关系
     *
     * @param systemId 系统ID
     * @return 系统内容，包含元数据数据源和表级血缘关系
     */
    @GetMapping("/{systemId}/contents")
    fun getSystemContents(
        @PathVariable systemId: Long
    ): ResponseEntity<ApiResponse<SystemContentsDto>> {
        return try {
            logger.info("7f4e9c2b | 接收到系统内容查询请求: systemId=$systemId")

            val systemContents = lineageService.findSystemContents(systemId)

            logger.info(
                "5a8d1f6c | 系统内容查询完成: systemId=$systemId, " +
                        "元数据数据源数量=${systemContents.metadataDataSources.size}, " +
                        "表级血缘关系数量=${systemContents.tableLineageViews.size}"
            )

            ResponseEntity.ok(
                ApiResponse.success(
                    data = systemContents,
                    message = "查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("2b9e4a7d | 查询系统内容时发生错误: systemId=$systemId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 根据元数据数据源过滤表级血缘关系 (Filter table lineage by metadata data source)
     *
     * UC-04: 从数据源快速导航到表
     *
     * @param metadataDataSourceId 元数据数据源ID
     * @return 过滤后的表级血缘关系列表
     */
    @GetMapping("/datasource/{metadataDataSourceId}/tables")
    fun getTableLineageByDataSource(
        @PathVariable metadataDataSourceId: Long
    ): ResponseEntity<ApiResponse<List<TableLineageView>>> {
        return try {
            logger.info("9e2f3a8b | 接收到按数据源过滤表级血缘请求: metadataDataSourceId=$metadataDataSourceId")

            val tableLineageViews = lineageService.findTableLineageByMetadataDataSource(metadataDataSourceId)

            logger.info(
                "6c4d7f1a | 按数据源过滤表级血缘完成: metadataDataSourceId=$metadataDataSourceId, " +
                        "过滤后血缘关系数量=${tableLineageViews.size}"
            )

            ResponseEntity.ok(
                ApiResponse.success(
                    data = tableLineageViews,
                    message = "查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("3b8e9f2c | 按数据源过滤表级血缘时发生错误: metadataDataSourceId=$metadataDataSourceId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 查询作业处理历史 (Get job processing history)
     *
     * UC-06: 查看手动血缘收集的执行结果
     *
     * @param jobKey 作业键 (可选)
     * @param limit 返回记录数限制 (默认50)
     * @return 作业处理历史列表
     */
    @GetMapping("/processing-history")
    fun getJobProcessingHistory(
        @RequestParam(required = false) jobKey: String?,
        @RequestParam(defaultValue = "50") limit: Int
    ): ResponseEntity<ApiResponse<List<JobLineageHashHistory>>> {
        return try {
            logger.info("4d7f9e2a | 接收到查询作业处理历史请求: jobKey=$jobKey, limit=$limit")

            val historyRecords = lineageService.findJobProcessingHistory(jobKey, limit)

            logger.info("8b3c6e1d | 作业处理历史查询完成: 返回${historyRecords.size}条记录")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = historyRecords,
                    message = "查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("1f5a8c7b | 查询作业处理历史时发生错误: jobKey=$jobKey", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 更新系统状态和Cron表达式 (Update system status and cron expression)
     *
     * @param systemId 系统ID
     * @param request 更新请求
     * @return 更新结果
     */
    @Operation(
        summary = "更新系统状态和Cron表达式",
        description = "根据系统ID更新系统的状态（ACTIVE/INACTIVE）和/或Cron表达式（标准cron格式）"
    )
    @PutMapping("/{systemId}")
    fun updateSystem(
        @Parameter(description = "系统ID", example = "1", required = true)
        @PathVariable systemId: Long,
        @io.swagger.v3.oas.annotations.parameters.RequestBody(
            description = "系统更新请求",
            required = true
        )
        @RequestBody request: SystemUpdateRequest
    ): ResponseEntity<ApiResponse<SystemUpdateResponse>> {
        return try {
            logger.info("cd2cad7c | 接收到系统更新请求: systemId=$systemId, status=${request.status}, cronExpression=${request.cronExpression}")

            // 参数验证 (Parameter validation)
            if (request.status == null && request.cronExpression == null) {
                logger.warn("b02f20a4 | 请求参数为空: systemId=$systemId")
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("请至少提供status或cronExpression其中一个参数 (At least one of status or cronExpression must be provided)"))
            }

            // 验证状态参数 (Validate status parameter)
            request.status?.let { status ->
                if (status.uppercase() !in listOf("ACTIVE", "INACTIVE")) {
                    logger.warn("9219c0c7 | 无效的状态值: systemId=$systemId, status=$status")
                    return ResponseEntity.badRequest()
                        .body(ApiResponse.error("状态值必须是ACTIVE或INACTIVE (Status must be ACTIVE or INACTIVE)"))
                }
            }

            // 验证Cron表达式 (Validate cron expression)
            request.cronExpression?.let { cronExpr ->
                if (cronExpr.isNotBlank() && !CronExpressionUtil.isValidCronExpression(cronExpr)) {
                    logger.warn("a24efd8a | 无效的Cron表达式: systemId=$systemId, cronExpression=$cronExpr")
                    return ResponseEntity.badRequest()
                        .body(ApiResponse.error("无效的Cron表达式格式 (Invalid cron expression format)"))
                }
            }

            // 执行更新 (Perform update)
            val updatedSystem = lineageService.updateSystem(
                systemId = systemId,
                status = request.status,
                cronExpression = request.cronExpression?.takeIf { it.isNotBlank() },
                updatedBy = request.updatedBy
            )

            if (updatedSystem == null) {
                logger.warn("c3f7808b | 系统不存在: systemId=$systemId")
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("系统不存在 (System not found)"))
            }

            // 构建响应 (Build response)
            val response = SystemUpdateResponse(
                id = updatedSystem.id,
                systemName = updatedSystem.systemName,
                systemCode = updatedSystem.systemCode,
                status = updatedSystem.status,
                cronExpression = updatedSystem.cronExpression,
                scheduleTime = updatedSystem.scheduleTime,
                updatedAt = updatedSystem.updatedAt,
                enableScheduleBy = updatedSystem.enableScheduleBy,
                enableScheduleAt = updatedSystem.enableScheduleAt,
                disableScheduleBy = updatedSystem.disableScheduleBy,
                disableScheduleAt = updatedSystem.disableScheduleAt,
                lastScheduleAt = updatedSystem.lastScheduleAt
            )

            logger.info("fa4d8e03 | 系统更新成功: systemId=$systemId, status=${updatedSystem.status}, cronExpression=${updatedSystem.cronExpression}")

            // 如果更新了状态或cron表达式，则重新调度定时任务
            if (request.status != null || request.cronExpression != null) {
                try {
                    cronJobSchedulerService.rescheduleSystemTask(systemId)
                    logger.info("701c0716 | 系统定时任务重新调度成功: systemId=$systemId")
                } catch (e: Exception) {
                    logger.warn("9c0138cb | 系统定时任务重新调度失败: systemId=$systemId", e)
                    // 注意：这里不抛出异常，因为系统更新本身是成功的
                }
            }

            ResponseEntity.ok(
                ApiResponse.success(
                    data = response,
                    message = "系统更新成功 (System updated successfully)"
                )
            )

        } catch (e: IllegalArgumentException) {
            logger.warn("72c3c96f | 系统更新参数错误: systemId=$systemId", e)
            ResponseEntity.badRequest()
                .body(ApiResponse.error("请求参数错误: ${e.message} (Invalid request parameters)"))

        } catch (e: Exception) {
            logger.error("73fb71bf | 系统更新时发生错误: systemId=$systemId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统更新失败: ${e.message} (System update failed)"))
        }
    }

    /**
     * 验证并解析系统类型 (Validate and parse system type)
     */
    private fun validateAndParseSystemType(systemType: String): TaskType {
        return try {
            val taskType = TaskType.valueOf(systemType.uppercase())

            // 检查是否支持该系统类型
            when (taskType) {
                TaskType.DATA_EXCHANGE_PLATFORM -> taskType
                TaskType.BIG_DATA_PLATFORM -> taskType
                else -> throw UnsupportedOperationException("当前不支持的系统类型: $systemType")
            }

        } catch (e: IllegalArgumentException) {
            throw IllegalArgumentException("无效的系统类型: $systemType", e)
        }
    }
}

/**
 * 系统类型信息 (System Type Info)
 */
data class SystemTypeInfo(
    val type: String,
    val name: String,
    val description: String,
    val supported: Boolean
)

/**
 * 系统更新请求 (System Update Request)
 */
data class SystemUpdateRequest(
    @Schema(description = "系统状态 (ACTIVE/INACTIVE)", example = "ACTIVE", allowableValues = ["ACTIVE", "INACTIVE"])
    val status: String? = null,

    @Schema(description = "Cron表达式 (标准cron格式)", example = "0 0 2 * * ?")
    val cronExpression: String? = null,

    @Schema(description = "更新用户 (用于记录启用/禁用调度的操作者)", example = "admin")
    val updatedBy: String? = null
)

/**
 * 系统更新响应 (System Update Response)
 */
data class SystemUpdateResponse(
    @Schema(description = "系统ID", example = "1")
    val id: Long,

    @Schema(description = "系统名称", example = "数据交互平台")
    val systemName: String,

    @Schema(description = "系统编码", example = "DATA_EXCHANGE_PLATFORM")
    val systemCode: String,

    @Schema(description = "系统状态", example = "ACTIVE")
    val status: String,

    @Schema(description = "Cron表达式", example = "0 0 2 * * ?")
    val cronExpression: String?,

    @Schema(description = "下次调度时间", example = "2024-01-15T02:00:00")
    val scheduleTime: java.time.LocalDateTime?,

    @Schema(description = "更新时间", example = "2024-01-14T10:30:00")
    val updatedAt: java.time.LocalDateTime,

    @Schema(description = "启用调度的用户", example = "admin")
    val enableScheduleBy: String?,

    @Schema(description = "启用调度的时间", example = "2024-01-14T10:30:00")
    val enableScheduleAt: java.time.LocalDateTime?,

    @Schema(description = "禁用调度的用户", example = "admin")
    val disableScheduleBy: String?,

    @Schema(description = "禁用调度的时间", example = "2024-01-14T10:30:00")
    val disableScheduleAt: java.time.LocalDateTime?,

    @Schema(description = "最后调度时间", example = "2024-01-14T02:00:00")
    val lastScheduleAt: java.time.LocalDateTime?
)
