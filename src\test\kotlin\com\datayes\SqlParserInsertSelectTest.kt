package com.datayes

import com.datayes.sql.DataModificationResult
import com.datayes.sql.SqlParser
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinModule
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

class SqlParserInsertSelectTest {

    private val objectMapper = ObjectMapper().registerModule(KotlinModule.Builder().build())

    private fun assertDataModificationResult(actual: DataModificationResult, expectedJson: String) {
        val actualStructure = toStructure(actual)
        val expectedStructure = objectMapper.readValue(expectedJson, Map::class.java)
        
        assertThat(actualStructure).isEqualTo(expectedStructure)
    }

    private fun toStructure(result: DataModificationResult): Map<String, Any?> {
        return mapOf(
            "targetTable" to mapOf(
                "name" to result.targetTable.name,
                "schema" to result.targetTable.schemaOrDatabase,
                "alias" to result.targetTable.alias
            ),
            "targetColumns" to result.targetColumns,
            "sourceTables" to result.sourceTables.map { table ->
                mapOf(
                    "name" to table.name,
                    "schema" to table.schemaOrDatabase,
                    "alias" to table.alias
                )
            },
            "sourceColumns" to result.sourceColumns.map { column ->
                mapOf(
                    "name" to column.name,
                    "tablePrefix" to column.tablePrefix,
                    "alias" to column.alias,
                    "originalExpression" to column.originalExpression,
                    "isWildcard" to column.isWildcard
                )
            },
            "columnMappings" to result.columnMappings.map { mapping ->
                mapOf(
                    "sourceColumn" to mapOf(
                        "name" to mapping.sourceColumn.name,
                        "tablePrefix" to mapping.sourceColumn.tablePrefix
                    ),
                    "targetColumnName" to mapping.targetColumnName,
                    "targetColumnIndex" to mapping.targetColumnIndex
                )
            }
        )
    }

    @Test
    @DisplayName("Should parse INSERT INTO ... SELECT statement correctly")
    fun testParseInsertIntoSelectStatement() {
        // Given
        val sqlQuery = """
            insert into foo
            select name, age from bar
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(sqlQuery)

        // Then
        assertThat(result).isNotNull
        
        val expected = """
        {
          "targetTable": {
            "name": "foo",
            "schema": null,
            "alias": null
          },
          "targetColumns": null,
          "sourceTables": [
            {
              "name": "bar",
              "schema": null,
              "alias": null
            }
          ],
          "sourceColumns": [
            {
              "name": "name",
              "tablePrefix": null,
              "alias": null,
              "originalExpression": "name",
              "isWildcard": false
            },
            {
              "name": "age",
              "tablePrefix": null,
              "alias": null,
              "originalExpression": "age",
              "isWildcard": false
            }
          ],
          "columnMappings": [
            {
              "sourceColumn": {
                "name": "name",
                "tablePrefix": null
              },
              "targetColumnName": "name",
              "targetColumnIndex": 0
            },
            {
              "sourceColumn": {
                "name": "age",
                "tablePrefix": null
              },
              "targetColumnName": "age",
              "targetColumnIndex": 1
            }
          ]
        }
        """.trimIndent()

        assertDataModificationResult(result, expected)
    }

    @Test
    @DisplayName("Should parse INSERT INTO ... SELECT with JOIN correctly")
    fun testParseInsertIntoSelectWithJoin() {
        // Given
        val sqlQuery = """
            insert into foo
            select u.name, p.age 
            from bar u
            join baz p on u.id = p.user_id
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(sqlQuery)

        // Then
        assertThat(result).isNotNull
        
        val expected = """
        {
          "targetTable": {
            "name": "foo",
            "schema": null,
            "alias": null
          },
          "targetColumns": null,
          "sourceTables": [
            {
              "name": "bar",
              "schema": null,
              "alias": "u"
            },
            {
              "name": "baz",
              "schema": null,
              "alias": "p"
            }
          ],
          "sourceColumns": [
            {
              "name": "name",
              "tablePrefix": "u",
              "alias": null,
              "originalExpression": "u.name",
              "isWildcard": false
            },
            {
              "name": "age",
              "tablePrefix": "p",
              "alias": null,
              "originalExpression": "p.age",
              "isWildcard": false
            }
          ],
          "columnMappings": [
            {
              "sourceColumn": {
                "name": "name",
                "tablePrefix": "u"
              },
              "targetColumnName": "name",
              "targetColumnIndex": 0
            },
            {
              "sourceColumn": {
                "name": "age",
                "tablePrefix": "p"
              },
              "targetColumnName": "age",
              "targetColumnIndex": 1
            }
          ]
        }
        """.trimIndent()

        assertDataModificationResult(result, expected)
    }

}