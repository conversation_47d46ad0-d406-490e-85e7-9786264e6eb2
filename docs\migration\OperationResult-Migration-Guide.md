# OperationResult Migration Guide

## Overview

This guide provides examples and recommendations for migrating existing response classes to use the new generic `OperationResult<T>` class.

## Benefits of Migration

1. **Consistency**: Standardized response format across all APIs
2. **Type Safety**: Generic type parameter ensures compile-time type checking
3. **Rich Functionality**: Built-in utility methods for common operations
4. **Maintainability**: Single class to maintain instead of multiple similar classes

## Migration Examples

### 1. ScriptLineageResult → OperationResult<DataLineage>

**Before:**
```kotlin
data class ScriptLineageResult(
    val lineage: DataLineage?,
    val warnings: List<String>,
    val errors: List<String>,
    val success: Boolean
)

// Usage
fun analyzeScript(): ScriptLineageResult {
    return ScriptLineageResult(
        lineage = dataLineage,
        warnings = listOf("Warning message"),
        errors = emptyList(),
        success = true
    )
}
```

**After:**
```kotlin
// Usage
fun analyzeScript(): OperationResult<DataLineage> {
    return OperationResult.success(
        data = dataLineage,
        warnings = listOf("Warning message")
    )
}

// Or for failures
fun analyzeScriptFailure(): OperationResult<DataLineage> {
    return OperationResult.failure(
        message = "Analysis failed",
        errors = listOf("SQL parsing error", "Invalid table reference"),
        warnings = listOf("Deprecated syntax detected")
    )
}
```

### 2. LineageOperationResponse → OperationResult<LineageOperationData>

**Before:**
```kotlin
data class LineageOperationResponse(
    val success: Boolean,
    val message: String,
    val tableRelationshipId: Long? = null,
    val affectedTableRelationships: Int = 0,
    val affectedColumnRelationships: Int = 0,
    val errors: List<String> = emptyList(),
    val warnings: List<String> = emptyList()
)
```

**After:**
```kotlin
// Create a data class for the operation result
data class LineageOperationData(
    val tableRelationshipId: Long? = null,
    val affectedTableRelationships: Int = 0,
    val affectedColumnRelationships: Int = 0
)

// Usage
fun createLineageRelationship(): OperationResult<LineageOperationData> {
    return OperationResult.success(
        data = LineageOperationData(
            tableRelationshipId = 123L,
            affectedTableRelationships = 1,
            affectedColumnRelationships = 5
        ),
        message = "Lineage relationship created successfully"
    )
}
```

### 3. ParsedScriptLineage → OperationResult<ParsedLineageData>

**Before:**
```kotlin
data class ParsedScriptLineage(
    val sourceTables: List<SqlTableReference>,
    val targetTables: List<SqlTableReference>,
    val columnMappings: List<ColumnMapping>,
    val consolidatedSql: String,
    val warnings: List<String>,
    val errors: List<String>,
    val success: Boolean
)
```

**After:**
```kotlin
data class ParsedLineageData(
    val sourceTables: List<SqlTableReference>,
    val targetTables: List<SqlTableReference>,
    val columnMappings: List<ColumnMapping>,
    val consolidatedSql: String
)

// Usage
fun parseScriptLineage(): OperationResult<ParsedLineageData> {
    return OperationResult.partialSuccess(
        data = ParsedLineageData(
            sourceTables = sourceTables,
            targetTables = targetTables,
            columnMappings = columnMappings,
            consolidatedSql = sql
        ),
        warnings = listOf("Some SQL statements could not be parsed")
    )
}
```

## Controller Migration Examples

### Before: Manual Response Construction
```kotlin
@PostMapping("/analyze")
fun analyzeScript(@RequestBody request: ScriptAnalysisRequest): ResponseEntity<ApiResponse<ScriptLineageResult>> {
    return try {
        val result = scriptAnalysisService.analyzeScript(request)
        if (result.success) {
            ResponseEntity.ok(ApiResponse.success(result))
        } else {
            ResponseEntity.badRequest().body(ApiResponse.error("Analysis failed"))
        }
    } catch (e: Exception) {
        ResponseEntity.internalServerError().body(ApiResponse.error("Internal error: ${e.message}"))
    }
}
```

### After: Using OperationResult
```kotlin
@PostMapping("/analyze")
fun analyzeScript(@RequestBody request: ScriptAnalysisRequest): ResponseEntity<ApiResponse<DataLineage>> {
    return try {
        val result = scriptAnalysisService.analyzeScript(request)
        if (result.success) {
            ResponseEntity.ok(ApiResponse.success(result.data, result.message))
        } else {
            ResponseEntity.badRequest().body(ApiResponse.error(result.message ?: "Analysis failed"))
        }
    } catch (e: Exception) {
        ResponseEntity.internalServerError().body(ApiResponse.error("Internal error: ${e.message}"))
    }
}
```

## Service Layer Migration

### Exception Handling
```kotlin
// Before
fun processLineage(): ScriptLineageResult {
    return try {
        val lineage = performAnalysis()
        ScriptLineageResult(lineage, warnings, emptyList(), true)
    } catch (e: Exception) {
        ScriptLineageResult(null, warnings, listOf(e.message ?: "Unknown error"), false)
    }
}

// After
fun processLineage(): OperationResult<DataLineage> {
    return try {
        val lineage = performAnalysis()
        OperationResult.success(lineage, warnings = warnings)
    } catch (e: Exception) {
        OperationResult.fromException(e, warnings = warnings)
    }
}
```

### Conditional Results
```kotlin
// Before
fun validateAndProcess(): ScriptLineageResult {
    if (!isValid) {
        return ScriptLineageResult(null, emptyList(), listOf("Validation failed"), false)
    }
    // ... processing
}

// After
fun validateAndProcess(): OperationResult<DataLineage> {
    return OperationResult.conditional(
        condition = isValid,
        data = if (isValid) processedData else null,
        failureMessage = "Validation failed"
    )
}
```

## Utility Method Usage

### Checking Result Status
```kotlin
val result = service.analyzeScript(request)

// Check different success levels
if (result.isCompleteSuccess()) {
    logger.info("Analysis completed without any issues")
} else if (result.isPartialSuccess()) {
    logger.warn("Analysis completed with warnings: ${result.warnings}")
} else {
    logger.error("Analysis failed: ${result.errors}")
}

// Get summary
logger.info(result.summarize())
```

### Transforming Results
```kotlin
// Transform the data type
val lineageResult: OperationResult<DataLineage> = service.analyzeScript(request)
val summaryResult: OperationResult<LineageSummary> = lineageResult.map { lineage ->
    LineageSummary(
        sourceTableCount = lineage.tableLineage.sourceTables.size,
        targetTableCount = lineage.tableLineage.targetTables.size
    )
}

// Chain operations
val finalResult = lineageResult.flatMap { lineage ->
    validationService.validateLineage(lineage)
}
```

### Combining Multiple Results
```kotlin
val results = listOf(
    service1.process(),
    service2.process(),
    service3.process()
)

val combinedResult = OperationResult.combine(results) { dataList ->
    CombinedData(dataList)
}
```

## Migration Strategy

### Phase 1: Create Data Classes
1. Extract data fields from existing response classes into separate data classes
2. Keep existing response classes for backward compatibility

### Phase 2: Update Service Layer
1. Modify service methods to return `OperationResult<T>`
2. Update internal logic to use new utility methods

### Phase 3: Update Controllers
1. Modify controllers to work with `OperationResult<T>`
2. Update response handling logic

### Phase 4: Deprecate Old Classes
1. Mark old response classes as `@Deprecated`
2. Add migration notes in documentation

### Phase 5: Remove Old Classes
1. Remove deprecated classes after ensuring all code is migrated
2. Update tests and documentation

## Best Practices

1. **Use Specific Data Types**: Create specific data classes for `T` rather than using generic types
2. **Meaningful Messages**: Provide clear, actionable error and warning messages
3. **Consistent Error Handling**: Use `OperationResult.fromException()` for exception handling
4. **Leverage Utility Methods**: Use built-in methods like `map()`, `flatMap()`, and `combine()`
5. **Log Appropriately**: Use `summarize()` method for consistent logging
6. **Validate Early**: Use `conditional()` for early validation checks

## Testing Considerations

### Unit Test Examples

```kotlin
@Test
fun `should return success result with warnings`() {
    val result = service.processWithWarnings()

    assertTrue(result.success)
    assertTrue(result.isPartialSuccess())
    assertFalse(result.isCompleteSuccess())
    assertEquals(2, result.warnings.size)
    assertNotNull(result.data)
}

@Test
fun `should handle exceptions gracefully`() {
    whenever(mockService.process()).thenThrow(RuntimeException("Test error"))

    val result = service.processWithExceptionHandling()

    assertFalse(result.success)
    assertTrue(result.hasErrors())
    assertEquals("Test error", result.errors.first())
}

@Test
fun `should transform data correctly`() {
    val originalResult = OperationResult.success(DataLineage(...))
    val transformedResult = originalResult.map { lineage ->
        LineageSummary(lineage.sourceTables.size, lineage.targetTables.size)
    }

    assertTrue(transformedResult.success)
    assertNotNull(transformedResult.data)
    assertEquals(originalResult.warnings, transformedResult.warnings)
}

@Test
fun `should combine multiple results correctly`() {
    val results = listOf(
        service1.process(),
        service2.process(),
        service3.process()
    )

    val combinedResult = OperationResult.combine(results) { dataList ->
        CombinedData(dataList)
    }

    // Test based on expected behavior
    if (results.all { it.success }) {
        assertTrue(combinedResult.success)
        assertNotNull(combinedResult.data)
    } else {
        assertFalse(combinedResult.success)
    }
}
```

### Integration Test Examples

```kotlin
@Test
fun `should handle complete workflow with OperationResult`() {
    // Arrange
    val request = ScriptAnalysisRequest(...)

    // Act
    val result = scriptAnalysisController.analyzeScript(request)

    // Assert
    assertEquals(HttpStatus.OK, result.statusCode)
    val apiResponse = result.body!!
    assertTrue(apiResponse.success)
    assertNotNull(apiResponse.data)
}

@Test
fun `should return appropriate error response on failure`() {
    // Arrange
    val invalidRequest = ScriptAnalysisRequest(...)

    // Act
    val result = scriptAnalysisController.analyzeScript(invalidRequest)

    // Assert
    assertEquals(HttpStatus.BAD_REQUEST, result.statusCode)
    val apiResponse = result.body!!
    assertFalse(apiResponse.success)
    assertNotNull(apiResponse.message)
}
```

### Performance Test Considerations

```kotlin
@Test
fun `should handle large result sets efficiently`() {
    val largeDataSet = generateLargeDataSet(10000)
    val startTime = System.currentTimeMillis()

    val result = OperationResult.success(largeDataSet)
    val transformedResult = result.map { data ->
        data.take(100) // Transform to smaller set
    }

    val endTime = System.currentTimeMillis()
    assertTrue(endTime - startTime < 1000) // Should complete within 1 second
    assertTrue(transformedResult.success)
}
```
