package com.datayes.lineage

import com.datayes.metadata.MetadataDataSourceDto
import org.slf4j.LoggerFactory
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.jdbc.support.GeneratedKeyHolder
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.sql.Statement
import java.time.LocalDateTime

/**
 * 数据库名称匹配服务 (Database Name Matching Service)
 *
 * 根据数据库名称查找元数据数据源，并匹配或创建对应的血缘数据源
 */
@Service
class DatabaseNameMatchingService(
    private val jdbcTemplate: JdbcTemplate
) {

    private val logger = LoggerFactory.getLogger(DatabaseNameMatchingService::class.java)

    /**
     * 根据数据库名称查找或创建血缘数据源
     *
     * @param databaseName 数据库名称
     * @return 血缘数据源ID
     * @throws IllegalArgumentException 如果未找到匹配的元数据数据源
     */
    @Transactional
    fun findOrCreateLineageDatasourceByDatabaseName(databaseName: String): Long {
        logger.info("f1a2b3c4 | 开始根据数据库名称查找或创建血缘数据源: databaseName=$databaseName")

        // 1. 根据数据库名称查找元数据数据源
        val metadataDataSource = findMetadataDataSourceByDatabaseName(databaseName)
            ?: throw IllegalArgumentException("未找到数据库名称为 '$databaseName' 的元数据数据源")

        logger.info("g5h6i7j8 | 找到匹配的元数据数据源: metadataDataSourceId=${metadataDataSource.id}, dbType=${metadataDataSource.dbType}")

        // 2. 查找或创建对应的血缘数据源
        val lineageDatasourceId = findOrCreateLineageDatasource(metadataDataSource)

        logger.info("k9l0m1n2 | 血缘数据源处理完成: lineageDatasourceId=$lineageDatasourceId, databaseName=$databaseName")

        return lineageDatasourceId
    }

    /**
     * 根据数据库名称查找或创建血缘数据源，并返回完整的数据库信息
     *
     * @param databaseName 数据库名称
     * @return 完整的数据库信息，包含连接详情
     * @throws IllegalArgumentException 如果未找到匹配的元数据数据源
     */
    @Transactional
    fun findOrCreateDatabaseInfoByDatabaseName(databaseName: String): DatabaseInfo {
        logger.info("db_info_request | 开始根据数据库名称获取完整数据库信息: databaseName=$databaseName")

        // 1. 根据数据库名称查找元数据数据源
        val metadataDataSource = findMetadataDataSourceByDatabaseName(databaseName)
            ?: throw IllegalArgumentException("未找到数据库名称为 '$databaseName' 的元数据数据源")

        // 2. 查找或创建对应的血缘数据源
        val lineageDatasourceId = findOrCreateLineageDatasource(metadataDataSource)

        // 3. 从血缘数据源构建DatabaseInfo
        val databaseInfo = buildDatabaseInfoFromLineageDatasource(lineageDatasourceId, metadataDataSource, databaseName)

        logger.info("db_info_success | 成功获取数据库信息: databaseName=$databaseName, dbType=${databaseInfo.dbType}, host=${databaseInfo.host}")

        return databaseInfo
    }

    /**
     * 从血缘数据源和元数据数据源构建DatabaseInfo
     */
    private fun buildDatabaseInfoFromLineageDatasource(
        lineageDatasourceId: Long,
        metadataDataSource: MetadataDataSourceDto,
        databaseName: String
    ): DatabaseInfo {
        // 查询lineage_datasources表获取实际存储的信息
        val sql = """
            SELECT db_type, host, port, database_name, connection_string
            FROM lineage_datasources
            WHERE id = ?
        """.trimIndent()

        return jdbcTemplate.queryForObject(sql, { rs, _ ->
            DatabaseInfo(
                dbType = rs.getString("db_type"),
                host = rs.getString("host"),
                port = rs.getInt("port"),
                databaseName = rs.getString("database_name"),
                originalConnectionString = rs.getString("connection_string")
            )
        }, lineageDatasourceId) ?: run {
            // 如果查询失败，从元数据数据源构建
            logger.warn("lineage_query_fail | 查询lineage_datasources失败，从元数据数据源构建DatabaseInfo")
            buildDatabaseInfoFromMetadataDataSource(metadataDataSource, databaseName)
        }
    }

    /**
     * 从元数据数据源构建DatabaseInfo
     */
    private fun buildDatabaseInfoFromMetadataDataSource(
        metadataDataSource: MetadataDataSourceDto,
        databaseName: String
    ): DatabaseInfo {
        val host = extractHostFromMetadataDataSource(metadataDataSource)
        val port = extractPortFromMetadataDataSource(metadataDataSource)
        val dbType = normalizeDbType(metadataDataSource.dbType)
        val connectionString = buildConnectionString(metadataDataSource, dbType, host, port, databaseName)

        return DatabaseInfo(
            dbType = dbType,
            host = host,
            port = port,
            databaseName = databaseName,
            originalConnectionString = connectionString
        )
    }

    /**
     * 根据数据库名称查找元数据数据源
     *
     * 查找逻辑：
     * 1. 根据DB_NAME字段查找所有匹配记录
     * 2. 如果有多个匹配，优先选择DB_TYPE为hive相关类型
     * 3. 如果多个都是hive类型，选择CREATE_TIME最早的
     */
    private fun findMetadataDataSourceByDatabaseName(databaseName: String): MetadataDataSourceDto? {
        logger.info("o3p4q5r6 | 开始查找元数据数据源: databaseName=$databaseName")

        val sql = """
            SELECT 
                mds.ID, mds.SOURCE_NAME, mds.DB_TYPE, mds.DB_DRIVER, mds.DB_NAME,
                mds.DB_URL, mds.DB_PORT, mds.DB_USERNAME, mds.CUSTOM_JDBC_URL,
                mds.ACTIVE_FLAG, mds.CREATE_BY, mds.CREATE_TIME, mds.UPDATE_BY, 
                mds.UPDATE_TIME, mds.SYSTEM_ID, mds.DESCRIPTION
            FROM metadata_data_source mds
            WHERE mds.DB_NAME = ? AND mds.ACTIVE_FLAG = 1
            ORDER BY 
                CASE 
                    WHEN LOWER(mds.DB_TYPE) IN ('hive', 'hive2') THEN 0 
                    ELSE 1 
                END,
                mds.CREATE_TIME ASC
        """.trimIndent()

        val allMatches = jdbcTemplate.query(sql, metadataDataSourceRowMapper, databaseName)

        return when {
            allMatches.isEmpty() -> {
                logger.warn("s7t8u9v0 | 未找到匹配的元数据数据源: databaseName=$databaseName")
                null
            }

            allMatches.size == 1 -> {
                logger.info("w1x2y3z4 | 找到唯一匹配的元数据数据源: databaseName=$databaseName, metadataDataSourceId=${allMatches[0].id}")
                allMatches[0]
            }

            else -> {
                // 多个匹配的情况
                val selectedDataSource = allMatches[0] // 已经按优先级排序
                val hiveMatches = allMatches.filter { isHiveType(it.dbType) }

                if (hiveMatches.size > 1) {
                    logger.warn(
                        "a5b6c7d8 | 找到多个hive类型的元数据数据源，选择CREATE_TIME最早的: databaseName=$databaseName, " +
                                "总匹配数=${allMatches.size}, hive类型数=${hiveMatches.size}, " +
                                "选择的metadataDataSourceId=${selectedDataSource.id}, dbType=${selectedDataSource.dbType}, " +
                                "createTime=${selectedDataSource.createTime}"
                    )
                } else {
                    logger.warn(
                        "e9f0g1h2 | 找到多个元数据数据源，优先选择hive类型: databaseName=$databaseName, " +
                                "总匹配数=${allMatches.size}, 选择的metadataDataSourceId=${selectedDataSource.id}, " +
                                "dbType=${selectedDataSource.dbType}"
                    )
                }

                selectedDataSource
            }
        }
    }

    /**
     * 检查数据库类型是否为hive相关类型
     */
    private fun isHiveType(dbType: String): Boolean {
        return dbType.lowercase() in setOf("hive", "hive2")
    }

    /**
     * 查找或创建血缘数据源
     */
    private fun findOrCreateLineageDatasource(metadataDataSource: MetadataDataSourceDto): Long {
        logger.info("i3j4k5l6 | 开始查找或创建血缘数据源: metadataDataSourceId=${metadataDataSource.id}")

        // 从元数据数据源提取信息
        val host = extractHostFromMetadataDataSource(metadataDataSource)
        val port = extractPortFromMetadataDataSource(metadataDataSource)
        val databaseName = metadataDataSource.dbName
        val dbType = normalizeDbType(metadataDataSource.dbType)

        logger.debug("m7n8o9p0 | 提取的连接信息: host=$host, port=$port, databaseName=$databaseName, dbType=$dbType")

        // 查找现有的血缘数据源
        val existingId = findExistingLineageDatasource(dbType, host, port, databaseName)

        if (existingId != null) {
            logger.info("q1r2s3t4 | 找到现有的血缘数据源: lineageDatasourceId=$existingId")

            // 更新metadata_data_source_id字段
            updateLineageDatasourceMetadataId(existingId, metadataDataSource.id)

            return existingId
        }

        // 创建新的血缘数据源
        val newId = createNewLineageDatasource(metadataDataSource, dbType, host, port, databaseName)
        logger.info("u5v6w7x8 | 创建新的血缘数据源: lineageDatasourceId=$newId")

        return newId
    }

    /**
     * 从元数据数据源中提取主机信息
     */
    private fun extractHostFromMetadataDataSource(metadataDataSource: MetadataDataSourceDto): String {
        // 优先从CUSTOM_JDBC_URL提取，如果为空则从DB_URL提取
        val url = if (!metadataDataSource.customJdbcUrl.isNullOrBlank()) {
            metadataDataSource.customJdbcUrl
        } else {
            metadataDataSource.dbUrl
        }

        if (url.isNullOrBlank()) {
            logger.warn("y9z0a1b2 | 元数据数据源没有可用的URL信息: metadataDataSourceId=${metadataDataSource.id}")
            return "unknown-host"
        }

        // JDBC URL解析逻辑，提取主机名
        return try {
            when {
                url.contains("://") -> {
                    val afterProtocol = url.substringAfter("://")
                    when {
                        afterProtocol.contains(":") -> afterProtocol.substringBefore(":")
                        afterProtocol.contains("/") -> afterProtocol.substringBefore("/")
                        else -> afterProtocol
                    }
                }

                url.contains(":") -> url.substringBefore(":")
                else -> url
            }
        } catch (e: Exception) {
            logger.warn("c3d4e5f6 | 解析主机名失败: url=$url, metadataDataSourceId=${metadataDataSource.id}", e)
            "unknown-host"
        }
    }

    /**
     * 从元数据数据源中提取端口信息
     */
    private fun extractPortFromMetadataDataSource(metadataDataSource: MetadataDataSourceDto): Int {
        // 如果CUSTOM_JDBC_URL不为空，优先从中解析端口
        if (!metadataDataSource.customJdbcUrl.isNullOrBlank()) {
            val portFromCustomUrl = extractPortFromUrl(metadataDataSource.customJdbcUrl!!, metadataDataSource.id)
            if (portFromCustomUrl != null) {
                return portFromCustomUrl
            }
        }

        // 其次使用DB_PORT字段
        metadataDataSource.dbPort?.let { return it }

        // 最后从DB_URL中提取端口
        if (!metadataDataSource.dbUrl.isNullOrBlank()) {
            val portFromDbUrl = extractPortFromUrl(metadataDataSource.dbUrl!!, metadataDataSource.id)
            if (portFromDbUrl != null) {
                return portFromDbUrl
            }
        }

        // 如果都没有，返回默认端口
        return getDefaultPortForDbType(metadataDataSource.dbType)
    }

    /**
     * 从URL中提取端口号
     */
    private fun extractPortFromUrl(url: String, metadataDataSourceId: Long): Int? {
        return try {
            // 匹配端口号模式，确保端口号后面跟着路径分隔符或字符串结尾
            val portPattern = Regex(":([0-9]+)(?:[/?]|${'$'})")
            val matchResult = portPattern.find(url)
            val port = matchResult?.groupValues?.get(1)?.toInt()

            if (port != null) {
                logger.debug("port_extract_success | 从URL提取端口成功: url=$url, port=$port, metadataDataSourceId=$metadataDataSourceId")
            }

            port
        } catch (e: Exception) {
            logger.warn("g7h8i9j0 | 解析端口失败: url=$url, metadataDataSourceId=$metadataDataSourceId", e)
            null
        }
    }

    /**
     * 根据数据库类型获取默认端口
     */
    private fun getDefaultPortForDbType(dbType: String): Int {
        return when (dbType.lowercase()) {
            "hive", "hive2" -> 10000
            "mysql" -> 3306
            "oracle" -> 1521
            "postgresql" -> 5432
            else -> 0
        }
    }

    /**
     * 标准化数据库类型
     */
    private fun normalizeDbType(dbType: String): String {
        return when (dbType.lowercase()) {
            "hive2" -> "hive"
            else -> dbType.lowercase()
        }
    }

    /**
     * 查找现有的血缘数据源
     */
    private fun findExistingLineageDatasource(dbType: String, host: String, port: Int, databaseName: String): Long? {
        val sql = """
            SELECT id FROM lineage_datasources 
            WHERE db_type = ? AND host = ? AND port = ? AND database_name = ? AND status = 'ACTIVE'
            LIMIT 1
        """.trimIndent()

        return jdbcTemplate.query(sql, { rs, _ -> rs.getLong("id") }, dbType, host, port, databaseName)
            .firstOrNull()
    }

    /**
     * 更新血缘数据源的metadata_data_source_id字段
     */
    private fun updateLineageDatasourceMetadataId(lineageDatasourceId: Long, metadataDataSourceId: Long) {
        val sql = """
            UPDATE lineage_datasources 
            SET metadata_data_source_id = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """.trimIndent()

        val updateCount = jdbcTemplate.update(sql, metadataDataSourceId, lineageDatasourceId)

        if (updateCount > 0) {
            logger.info("k1l2m3n4 | 更新血缘数据源metadata_data_source_id成功: lineageDatasourceId=$lineageDatasourceId, metadataDataSourceId=$metadataDataSourceId")
        } else {
            logger.warn("o5p6q7r8 | 更新血缘数据源metadata_data_source_id失败: lineageDatasourceId=$lineageDatasourceId, metadataDataSourceId=$metadataDataSourceId")
        }
    }

    /**
     * 创建新的血缘数据源
     */
    private fun createNewLineageDatasource(
        metadataDataSource: MetadataDataSourceDto,
        dbType: String,
        host: String,
        port: Int,
        databaseName: String
    ): Long {
        val keyHolder = GeneratedKeyHolder()

        // 使用metadata_data_source.SOURCE_NAME作为datasource_name
        val datasourceName = metadataDataSource.sourceName
        val connectionString = buildConnectionString(metadataDataSource, dbType, host, port, databaseName)

        jdbcTemplate.update({ connection ->
            val ps = connection.prepareStatement(
                """
                INSERT INTO lineage_datasources (
                    datasource_name, db_type, host, port, database_name, 
                    connection_string, metadata_data_source_id, status
                ) VALUES (?, ?, ?, ?, ?, ?, ?, 'ACTIVE')
                """.trimIndent(),
                Statement.RETURN_GENERATED_KEYS
            )
            ps.setString(1, datasourceName)
            ps.setString(2, dbType)
            ps.setString(3, host)
            ps.setInt(4, port)
            ps.setString(5, databaseName)
            ps.setString(6, connectionString)
            ps.setLong(7, metadataDataSource.id)
            ps
        }, keyHolder)

        return keyHolder.key!!.toLong()
    }

    /**
     * 构建连接字符串
     */
    private fun buildConnectionString(
        metadataDataSource: MetadataDataSourceDto,
        dbType: String,
        host: String,
        port: Int,
        databaseName: String
    ): String {
        // 如果CUSTOM_JDBC_URL不为空，优先使用它并追加数据库名称
        if (!metadataDataSource.customJdbcUrl.isNullOrBlank()) {
            val customUrl = metadataDataSource.customJdbcUrl!!

            // 检查URL是否已经包含数据库名称
            return if (customUrl.endsWith("/$databaseName") || customUrl.contains("/$databaseName?") || customUrl.contains(
                    "/$databaseName;"
                )
            ) {
                customUrl
            } else {
                // 追加数据库名称
                when {
                    customUrl.endsWith("/") -> "$customUrl$databaseName"
                    customUrl.contains("?") || customUrl.contains(";") -> {
                        // URL中包含参数，在参数前插入数据库名
                        val baseUrl = customUrl.substringBefore("?").substringBefore(";")
                        val params = customUrl.substring(baseUrl.length)
                        "$baseUrl/$databaseName$params"
                    }

                    else -> "$customUrl/$databaseName"
                }
            }
        }

        // 如果有DB_URL，使用它并追加数据库名称
        if (!metadataDataSource.dbUrl.isNullOrBlank()) {
            val dbUrl = metadataDataSource.dbUrl!!
            return if (dbUrl.endsWith("/$databaseName") || dbUrl.contains("/$databaseName?") || dbUrl.contains("/$databaseName;")) {
                dbUrl
            } else {
                when {
                    dbUrl.endsWith("/") -> "$dbUrl$databaseName"
                    dbUrl.contains("?") || dbUrl.contains(";") -> {
                        val baseUrl = dbUrl.substringBefore("?").substringBefore(";")
                        val params = dbUrl.substring(baseUrl.length)
                        "$baseUrl/$databaseName$params"
                    }

                    else -> "$dbUrl/$databaseName"
                }
            }
        }

        // 默认构建标准JDBC URL
        return when (dbType.lowercase()) {
            "hive" -> "**************************************"
            "mysql" -> "**************************************"
            "oracle" -> "*******************************************"
            "postgresql" -> "*******************************************"
            else -> "jdbc:$dbType://$host:$port/$databaseName"
        }
    }

    /**
     * MetadataDataSourceDto 行映射器
     */
    private val metadataDataSourceRowMapper = RowMapper<MetadataDataSourceDto> { rs, _ ->
        MetadataDataSourceDto(
            id = rs.getLong("ID"),
            sourceName = rs.getString("SOURCE_NAME"),
            dbType = rs.getString("DB_TYPE"),
            dbDriver = rs.getString("DB_DRIVER"),
            dbName = rs.getString("DB_NAME"),
            dbUrl = rs.getString("DB_URL"),
            dbPort = rs.getObject("DB_PORT") as? Int,
            dbUsername = rs.getString("DB_USERNAME"),
            customJdbcUrl = rs.getString("CUSTOM_JDBC_URL"),
            activeFlag = rs.getBoolean("ACTIVE_FLAG"),
            createBy = rs.getString("CREATE_BY"),
            createTime = rs.getTimestamp("CREATE_TIME")?.toLocalDateTime(),
            updateBy = rs.getString("UPDATE_BY"),
            updateTime = rs.getTimestamp("UPDATE_TIME")?.toLocalDateTime(),
            systemId = rs.getObject("SYSTEM_ID") as? Long,
            description = rs.getString("DESCRIPTION"),
            systemInfo = null, // 不需要系统信息
            hasBeenCollected = false // 默认值
        )
    }
}

/**
 * 数据库名称匹配结果 (Database Name Matching Result)
 */
data class DatabaseNameMatchingResult(
    val lineageDatasourceId: Long,
    val metadataDataSourceId: Long,
    val databaseName: String,
    val isNewlyCreated: Boolean,
    val processedAt: LocalDateTime = LocalDateTime.now()
)