package com.datayes.task

import org.springframework.data.jdbc.repository.query.Query
import org.springframework.data.repository.CrudRepository
import org.springframework.data.repository.query.Param
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.stereotype.Repository
import java.sql.ResultSet
import java.time.LocalDateTime

/**
 * 执行日志数据访问接口 (Execution Log Repository Interface)
 */
interface ExecutionLogRepository : CrudRepository<ExecutionLog, Long> {

    /**
     * 根据任务ID查找最新的执行日志
     */
    @Query("SELECT * FROM lineage_execution_logs WHERE task_id = :taskId ORDER BY created_at DESC LIMIT 1")
    fun findLatestByTaskId(@Param("taskId") taskId: Long): ExecutionLog?

    /**
     * 根据任务ID和执行ID查找执行日志
     */
    @Query("SELECT * FROM lineage_execution_logs WHERE task_id = :taskId AND execution_id = :executionId ORDER BY created_at DESC LIMIT 1")
    fun findByTaskIdAndExecutionId(@Param("taskId") taskId: Long, @Param("executionId") executionId: String): ExecutionLog?

    /**
     * 根据任务ID查找所有执行日志，按创建时间倒序
     */
    @Query("SELECT * FROM lineage_execution_logs WHERE task_id = :taskId ORDER BY created_at DESC")
    fun findAllByTaskIdOrderByCreatedAtDesc(@Param("taskId") taskId: Long): List<ExecutionLog>

    /**
     * 根据任务ID和状态查找最新的执行日志
     */
    @Query("SELECT * FROM lineage_execution_logs WHERE task_id = :taskId AND task_status = :status ORDER BY created_at DESC LIMIT 1")
    fun findLatestByTaskIdAndStatus(@Param("taskId") taskId: Long, @Param("status") status: String): ExecutionLog?
}

/**
 * 执行日志自定义数据访问实现 (Custom Execution Log Repository Implementation)
 */
@Repository
class ExecutionLogCustomRepository(private val jdbcTemplate: JdbcTemplate) {

    /**
     * 创建执行日志
     */
    fun createExecutionLog(request: CreateExecutionLogRequest): Long {
        val sql = """
            INSERT INTO lineage_execution_logs (
                task_id, execution_id, log_level, log_message, execution_step,
                started_at, task_status, database_connection_string, sql_queries,
                source_job_id, source_job_type, additional_info, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """.trimIndent()

        val keyHolder = org.springframework.jdbc.support.GeneratedKeyHolder()
        jdbcTemplate.update({ connection ->
            val ps = connection.prepareStatement(sql, arrayOf("id"))
            ps.setLong(1, request.taskId)
            ps.setString(2, request.executionId)
            ps.setString(3, request.logLevel.name)
            ps.setString(4, request.logMessage)
            ps.setString(5, request.executionStep)
            ps.setObject(6, request.startedAt)
            ps.setString(7, request.taskStatus?.name)
            ps.setString(8, request.databaseConnectionString)
            ps.setString(9, request.sqlQueries?.let { serializeToJson(it) })
            ps.setString(10, request.sourceJobId)
            ps.setString(11, request.sourceJobType?.name)
            ps.setString(12, request.additionalInfo?.let { serializeToJson(it) })
            ps.setObject(13, LocalDateTime.now())
            ps
        }, keyHolder)

        return keyHolder.key?.toLong() ?: throw RuntimeException("Failed to create execution log")
    }

    /**
     * 根据执行ID更新执行日志
     */
    fun updateExecutionLogByExecutionId(executionId: String, request: UpdateExecutionLogRequest): Boolean {
        val setParts = mutableListOf<String>()
        val args = mutableListOf<Any?>()

        request.logMessage?.let {
            setParts.add("log_message = ?")
            args.add(it)
        }

        request.completedAt?.let {
            setParts.add("completed_at = ?")
            args.add(it)
        }

        request.taskStatus?.let {
            setParts.add("task_status = ?")
            args.add(it.name)
        }

        request.processingTimeMs?.let {
            setParts.add("processing_time_ms = ?")
            args.add(it)
        }

        request.exceptionStack?.let {
            setParts.add("exception_stack = ?")
            args.add(it)
        }

        request.additionalInfo?.let {
            setParts.add("additional_info = ?")
            args.add(serializeToJson(it))
        }

        request.executionStep?.let {
            setParts.add("execution_step = ?")
            args.add(it)
        }

        if (setParts.isEmpty()) {
            return false
        }

        args.add(executionId)
        val sql = "UPDATE lineage_execution_logs SET ${setParts.joinToString(", ")} WHERE execution_id = ?"

        return jdbcTemplate.update(sql, *args.toTypedArray()) > 0
    }

    /**
     * 获取任务的执行统计信息
     */
    fun getTaskExecutionStats(taskId: Long): TaskExecutionStats? {
        val sql = """
            SELECT 
                task_id,
                COUNT(*) as execution_count,
                SUM(CASE WHEN task_status = 'SUCCESS' THEN 1 ELSE 0 END) as success_count,
                SUM(CASE WHEN task_status = 'FAILED' THEN 1 ELSE 0 END) as failed_count,
                MAX(CASE WHEN task_status = 'SUCCESS' THEN created_at END) as last_success_at,
                MAX(CASE WHEN task_status = 'FAILED' THEN created_at END) as last_failed_at,
                MAX(created_at) as last_execution_at
            FROM lineage_execution_logs 
            WHERE task_id = ?
            GROUP BY task_id
        """.trimIndent()

        return jdbcTemplate.query(sql, TaskExecutionStatsRowMapper(), taskId).firstOrNull()
    }

    /**
     * 获取任务的最新执行日志信息（包含成功和失败的最新记录）
     */
    fun getTaskLatestExecutionInfo(taskId: Long): TaskLatestExecutionInfo? {
        // 先获取基本统计信息
        val stats = getTaskExecutionStats(taskId) ?: return null

        // 获取最新执行日志
        val latestLog = jdbcTemplate.query(
            "SELECT * FROM lineage_execution_logs WHERE task_id = ? ORDER BY created_at DESC LIMIT 1",
            ExecutionLogRowMapper(),
            taskId
        ).firstOrNull()

        // 获取最新成功执行日志
        val lastSuccessLog = jdbcTemplate.query(
            "SELECT * FROM lineage_execution_logs WHERE task_id = ? AND task_status = 'SUCCESS' ORDER BY created_at DESC LIMIT 1",
            ExecutionLogRowMapper(),
            taskId
        ).firstOrNull()

        // 获取最新失败执行日志
        val lastFailedLog = jdbcTemplate.query(
            "SELECT * FROM lineage_execution_logs WHERE task_id = ? AND task_status = 'FAILED' ORDER BY created_at DESC LIMIT 1",
            ExecutionLogRowMapper(),
            taskId
        ).firstOrNull()

        return TaskLatestExecutionInfo(
            taskId = taskId,
            executionCount = stats.executionCount,
            successCount = stats.successCount,
            failedCount = stats.failedCount,
            latestExecution = latestLog,
            lastSuccessExecution = lastSuccessLog,
            lastFailedExecution = lastFailedLog
        )
    }

    private fun serializeToJson(obj: Any): String {
        return com.fasterxml.jackson.module.kotlin.jacksonObjectMapper().writeValueAsString(obj)
    }
}

/**
 * 执行日志行映射器 (Execution Log Row Mapper)
 */
class ExecutionLogRowMapper : RowMapper<ExecutionLog> {
    override fun mapRow(rs: ResultSet, rowNum: Int): ExecutionLog {
        return ExecutionLog(
            id = rs.getLong("id"),
            taskId = rs.getLong("task_id"),
            executionId = rs.getString("execution_id"),
            logLevel = LogLevel.valueOf(rs.getString("log_level")),
            logMessage = rs.getString("log_message"),
            exceptionStack = rs.getString("exception_stack"),
            executionStep = rs.getString("execution_step"),
            processingTimeMs = rs.getObject("processing_time_ms") as Long?,
            startedAt = rs.getTimestamp("started_at")?.toLocalDateTime(),
            completedAt = rs.getTimestamp("completed_at")?.toLocalDateTime(),
            taskStatus = rs.getString("task_status")?.let { TaskStatus.valueOf(it) },
            databaseConnectionString = rs.getString("database_connection_string"),
            sqlQueries = rs.getString("sql_queries"),
            sourceJobId = rs.getString("source_job_id"),
            sourceJobType = rs.getString("source_job_type")?.let { SourceJobType.valueOf(it) },
            additionalInfo = rs.getString("additional_info"),
            createdAt = rs.getTimestamp("created_at").toLocalDateTime()
        )
    }
}

/**
 * 任务执行统计信息行映射器 (Task Execution Stats Row Mapper)
 */
class TaskExecutionStatsRowMapper : RowMapper<TaskExecutionStats> {
    override fun mapRow(rs: ResultSet, rowNum: Int): TaskExecutionStats {
        return TaskExecutionStats(
            taskId = rs.getLong("task_id"),
            executionCount = rs.getInt("execution_count"),
            successCount = rs.getInt("success_count"),
            failedCount = rs.getInt("failed_count"),
            lastSuccessAt = rs.getTimestamp("last_success_at")?.toLocalDateTime(),
            lastFailedAt = rs.getTimestamp("last_failed_at")?.toLocalDateTime(),
            lastExecutionAt = rs.getTimestamp("last_execution_at")?.toLocalDateTime()
        )
    }
}

/**
 * 任务执行统计信息 (Task Execution Statistics)
 */
data class TaskExecutionStats(
    val taskId: Long,
    val executionCount: Int,
    val successCount: Int,
    val failedCount: Int,
    val lastSuccessAt: LocalDateTime?,
    val lastFailedAt: LocalDateTime?,
    val lastExecutionAt: LocalDateTime?
)

/**
 * 任务最新执行信息 (Task Latest Execution Information)
 */
data class TaskLatestExecutionInfo(
    val taskId: Long,
    val executionCount: Int,
    val successCount: Int,
    val failedCount: Int,
    val latestExecution: ExecutionLog?,
    val lastSuccessExecution: ExecutionLog?,
    val lastFailedExecution: ExecutionLog?
)