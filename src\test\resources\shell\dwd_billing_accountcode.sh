#!/bin/bash
hdfs dfs -cat /project/dwd/tbds_config.properties > tbds_config.properties
source tbds_config.properties
hdfs dfs -cat /project/dwd/tbds_config.sh > tbds_config.sh
source tbds_config.sh

#所属主题：财务信息
#功能描述；内部科目对照表
#创建者；胡静
#创建日期；2025-02-10
#修改日志
#修改日期                         修改人                         修改内容
#
#表字段
#
#会计科目编号      account_code
#会计科目名称      account_name
#会计科目级次      account_grade
#上级科目编号      superior_account_code
#上级科目名称      superior_account_name
#会计科目类型代码  account_type_code
#会计科目类型      account_type
#会计科目状态      enabled_flag
#科目停用日期      disabled_date


${hive} -e "

insert overwrite table dwd.dwd_billing_accountcode
select 
      coa1.flex_value                                                                                               as account_code
      ,coa1.description                                                                                             as account_name
      ,nvl(coa1.account_level,'空')                                                                                 as account_grade
      ,coa1.f_flex_value                                                                                            as superior_account_code
      ,coa2.description                                                                                             as superior_account_name
      ,coa1.account_type                                                                                            as account_type_code
      ,dic.value                                                                                                    as account_type
      ,coa1.enabled_flag                                                                                            as enabled_flag
      ,case when coa1.enabled_flag='N' then replace(to_date(coa1.last_update_date),'-','') else '' end              as disabled_date       
      ,coa1.operated_by                                                                                             as operator
      ,coa1.created_date                                                                                            as make_time
      ,coa1.operated_date                                                                                           as modify_time
      ,from_unixtime(unix_timestamp(),'yyyy-mm-dd hh:mm:ss')                                                        as first_hadoop_opt_time
      ,from_unixtime(unix_timestamp(),'yyyy-mm-dd hh:mm:ss')                                                        as last_hadoop_opt_time
   from  ms_ods_ebs.hpods_coa  coa1
   left join ms_ods_ebs.hpods_coa  coa2 on coa1.f_flex_value=coa2.flex_value and coa2.d_ums = 'MS_ACCOUNT'
   left join (select code,value from dwd.dim_dictionary where type = 'account_type') dic on coa1.account_type = dic.code
   where coa1.d_ums = 'MS_ACCOUNT'  
   and coa1.flex_value not in ('********','121300','********','********','********','********','********','1217','121701','********','********',
    '********','********','********','********','224122','********','224123','********','260100','********','********','********','********'
    ,'********','260401','********','********','********','********','607102','********','********','********','********','********','********'
    ,'641106','********','650100','********','********','********','********','********','650301','********','********','********','********'
    ,'********','670103','********','********','********','********','********','180107','********','********')
              ;
              
"
exitCodeCheck $? "内部科目对照表数据提取 失败！" "内部科目对照表数据提取 成功！"