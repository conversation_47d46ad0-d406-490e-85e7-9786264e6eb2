package com.datayes.task

import com.datayes.ApiResponse
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import io.swagger.v3.oas.annotations.Operation

/**
 * Excel测试控制器 (Excel Test Controller)
 *
 * 提供Excel文件解析测试的REST API接口，用于验证Excel文件格式和解析结果
 */
@RestController
@RequestMapping("/api/v1/excel-test")
@CrossOrigin(origins = ["*"])
class ExcelTestController(
    private val manualLineageImportService: ManualLineageImportService
) {

    private val logger = LoggerFactory.getLogger(ExcelTestController::class.java)

    /**
     * 测试Excel血缘文件解析 (Test Excel lineage file parsing)
     * 
     * 仅用于测试Excel文件解析功能，不会创建任务或保存数据
     * 
     * Excel文件要求：
     * 1. 必须包含标题行，包含以下列（顺序不限）：
     *    - 源数据源信息、源Schema、源表名、源字段名
     *    - 目标数据源信息、目标Schema、目标表名、目标字段名
     * 2. 标题行可以在文件的前20行任意位置
     * 3. 支持 .xlsx 和 .xls 格式
     * 4. 文件大小不超过10MB
     * 
     * @param file 上传的Excel文件
     * @return 解析结果预览，包含血缘摘要和验证结果
     */
    @PostMapping("/parse-excel", consumes = ["multipart/form-data"])
    @Operation(
        summary = "测试Excel血缘文件解析",
        description = "上传Excel文件进行解析测试，不会创建任务或保存数据，仅返回解析结果")
    fun testExcelParsing(
        @RequestParam("file") file: MultipartFile
    ): ResponseEntity<ApiResponse<ExcelParsingTestResult>> {
        return try {
            logger.info("d4e8f2a9 | 接收到Excel解析测试请求: fileName=${file.originalFilename}")

            if (file.isEmpty) {
                return ResponseEntity.ok(ApiResponse.error("上传文件不能为空"))
            }

            val fileName = file.originalFilename ?: "unknown_file"
            
            // 验证文件格式
            if (!fileName.endsWith(".xlsx", ignoreCase = true) && !fileName.endsWith(".xls", ignoreCase = true)) {
                return ResponseEntity.ok(ApiResponse.error("仅支持Excel文件格式 (.xlsx, .xls)"))
            }

            val startTime = System.currentTimeMillis()
            
            // 调用解析服务
            val lineageData = manualLineageImportService.parseLineageFile(file)
            
            val processingTime = System.currentTimeMillis() - startTime

            // 构建测试结果
            val result = ExcelParsingTestResult(
                fileName = fileName,
                fileSize = file.size,
                processingTimeMs = processingTime,
                lineageCount = lineageData.size,
                lineageSummary = lineageData.map { lineage ->
                    LineageSummary(
                        jobId = lineage.jobId,
                        jobName = lineage.jobName,
                        sourceDatabase = "${lineage.sourceDatabase.dbType}://${lineage.sourceDatabase.host}:${lineage.sourceDatabase.port}/${lineage.sourceDatabase.databaseName}",
                        targetDatabase = "${lineage.targetDatabase.dbType}://${lineage.targetDatabase.host}:${lineage.targetDatabase.port}/${lineage.targetDatabase.databaseName}",
                        sourceTables = lineage.tableLineage.sourceTables.map { 
                            "${it.schema?.let { s -> "$s." } ?: ""}${it.tableName}"
                        },
                        targetTable = "${lineage.tableLineage.targetTable.schema?.let { s -> "$s." } ?: ""}${lineage.tableLineage.targetTable.tableName}",
                        columnMappingCount = lineage.columnLineages.size,
                        columnMappings = lineage.columnLineages.map { 
                            "${it.sourceColumn.columnName} -> ${it.targetColumn.columnName}"
                        }
                    )
                },
                validation = try {
                    manualLineageImportService.validateLineageData(lineageData)
                    ValidationResult(isValid = true, errorMessage = null)
                } catch (e: Exception) {
                    ValidationResult(isValid = false, errorMessage = e.message)
                }
            )

            logger.info("7c1b5e3f | Excel解析测试完成: fileName=$fileName, 解析血缘数=${lineageData.size}, 处理时间=${processingTime}ms")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = result,
                    message = "Excel文件解析成功"
                )
            )

        } catch (e: IllegalArgumentException) {
            logger.warn("a8f3d7e2 | Excel解析测试参数错误", e)
            ResponseEntity.ok(ApiResponse.error("文件解析错误: ${e.message}"))

        } catch (e: Exception) {
            logger.error("2b9e4f8c | Excel解析测试时发生错误", e)
            ResponseEntity.ok(ApiResponse.error("解析失败: ${e.message}"))
        }
    }
}