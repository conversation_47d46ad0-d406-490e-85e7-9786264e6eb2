package com.datayes.shell

import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

class ShellScriptParserTest {

    @Test
    fun `test parseShellScriptForSql with multiline hive command and variable substitution`() {
        val testScript = """
        #!/bin/bash
        export my_var="test_value"
        hive -e "
        SELECT *
        FROM my_table
        WHERE col = '${'$'}{my_var}';
        "
        """.trimIndent()
        val expectedSql = """
        SELECT *
        FROM my_table
        WHERE col = 'test_value';
        """.trimIndent()
        val sqls = ShellScriptParser.parseShellScriptForSql(testScript)
        assertEquals(1, sqls.size)
        assertEquals(expectedSql, sqls[0].trim())
    }

    @Test
    fun `test parseShellScriptForSql with single line hive command`() {
        val testScript = """
        #!/bin/bash
        export my_var="single_line_test"
        hive -e "SELECT col FROM another_table WHERE id = '${'$'}{my_var}';"
        """.trimIndent()
        val expectedSql = "SELECT col FROM another_table WHERE id = 'single_line_test';"
        val sqls = ShellScriptParser.parseShellScriptForSql(testScript)
        assertEquals(1, sqls.size)
        assertEquals(expectedSql, sqls[0].trim())
    }

    @Test
    fun `test substituteVariables basic`() {
        val text = "SELECT * FROM table WHERE date = '\${dt}' AND region = '\$REGION';"
        val variables = mapOf("dt" to "2023-01-01", "REGION" to "US")
        val expected = "SELECT * FROM table WHERE date = '2023-01-01' AND region = 'US';"
        assertEquals(expected, ShellScriptParser.substituteVariables(text, variables))
    }

    @Test
    fun `test substituteVariables recursive`() {
        val text = "SELECT * FROM table WHERE path = '\${base_path}/\${sub_dir}';"
        val variables = mapOf("base_path" to "/data/\${year}", "sub_dir" to "logs", "year" to "2023")
        val expected = "SELECT * FROM table WHERE path = '/data/2023/logs';"
        assertEquals(expected, ShellScriptParser.substituteVariables(text, variables))
    }

    @Test
    @Disabled("Not Support read args from command line")
    fun `test parseShellScriptForSql with initialVariables for script argument`() {
        val scriptContent = """
        #!/bin/bash
        export yesterday=\$1
        export db_name="my_db"
        
        hive -e "
        USE ${'$'}{db_name};
        SELECT * FROM sales WHERE dt = '${'$'}{yesterday}';
        "
        """.trimIndent()
        val initialVars = mapOf("1" to "2023-10-26")
        val expectedSql = """
        USE my_db;
        SELECT * FROM sales WHERE dt = '2023-10-26';
        """.trimIndent()
        val sqls = ShellScriptParser.parseShellScriptForSql(scriptContent, initialVars)
        assertEquals(1, sqls.size)
        assertEquals(expectedSql, sqls[0].trim())
    }

    @Test
    @Disabled("Not Support read args from command line")
    fun `test parseShellScriptForSql with yesterday_arg as initialVariable`() {
        val scriptContent = """
        #!/bin/bash
        export yesterday=\$1 
        # If yesterday_arg is provided, it should be used for $1 if $1 is not directly set
        
        hive -e "
        SELECT * FROM events WHERE event_date = '${'$'}{yesterday}';
        "
        """.trimIndent()
        val initialVars = mapOf("yesterday_arg" to "2024-01-15")
        val expectedSql = "SELECT * FROM events WHERE event_date = '2024-01-15';"
        val sqls = ShellScriptParser.parseShellScriptForSql(scriptContent, initialVars)
        assertEquals(1, sqls.size)
        assertEquals(expectedSql, sqls[0].trim())
    }

    @Test
    fun `test parseShellScriptForSql with multiline variable assignment used in sql`() {
        val scriptContent = """
        export START_DATE="2023-01-01"
        export END_DATE="2023-01-31"
        
        export SQL_FILTER="date >= '${'$'}{START_DATE}'
        AND date <= '${'$'}{END_DATE}'
        AND status = 'processed'"
        
        hive -e "
        SELECT 
            id,
            name,
            value
        FROM
            source_table
        WHERE
            ${'$'}{SQL_FILTER}
        ORDER BY id;
        "
        """.trimIndent()

        val expectedSql = """
        SELECT 
            id,
            name,
            value
        FROM
            source_table
        WHERE
            date >= '2023-01-01'
        AND date <= '2023-01-31'
        AND status = 'processed'
        ORDER BY id;
        """.trimIndent()

        val sqls = ShellScriptParser.parseShellScriptForSql(scriptContent)
        assertEquals(1, sqls.size)
        val normalize = { s: String -> s.replace("\\s+".toRegex(), " ").trim() }
        assertEquals(normalize(expectedSql), normalize(sqls[0]))
    }

    @Test
    fun `test parseShellScriptForSql no hive command`() {
        val scriptContent = """
        export VAR="value"
        echo "No hive command here"
        """.trimIndent()
        val sqls = ShellScriptParser.parseShellScriptForSql(scriptContent)
        assertEquals(0, sqls.size)
    }

    @Test
    fun `test parseShellScriptForSql with variable not defined`() {
        val scriptContent = """
        hive -e "SELECT * FROM ${'$'}{UNDEFINED_VAR};"
        """.trimIndent()
        val expectedSql = "SELECT * FROM \${UNDEFINED_VAR};"
        val sqls = ShellScriptParser.parseShellScriptForSql(scriptContent)
        assertEquals(1, sqls.size)
        assertEquals(expectedSql, sqls[0].trim())
    }

    // test parse a shell script on classpath
    @Test
    fun `test parseShellScriptForSql with classpath script`() {
        val scriptContent = ShellScriptParser::class.java.getResource("/shell/dws_trans_ljapolpay.sh")!!.readText()
        val sqls = ShellScriptParser.parseShellScriptForSql(scriptContent)
        assertEquals(1, sqls.size)

        assertTrue(sqls[0].contains("insert overwrite table urp_dws"))
    }

    // test for dwd_endorse_ljloanrepayment.sh
    @Test
    fun `test parseShellScriptForSql with dwd_endorse_ljloanrepayment script`() {
        val scriptContent =
            ShellScriptParser::class.java.getResource("/shell/dwd_endorse_ljloanrepayment.sh")!!.readText()
        val sqls = ShellScriptParser.parseShellScriptForSql(scriptContent)

        assertEquals(2, sqls.size)
        assertTrue(sqls[0].contains("with lpedoritem_group_eece"))
        assertTrue(sqls[1].contains("insert overwrite table dwd.dwd_endorse_ljloanrepayment"))
    }

    @Test
    fun `test parseShellScriptForSql with malformed single quote variables`() {
        val scriptContent = """
        #!/bin/bash
        
        # Test variables that previously caused StringIndexOutOfBoundsException
        sql_normal="
        sql_del="
        sql_yj_risk="
        
        # Also test single quotes
        test_var='
        another_var='
        
        # Valid usage should still work
        valid_var="select * from table"
        
        echo "Script with malformed variables"
        """.trimIndent()

        // This test ensures that the parser doesn't crash with StringIndexOutOfBoundsException
        // when encountering malformed quoted variables with length 1
        val sqls = ShellScriptParser.parseShellScriptForSql(scriptContent)
        
        // Should not crash and return empty list since no hive commands present
        assertEquals(0, sqls.size, "Should return empty list for script without hive commands")
    }

    @Test
    fun `test parseShellScriptForSql trailing quote issue`() {
        val scriptContent = """
        ${'$'}{hive} -e "
        insert into urp_bui_prip.temp_lcpoltransaction
        select * from 
        (select *, row_number() over(partition by policyno, posipolicyno order by posipolicyno asc) as rownum1
          from (select * from (select trans.policyno,trans.posipolicyno from dwd.dwd_trans_lcpoltransaction trans
          where length(nvl(trans.policyno,''))>0) a ) b where b.rownum1 = 1;" 
        """.trimIndent()
        
        val sqls = ShellScriptParser.parseShellScriptForSql(scriptContent)
        assertEquals(1, sqls.size)
        
        val extractedSql = sqls[0].trim()
        println("Extracted SQL: '$extractedSql'")
        
        // The extracted SQL should NOT end with a quote
        assertFalse(extractedSql.endsWith("\""), "Extracted SQL should not end with a quote")
        assertFalse(extractedSql.endsWith(";\""), "Extracted SQL should not end with ';\"'")
        
        // It should end with a semicolon
        assertTrue(extractedSql.endsWith("1;"), "Extracted SQL should end with the proper semicolon")
    }

    @Test
    fun `test extractSqlFromShellScript with HdfsShellScriptLineageConverter`() {
        // Test the specific method mentioned in the issue
        val scriptContent = """
        ${'$'}{hive} -e "
        insert into urp_bui_prip.temp_lcpoltransaction
        select * from 
        ...
          where length(nvl(trans.policyno,''))>0) a ) b where b.rownum1 = 1;" 
        """.trimIndent()
        
        val warnings = mutableListOf<String>()
        val sqls = com.datayes.hdfs.HdfsShellScriptLineageConverter.extractSqlFromShellScript(scriptContent, warnings)
        
        assertEquals(1, sqls.size)
        val extractedSql = sqls[0].trim()
        
        // The extracted SQL should NOT end with a quote
        assertFalse(extractedSql.endsWith("\""), "Extracted SQL should not end with a quote from HdfsShellScriptLineageConverter")
        assertFalse(extractedSql.endsWith(";\""), "Extracted SQL should not end with ';\"'")
    }

    @Test
    fun `test parseShellScriptForSql with impala-shell single line command`() {
        val testScript = """
        #!/bin/bash
        export my_var="impala_test"
        impala-shell ${'$'}{haproxy} -q "SELECT col FROM impala_table WHERE id = '${'$'}{my_var}';"
        """.trimIndent()
        val expectedSql = "SELECT col FROM impala_table WHERE id = 'impala_test';"
        val sqls = ShellScriptParser.parseShellScriptForSql(testScript)
        assertEquals(1, sqls.size)
        assertEquals(expectedSql, sqls[0].trim())
    }

    @Test
    fun `test parseShellScriptForSql with impala-shell multiline command`() {
        val testScript = """
        #!/bin/bash
        export table_name="test_table"
        impala-shell ${'$'}{haproxy} -q "
        SELECT *
        FROM ${'$'}{table_name}
        WHERE status = 'active';
        "
        """.trimIndent()
        val expectedSql = """
        SELECT *
        FROM test_table
        WHERE status = 'active';
        """.trimIndent()
        val sqls = ShellScriptParser.parseShellScriptForSql(testScript)
        assertEquals(1, sqls.size)
        assertEquals(expectedSql, sqls[0].trim())
    }

    @Test
    fun `test parseShellScriptForSql with impala-shell without haproxy variable`() {
        val testScript = """
        #!/bin/bash
        export db_name="test_db"
        impala-shell -q "USE ${'$'}{db_name}; SELECT COUNT(*) FROM users;"
        """.trimIndent()
        val expectedSql = "USE test_db; SELECT COUNT(*) FROM users;"
        val sqls = ShellScriptParser.parseShellScriptForSql(testScript)
        assertEquals(1, sqls.size)
        assertEquals(expectedSql, sqls[0].trim())
    }

    @Test
    fun `test parseShellScriptForSql with both hive and impala-shell commands`() {
        val testScript = """
        #!/bin/bash
        export my_var="mixed_test"
        hive -e "SELECT * FROM hive_table WHERE name = '${'$'}{my_var}';"
        impala-shell ${'$'}{haproxy} -q "SELECT * FROM impala_table WHERE name = '${'$'}{my_var}';"
        """.trimIndent()
        val expectedHiveSql = "SELECT * FROM hive_table WHERE name = 'mixed_test';"
        val expectedImpalaSql = "SELECT * FROM impala_table WHERE name = 'mixed_test';"
        val sqls = ShellScriptParser.parseShellScriptForSql(testScript)
        assertEquals(2, sqls.size)
        assertEquals(expectedHiveSql, sqls[0].trim())
        assertEquals(expectedImpalaSql, sqls[1].trim())
    }

    @Test
    fun `test parseShellScriptForSql with malformed quoted variables - market risk script`() {
        val scriptContent = """
        #!/bin/bash

        #/********************************************************************
        #*模块名：        111415-鑫喜连恒数据基础表
        #*程序名：        etl_f_market_risk_daily_his.sh
        #*功能：          鑫喜连恒数据业绩数据(含24年预售数据)
        #*开发人：        867129390
        #*开发日期：      
        #*修改记录：      
        #*********************************************************************

        hdfs dfs -cat /project/bi/tbds_config.properties > tbds_config.properties
        # hdfs dfs -cat /project/bi/bi_params.properties > bi_params.properties
        source tbds_config.properties
        # source bi_params.properties


        #--------------这里写业务逻辑hive sql语句-----------------#
        :<<!\
        目标表：
        ms_bi.f_market_risk_daily_his
        sql注释：
        sql_presale 预售的逻辑
        sql_normal 正常的逻辑
        !
        #加入季度数据，备用
        #考虑到数据延时，准实时的批处理，s_lacommision 取到T+2的数据 f_yx_ljapayperson 取俩天的数据
        if [ ! ${'$'}1 ]; then
        daily=${'$'}(date "+%Y-%m-%d")
        else
        daily=${'$'}1
        fi

        unix_curtime=${'$'}(date -d "${'$'}{daily}" +%s)
        unix_endtime=${'$'}(date -d "2024-01-01" +%s)
        unix_starttime=${'$'}(date -d "2023-10-18" +%s)


        begin_date=${'$'}(date -d "${'$'}{daily}-2days" +%Y-%m-%d)
        yearmonth=${'$'}(date -d "${'$'}{daily}" "+%Y%m")
        monthly=${'$'}(date -d "${'$'}{daily}" "+%Y-%m-01")
        yearly=${'$'}(date -d "${'$'}{daily}" "+%Y-01-01")
        #季度判断
        mth=${'$'}(date -d "${'$'}{monthly}" "+%m")
        mth1=${'$'}(echo ${'$'}{mth}| sed -r 's/0*([0-9])/\1/')
        pmod=`expr ${'$'}{mth} % 3`
        if [ ${'$'}{pmod}  == 0 ]; then
        quarter=${'$'}(date -d "${'$'}{monthly} -2 month" "+%Y-%m-%d")
        elif [ ${'$'}{pmod}  == 2  ]; then
        quarter=${'$'}(date -d "${'$'}{monthly} -1 month" "+%Y-%m-%d")
        else
        quarter=${'$'}{monthly}
        fi

        sql_normal=" 
        select t.managecom,
                   t.riskcode,
                   t.salechnl,
                   t.standprem,
                   t.tmakedate dateid
              from ms_bi.s_lacommision t
             where t.tmakedate between '${'$'}{yearly}' and date_add(date_format('${'$'}{daily}','yyyy-MM-dd'),-2)
             and t.type='new' and t.riskcode = '111415'
             union all
        select t.managecom,
        t.riskcode,
        t.salechnl,
        t.standprem standprem,
        cast(t.dateid as date) dateid
        from ms_bi.f_yx_ljapayperson t
        where t.dateid between date_add(date_format('${'$'}{daily}','yyyy-MM-dd'),-1) and date_format('${'$'}{daily}','yyyy-MM-dd')
        and t.salechnl in('1011','1021')  and t.riskcode ='111415' "

        sql_presale=" union all 
            select t.managecom,
                   t.riskcode,
                   t.salechnlcode salechnl,
                   t.standprem,
                   cast(t.pay_made_date as date) dateid
             from  
             ms_bi.f_tempfee_mobile t
             where t.riskcode ='111415' "

        if [ ${'$'}unix_curtime -lt ${'$'}unix_endtime -a ${'$'}unix_curtime -ge ${'$'}unix_starttime ];then
         echo "统计期在23年10月18之后24年之前需要统计预售数据，加上预售逻辑"
        sql_normal="${'$'}{sql_normal}
        ${'$'}{sql_presale}
        "
        else 
         echo "统计期不含23年预售期间，正常的逻辑"
        fi
        sql_del="
        alter table ms_bi.f_market_risk_daily_his drop partition(yearmonth='${'$'}{yearmonth}');
        "
        sql_yj_risk="
        alter table ms_bi.f_market_risk_daily_his drop partition(etl_date='${'$'}{daily}');
        insert into table ms_bi.f_market_risk_daily_his partition(yearmonth,etl_date)
        (
        managecom,
        riskcode,
        riskname,
        salechnl,
        standprem_d,
        standprem_m,
        standprem_q,
        standprem_y,
        yearmonth,
        etl_date)
        select
        rsk.managecom,
        rsk.riskcode,
        rsk.riskname,
        rsk.salechnl,
        rsk.standprem_d,
        rsk.standprem_m,
        rsk.standprem_q,
        standprem_y,
        rsk.yearmonth,
        rsk.etl_date
         from ( select a.managecom,
                   a.riskcode,
                   b.riskname,
                   a.salechnl,
                   sum(case when a.dateid = '${'$'}{daily}' then a.standprem end) standprem_d,
                   sum(case when a.dateid between '${'$'}{monthly}' and '${'$'}{daily}' then a.standprem end) standprem_m,
                   sum(case when a.dateid between '${'$'}{quarter}' and '${'$'}{daily}' then a.standprem end) standprem_q,
                   sum(case when a.dateid between '${'$'}{yearly}' and '${'$'}{daily}' then a.standprem end) standprem_y,
                   '${'$'}{yearmonth}' yearmonth,
                   '${'$'}{daily}' etl_date
              from (  
          ${'$'}{sql_normal} 
        ) a
              inner join ms_bi.d_risk b
              on a.riskcode=b.riskcode
             group by a.managecom, a.riskcode, b.riskname, a.salechnl) rsk
        "
        echo ${'$'}{sql_yj_risk}
        day=`date -d "${'$'}{daily} +1 day" "+%d"`
        #月末历史数据处理，删除分区，把当月的分区全部删除，再插入月末数据
        if [ ${'$'}{day} = '01' ];
        then
            ${'$'}{hive} --silent=true --showWarnings=true -e "${'$'}{sql_del}"
            ${'$'}{hive} --silent=true --showWarnings=true -e "${'$'}{sql_yj_risk}"
        else
            ${'$'}{hive} --silent=true --showWarnings=true -e "${'$'}{sql_yj_risk}"
        fi

        res=${'$'}? #获取上一个语句执行结果
        if [ ${'$'}res -ne 0 ]; then #若执行结果不为0（即出错），则退出并返回错误代码
        echo "报错了"
          exit ${'$'}res
        fi


        #--------------业务逻辑hive sql语句结束-----------------#
        """.trimIndent()

        // This test ensures that the parser doesn't crash with StringIndexOutOfBoundsException
        // when encountering malformed quoted variables like sql_normal="
        val sqls = ShellScriptParser.parseShellScriptForSql(scriptContent)
        
        // The script contains SQL in variables but uses hive command execution
        // Should extract the SQL commands from hive -e calls
        assertTrue(sqls.size >= 0, "Parser should not crash and return empty or valid SQL list")
        
        // If SQL is extracted, it should contain the expected table operations
        if (sqls.isNotEmpty()) {
            val allSql = sqls.joinToString(" ")
            assertTrue(allSql.contains("ms_bi.f_market_risk_daily_his") || allSql.isEmpty(), 
                "If SQL is extracted, it should contain the target table name")
        }
    }
}