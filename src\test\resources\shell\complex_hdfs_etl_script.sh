#!/bin/bash
hdfs dfs -cat /project/urp_east/common/tbds_config.properties > tbds_config.properties
source tbds_config.properties
hdfs dfs -cat /project/urp_east/common/tbds_config.sh > tbds_config.sh
source tbds_config.sh

export BATCHNO=$1
echo "导入批次号：${BATCHNO}"

export SJBSPCH=$2
echo "上报批次号：${SJBSPCH}"

export date=$(date +%Y-%m-%d\ %H:%M:%S)

${hive} -e "truncate table urp_bui_east_temp.jhgj_grkhxxb"
exitCodeCheck $? 'grkhxxb清空失败！' 'grkhxxb清空成功！'

${hive} -e "
reload function;
insert overwrite table urp_bui_east_temp.jhgj_grkhxxb 
select '${SJBSPCH}' as SJBSPCH,OTHERSIGN as LSH,BXJGDM,BXJGMC,KHBH,urp_bui_east.FN_RDSTUOMIN(1,XM) AS XM,XB,to_date('9999-12-31') as CSRQ,ZJLX,case when length(nvl(ZJHM,''))=0 then '未记录' else urp_bui_east.FN_RDSTUOMIN(2,ZJHM) end as ZJHM,to_date(ZJYXQQ),to_date(ZJYXZQ),ZY,GJ,HYZK,'隐私' AS YDDH,'隐私' AS GDDH,DZYX,XL,GRNSR,substring(DZSZS,1,15) DZSZS,substring(DZSZDS,1,15) DZSZDS,DZSZQX,'隐私' AS JD,${SJBSPCH} 
from urp_bui_rds.rds_grkhxxb where BATCHNO='${BATCHNO}'"
exitCodeCheck $? 'grkhxxb推到JHGJ库失败！' 'grkhxxb推到JHGJ库成功！'

${hive} -e "truncate table urp_bui_east_temp.jhgj_khbddzb"
exitCodeCheck $? 'khbddzb清空失败！' 'khbddzb清空成功！'

${hive} -e "
reload function;
insert overwrite table urp_bui_east_temp.jhgj_khbddzb 
select '${SJBSPCH}' as SJBSPCH,OTHERSIGN as LSH,BXJGDM,BXJGMC,KHBH,KHLB,KHSX,TTBDH,GRBDH,BDTGXZ,BDZT,${SJBSPCH} 
from urp_bui_rds.rds_khbddzb where BATCHNO='${BATCHNO}'"
exitCodeCheck $? 'khbddzb推到JHGJ库失败！' 'khbddzb推到JHGJ库成功！'

${hive} -e "truncate table urp_bui_east_temp.jhgj_ttkhxxb"
exitCodeCheck $? 'ttkhxxb清空失败！' 'ttkhxxb清空成功！'

${hive} -e "
reload function;
insert overwrite table urp_bui_east_temp.jhgj_ttkhxxb 
select '${SJBSPCH}' as SJBSPCH,OTHERSIGN as LSH,BXJGDM,BXJGMC,KHBH,urp_bui_east.FN_RDSTUOMIN(1, FDDBRFZR) AS FDDBRFZR,TBDWMC,KHLB,JGZJLX,JGZJHM,to_date(ZJYXQQ),to_date(ZJYXZQ),
to_date(QYCLRQ),QYGMLX,DWXZ,XYFL,ZCZB,ZCDZ,QYDHHM,case when length(nvl(LXRXM,''))=0 then '未记录' else urp_bui_east.FN_RDSTUOMIN(1, LXRXM) end as LXRXM,'隐私' AS LXRSJHM,'隐私' AS LXRGHHM,CJSHTCBZ,DZSZS,DZSZDS,DZSZQX,JD,${SJBSPCH} 
from urp_bui_rds.rds_ttkhxxb where BATCHNO='${BATCHNO}'"
exitCodeCheck $? 'ttkhxxb推到JHGJ库失败！' 'ttkhxxb推到JHGJ库成功！'

exit 0 