# SpringDoc OpenAPI configuration for Kubernetes deployment with path prefix
springdoc.swagger-ui.path           = /swagger-ui.html
springdoc.api-docs.path             = /v3/api-docs
springdoc.swagger-ui.config-url     = /dgp-lineage-collector/v3/api-docs/swagger-config
springdoc.swagger-ui.url            = /dgp-lineage-collector/v3/api-docs

# Custom path prefix for HTML template URL generation in K8s deployment
app.k8s.path-prefix                 = /dgp-lineage-collector

spring.datasource.driver-class-name = com.mysql.cj.jdbc.Driver
spring.datasource.url               = jdbc:mysql://************:3306/dgp?useSSL=false&useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai
spring.datasource.username          = dbausr
spring.datasource.password          = J^DqUUzjbT$cMmYq

# HDFS Configuration
hdfs.tbds.username                  = ms_dgp
hdfs.tbds.secureid                  = AP2BCAoyJJ1JmHx6XOxCoLj3v7JMGoGkjbeF
hdfs.tbds.securekey                 = mD7IpdIXq6TLzXDzwqxzVy7nHHRu9z06
