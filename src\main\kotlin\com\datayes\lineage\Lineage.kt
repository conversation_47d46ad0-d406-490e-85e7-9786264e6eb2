package com.datayes.lineage

import com.fasterxml.jackson.annotation.JsonIgnore
import com.datayes.util.JdbcUrlParser
import org.slf4j.LoggerFactory
import java.time.LocalDateTime

/**
 * 数据血缘信息 (Data Lineage Information)
 *
 * 表示数据从源到目标的完整血缘关系，包含表级和列级映射
 *
 * @property jobId 作业标识符
 * @property jobName 作业名称
 * @property tableLineage 表级血缘关系
 * @property columnLineages 列级血缘关系列表
 * @property sourceDatabase 源数据库信息
 * @property targetDatabase 目标数据库信息
 * @property originalSql 原始SQL语句
 * @property createdAt 血缘信息创建时间
 */
data class DataLineage(
    val jobId: String,
    val jobName: String,
    val tableLineage: TableLineage,
    val columnLineages: List<ColumnLineage>,
    val sourceDatabase: DatabaseInfo,
    val targetDatabase: DatabaseInfo,
    val originalSql: String,
    val createdAt: LocalDateTime = LocalDateTime.now(),
)

/**
 * 表级血缘关系 (Table-level Lineage)
 *
 * 描述源表到目标表的数据流转关系
 *
 * @property sourceTables 源表信息列表
 * @property targetTable 目标表信息
 * @property lineageType 血缘类型
 */
data class TableLineage(
    val sourceTables: List<TableInfo>,
    val targetTable: TableInfo,
    val lineageType: LineageType = LineageType.DIRECT_COPY,
)

/**
 * 列级血缘关系 (Column-level Lineage)
 *
 * 描述源列到目标列的映射关系，包括数据转换信息
 *
 * @property sourceColumn 源列信息
 * @property targetColumn 目标列信息
 * @property transformation 数据转换描述
 * @property columnIndex 列在映射中的索引位置
 * @property id 列血缘ID（可选，用于避免重复查询）
 */
data class ColumnLineage(
    val sourceColumn: ColumnInfo,
    val targetColumn: ColumnInfo,
    val transformation: DataTransformation?,
    val columnIndex: Int,
    val id: Long? = null,
)

/**
 * 表信息 (Table Information)
 *
 * @property schema 模式名（可选）
 * @property tableName 表名
 * @property database 数据库信息
 * @property id 表ID（可选，用于避免重复查询）
 */
data class TableInfo(
    val schema: String?,
    val tableName: String,
    val database: DatabaseInfo,
    val id: Long? = null,
    val alias: String? = null,
) {
    /**
     * 获取完全限定的表名
     */
    @JsonIgnore
    fun getFullyQualifiedName(): String {
        return if (schema != null) {
            "${database.databaseName}.$schema.$tableName"
        } else {
            "${database.databaseName}.$tableName"
        }
    }

}

/**
 * 数据库连接信息 (Database Connection Information)
 *
 * 从JDBC连接字符串解析出的结构化信息
 *
 * @property dbType 数据库类型 (e.g., "mysql", "postgresql")
 * @property host 主机地址
 * @property port 端口号
 * @property databaseName 数据库名称
 * @property originalConnectionString 原始连接字符串
 */
data class DatabaseInfo(
    val dbType: String,
    val host: String,
    val port: Int,
    val databaseName: String,
    val originalConnectionString: String,
) {
    companion object {


        private val log = LoggerFactory.getLogger(DatabaseInfo::class.java)

        /**
         * 从JDBC连接字符串解析数据库信息
         *
         * @param jdbcUrl JDBC连接字符串
         * @return 解析后的数据库信息，解析失败时返回null
         */
        fun parseFromJdbcUrl(jdbcUrl: String): DatabaseInfo? {
            return try {
                log.debug("8f2e5a7c | 开始解析JDBC URL: $jdbcUrl")
                
                val parseResult = JdbcUrlParser.parseJdbcUrl(jdbcUrl)
                if (parseResult != null && parseResult.host != null && parseResult.port != null && parseResult.database != null) {
                    // 从JDBC URL中提取数据库类型
                    val dbType = when {
                        jdbcUrl.startsWith("jdbc:mysql://") -> "mysql"
                        jdbcUrl.startsWith("jdbc:hive2://") -> "hive2"
                        jdbcUrl.startsWith("jdbc:oracle:") -> "oracle"
                        jdbcUrl.startsWith("jdbc:postgresql://") -> "postgresql"
                        else -> {
                            // 尝试从URL中提取类型
                            val typeMatch = Regex("jdbc:(\\w+):").find(jdbcUrl)
                            typeMatch?.groupValues?.get(1) ?: "unknown"
                        }
                    }
                    
                    DatabaseInfo(
                        dbType = dbType,
                        host = parseResult.host,
                        port = parseResult.port,
                        databaseName = parseResult.database,
                        originalConnectionString = jdbcUrl
                    )
                } else {
                    log.warn("3d9c6f2a | JDBC URL解析失败: $jdbcUrl")
                    null
                }
            } catch (e: Exception) {
                log.error("7b4e8a1d | 解析JDBC URL时发生异常: $jdbcUrl", e)
                null
            }
        }
    }
}

/**
 * 列信息 (Column Information)
 *
 * @property columnName 列名
 * @property dataType 数据类型
 * @property comment 列注释或备注
 * @property table 所属表信息
 */
data class ColumnInfo(
    val columnName: String,
    val dataType: String,
    val comment: String?,
    val table: TableInfo,
) {
    /**
     * 获取完全限定的列名
     */
    @JsonIgnore
    fun getFullyQualifiedName(): String {
        return "${table.getFullyQualifiedName()}.$columnName"
    }
}

/**
 * 数据转换信息 (Data Transformation Information)
 *
 * 描述数据从源到目标的转换逻辑
 *
 * @property transformationType 转换类型
 * @property description 转换描述
 * @property expression SQL表达式（如果适用）
 */
data class DataTransformation(
    val transformationType: TransformationType,
    val description: String,
    val expression: String? = null,
)

/**
 * 血缘类型枚举 (Lineage Type Enumeration)
 */
enum class LineageType {
    /** 直接复制 */
    DIRECT_COPY,

    /** SQL查询 */
    SQL_QUERY,

    /** 数据聚合 */
    AGGREGATION,

    /** 数据连接 */
    JOIN,

    /** 数据过滤 */
    FILTER,

    /** 复杂转换 */
    COMPLEX_TRANSFORMATION,

    /** 数据移动 */
    DATA_MOVEMENT,

    /** 脚本处理 */
    SCRIPT_PROCESSING
}

/**
 * 转换类型枚举 (Transformation Type Enumeration)
 */
enum class TransformationType {
    /** 无转换（直接映射） */
    NONE,

    /** 类型转换 */
    TYPE_CAST,

    /** 函数计算 */
    FUNCTION,

    /** 表达式计算 */
    EXPRESSION,

    /** 常量赋值 */
    CONSTANT,

    /** 聚合计算 */
    AGGREGATION,

    /** 条件判断 */
    CONDITIONAL
}

/**
 * 血缘构建结果 (Lineage Construction Result)
 *
 * 包含构建过程的状态和可能的警告信息
 *
 * @property lineage 构建的血缘信息
 * @property warnings 构建过程中的警告列表
 * @property errors 构建过程中的错误列表
 * @property success 是否成功构建
 */
data class LineageResult(
    val lineage: DataLineage?,
    val warnings: List<String> = emptyList(),
    val errors: List<String> = emptyList(),
    val success: Boolean = lineage != null && errors.isEmpty(),
) 