package com.datayes.task

import com.fasterxml.jackson.annotation.JsonProperty
import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime

/**
 * 血缘执行日志实体 (Lineage Execution Log Entity)
 *
 * 记录LineageTask执行的完整生命周期信息，包括开始时间、结束时间、状态变化、
 * SQL查询语句、数据库连接信息等关键执行信息
 */
@Table("lineage_execution_logs")
data class ExecutionLog(

    @Id
    val id: Long = 0,

    @Column("task_id")
    val taskId: Long,

    @Column("execution_id")
    val executionId: String,

    @Column("log_level")
    val logLevel: LogLevel,

    @Column("log_message")
    val logMessage: String,

    @Column("exception_stack")
    val exceptionStack: String? = null,

    @Column("execution_step")
    val executionStep: String? = null,

    @Column("processing_time_ms")
    val processingTimeMs: Long? = null,

    @Column("started_at")
    val startedAt: LocalDateTime? = null,

    @Column("completed_at")
    val completedAt: LocalDateTime? = null,

    @Column("task_status")
    val taskStatus: TaskStatus? = null,

    @Column("database_connection_string")
    val databaseConnectionString: String? = null,

    @Column("sql_queries")
    val sqlQueries: String? = null, // JSON格式存储多个SQL查询

    @Column("source_job_id")
    val sourceJobId: String? = null,

    @Column("source_job_type")
    val sourceJobType: SourceJobType? = null,

    @Column("additional_info")
    val additionalInfo: String? = null, // JSON格式存储其他信息

    @Column("created_at")
    val createdAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 日志级别枚举 (Log Level Enum)
 */
enum class LogLevel {
    DEBUG,
    INFO,
    WARN,
    ERROR
}

/**
 * 来源作业类型枚举 (Source Job Type Enum)
 */
enum class SourceJobType {
    DATA_EXCHANGE_JOB,      // 数据交换作业
    HDFS_SHELL_SCRIPT_JOB   // HDFS Shell脚本作业
}

/**
 * 创建执行日志请求 (Create Execution Log Request)
 */
data class CreateExecutionLogRequest(
    val taskId: Long,
    val executionId: String,
    val logLevel: LogLevel,
    val logMessage: String,
    val executionStep: String? = null,
    val startedAt: LocalDateTime? = null,
    val taskStatus: TaskStatus? = null,
    val databaseConnectionString: String? = null,
    val sqlQueries: List<String>? = null,
    val sourceJobId: String? = null,
    val sourceJobType: SourceJobType? = null,
    val additionalInfo: Map<String, Any>? = null
) {
    fun toExecutionLog(): ExecutionLog {
        return ExecutionLog(
            taskId = taskId,
            executionId = executionId,
            logLevel = logLevel,
            logMessage = logMessage,
            executionStep = executionStep,
            startedAt = startedAt,
            taskStatus = taskStatus,
            databaseConnectionString = databaseConnectionString,
            sqlQueries = sqlQueries?.let { serializeToJson(it) },
            sourceJobId = sourceJobId,
            sourceJobType = sourceJobType,
            additionalInfo = additionalInfo?.let { serializeToJson(it) }
        )
    }

    private fun serializeToJson(obj: Any): String {
        return com.fasterxml.jackson.module.kotlin.jacksonObjectMapper().writeValueAsString(obj)
    }
}

/**
 * 更新执行日志请求 (Update Execution Log Request)
 */
data class UpdateExecutionLogRequest(
    val logMessage: String? = null,
    val completedAt: LocalDateTime? = null,
    val taskStatus: TaskStatus? = null,
    val processingTimeMs: Long? = null,
    val exceptionStack: String? = null,
    val additionalInfo: Map<String, Any>? = null,
    val executionStep: String? = null
)

/**
 * 执行日志查询响应 (Execution Log Query Response)
 */
data class ExecutionLogResponse(
    val id: Long,
    val taskId: Long,
    val executionId: String,
    val logLevel: String,
    val logMessage: String,
    val executionStep: String?,
    val startedAt: String?,
    val completedAt: String?,
    val taskStatus: String?,
    val databaseConnectionString: String?,
    val sqlQueries: List<String>?,
    val sourceJobId: String?,
    val sourceJobType: String?,
    val processingTimeMs: Long?,
    val exceptionStack: String?,
    val additionalInfo: Map<String, Any>?,
    val createdAt: String
) {
    companion object {
        fun fromExecutionLog(log: ExecutionLog): ExecutionLogResponse {
            return ExecutionLogResponse(
                id = log.id,
                taskId = log.taskId,
                executionId = log.executionId,
                logLevel = log.logLevel.name,
                logMessage = log.logMessage,
                executionStep = log.executionStep,
                startedAt = log.startedAt?.toString(),
                completedAt = log.completedAt?.toString(),
                taskStatus = log.taskStatus?.name,
                databaseConnectionString = log.databaseConnectionString,
                sqlQueries = log.sqlQueries?.let { deserializeFromJson<List<String>>(it) },
                sourceJobId = log.sourceJobId,
                sourceJobType = log.sourceJobType?.name,
                processingTimeMs = log.processingTimeMs,
                exceptionStack = log.exceptionStack,
                additionalInfo = log.additionalInfo?.let { deserializeFromJson<Map<String, Any>>(it) },
                createdAt = log.createdAt.toString()
            )
        }

        private inline fun <reified T> deserializeFromJson(json: String): T? {
            return try {
                com.fasterxml.jackson.module.kotlin.jacksonObjectMapper().readValue(json, T::class.java)
            } catch (e: Exception) {
                null
            }
        }
    }
}

/**
 * 任务最新执行日志响应 (Latest Task Execution Log Response)
 */
data class TaskLatestExecutionLogResponse(
    @JsonProperty("task_id")
    val taskId: Long,
    @JsonProperty("task_name")
    val taskName: String,
    @JsonProperty("task_type")
    val taskType: String,
    @JsonProperty("latest_execution")
    val latestExecution: ExecutionLogResponse?,
    @JsonProperty("execution_count")
    val executionCount: Int,
    @JsonProperty("last_success_execution")
    val lastSuccessExecution: ExecutionLogResponse?,
    @JsonProperty("last_failed_execution")
    val lastFailedExecution: ExecutionLogResponse?
)