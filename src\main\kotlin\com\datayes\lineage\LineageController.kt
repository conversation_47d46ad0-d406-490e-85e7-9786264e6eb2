package com.datayes.lineage

import com.datayes.ApiResponse
import com.datayes.metadata.MetadataDataSourceDto
import com.datayes.metadata.MetadataService
import com.datayes.task.TableLineageDto
import com.datayes.task.TableRelationshipDto
import com.datayes.task.ColumnMappingDto
import com.datayes.script.ScriptRepository
import com.datayes.script.AnalysisStatus
import com.datayes.script.UploadedScript
import com.datayes.task.toTableRelationshipDtos
import com.datayes.util.JdbcUrlParser
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.time.LocalDateTime
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.web.bind.annotation.CrossOrigin
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * 血缘关系控制器 (Lineage Controller)
 *
 * 提供血缘关系的 REST API
 */
@Tag(name = "Lineage", description = "血缘关系查询接口")
@RestController
@RequestMapping("/api/lineage")
@CrossOrigin(origins = ["*"])
class LineageController(
    private val lineageService: LineageService,
    private val metadataService: MetadataService,
    private val lineageTableQueryService: LineageTableQueryService,
    private val lineageTablePropertiesService: LineageTablePropertiesService,
    private val scriptRepository: ScriptRepository,
    private val objectMapper: ObjectMapper,
    private val jdbcTemplate: JdbcTemplate
) {

    private val logger = LoggerFactory.getLogger(LineageController::class.java)

    /**
     * 根据表ID查询上游血缘关系
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级，默认为3
     * @return 表血缘视图列表
     */
    @Operation(summary = "查询表上游血缘", description = "根据表ID查询上游血缘关系")
    @GetMapping("/upstream/table/{tableId}")
    fun getUpstreamLineageByTableId(
        @Parameter(description = "表ID", example = "123")
        @PathVariable tableId: Long,
        @RequestParam(required = false, defaultValue = "3") maxLevels: Int
    ): ResponseEntity<List<TableLineageView>> {
        val result = lineageService.findUpstreamLineageByTableId(tableId, maxLevels)
        return ResponseEntity.ok(result)
    }

    /**
     * 根据表ID查询下游血缘关系
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级，默认为3
     * @return 表血缘视图列表
     */
    @Operation(summary = "查询表下游血缘", description = "根据表ID查询下游血缘关系")
    @GetMapping("/downstream/table/{tableId}")
    fun getDownstreamLineageByTableId(
        @Parameter(description = "表ID", example = "123")
        @PathVariable tableId: Long,
        @RequestParam(required = false, defaultValue = "3") maxLevels: Int
    ): ResponseEntity<List<TableLineageView>> {
        val result = lineageService.findDownstreamLineageByTableId(tableId, maxLevels)
        return ResponseEntity.ok(result)
    }

    /**
     * 根据表ID查询表的上下游血缘关系及列映射 (Get table lineage with column mappings by table ID)
     *
     * 用于详细的血缘分析，包含完整的列映射信息和元数据增强
     * 适用于血缘详情查看、影响分析等场景
     *
     * 响应包含：
     * - 上游和下游血缘关系及列映射
     * - 表的详细信息（名称、类型、描述等）
     * - 表的所有列信息（名称、类型、注释等）
     * - 元数据数据源信息
     * - 系统信息
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级，不传时返回所有层级
     * @return 表血缘DTO，包含上下游关系、列映射、表信息、列信息和元数据
     */
    @Operation(summary = "查询表上下游血缘及列映射", description = "根据表ID查询表的上下游血缘及列映射信息")
    @GetMapping("/table/{tableId}/lineage-with-columns")
    fun getTableLineageWithColumnMappings(
        @Parameter(description = "表ID", example = "123")
        @PathVariable tableId: Long,
        @RequestParam(required = false) maxLevels: Int?
    ): ResponseEntity<ApiResponse<TableLineageDto>> {
        return try {
            val effectiveMaxLevels = maxLevels ?: 999 // 不传maxLevels时返回所有层级
            logger.info("e7a2d8f5 | 接收到表血缘查询请求: tableId=$tableId, maxLevels=$maxLevels, effectiveMaxLevels=$effectiveMaxLevels")

            val tableLineage =
                lineageService.findTableLineageWithColumnMappings(tableId, effectiveMaxLevels, metadataService)

            // 为血缘关系添加元数据信息
            val enhancedTableLineage = enhanceTableLineageWithMetadata(tableLineage)

            // 为血缘关系添加前端展开控制信息
            // 为血缘关系添加前端展开控制信息
            val finalTableLineage = enhanceTableLineageWithExpandControl(enhancedTableLineage)

            logger.info("c4f6b3a1 | 表血缘查询完成: tableId=$tableId, 上游表数=${finalTableLineage.upstream.size}, 下游表数=${finalTableLineage.downstream.size}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = finalTableLineage,
                    message = "查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("b9d1c7e2 | 查询表血缘关系时发生错误: tableId=$tableId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 根据脚本ID查询血缘关系及列映射 (Get lineage with column mappings by script ID)
     *
     * 从脚本分析结果开始，合并脚本临时血缘和相关表的永久血缘关系
     * 响应结构与 getTableLineageWithColumnMappings 相同，但包含脚本分析的临时血缘数据
     *
     * @param scriptId 脚本ID
     * @param maxLevels 最大查询层级，默认3
     * @return 包含脚本临时血缘和永久血缘的合并结果
     */
    @Operation(
        summary = "根据脚本ID查询血缘关系及列映射",
        description = "从脚本分析结果开始，合并临时血缘和永久血缘关系"
    )
    @GetMapping("/script/{scriptId}/lineage-with-columns")
    fun getScriptLineageWithColumnMappings(
        @Parameter(description = "脚本ID", example = "123")
        @PathVariable scriptId: Long,
        @RequestParam(required = false, defaultValue = "3") maxLevels: Int
    ): ResponseEntity<ApiResponse<TableLineageDto>> {
        return try {
            logger.info("a8f3e1d7 | 接收到脚本血缘查询请求: scriptId=$scriptId, maxLevels=$maxLevels")

            // 1. 获取脚本信息
            val script = scriptRepository.findById(scriptId)
            if (script == null) {
                logger.warn("b9e2f4c6 | 脚本不存在: scriptId=$scriptId")
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("脚本不存在 (Script not found)"))
            }

            // 2. 检查分析状态
            if (script.analysisStatus != AnalysisStatus.COMPLETED || script.analysisResult.isNullOrBlank()) {
                logger.warn("c1d5g8h2 | 脚本分析未完成或结果为空: scriptId=$scriptId, status=${script.analysisStatus}")
                val errorMessage = script.analysisResult?.takeIf { it.isNotBlank() } 
                    ?: "脚本分析未完成或结果为空 (Script analysis not completed or result is empty)"
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error(errorMessage))
            }

            // 3. 解析脚本血缘结果
            val dataLineage = try {
                objectMapper.readValue(script.analysisResult, DataLineage::class.java)
            } catch (e: Exception) {
                logger.error("d4f7i9j3 | 解析脚本血缘结果失败: scriptId=$scriptId", e)
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("解析脚本血缘结果失败 (Failed to parse script lineage result)"))
            }

            // 4. 检查是否有目标表（非查询脚本）
            if (dataLineage == null) {
                logger.warn("c5d8e1f4 | 脚本只包含查询语句，无法生成血缘关系: scriptId=$scriptId")
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponse.error("脚本只包含查询语句，无法生成血缘关系 (Script contains only query statements, cannot generate lineage)"))
            }

            // 5. 合并脚本临时血缘和永久血缘
            val mergedLineage = mergeScriptAndPermanentLineage(script, dataLineage, maxLevels)

            // 5.5. 填充表信息和列信息
            val tableInfo = lineageService.findTableInfoWithMetadataById(mergedLineage.tableId, metadataService)
            val columns = lineageService.findTableColumnsById(mergedLineage.tableId)
            val mergedLineageWithTableInfo = mergedLineage.copy(
                tableInfo = tableInfo,
                columns = columns
            )

            // 6. 为血缘关系添加元数据信息
            val enhancedLineage = enhanceTableLineageWithMetadata(mergedLineageWithTableInfo)

            // 7. 为血缘关系添加前端展开控制信息
            val finalLineage = enhanceTableLineageWithExpandControl(enhancedLineage)

            logger.info("e6h8k1l4 | 脚本血缘查询完成: scriptId=$scriptId, 上游表数=${finalLineage.upstream.size}, 下游表数=${finalLineage.downstream.size}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = finalLineage,
                    message = "脚本血缘查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("f7i0j5k8 | 查询脚本血缘关系时发生错误: scriptId=$scriptId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 合并脚本临时血缘和永久血缘 (Merge script temporary lineage with permanent lineage)
     */
    private fun mergeScriptAndPermanentLineage(
        script: UploadedScript,
        dataLineage: DataLineage,
        maxLevels: Int
    ): TableLineageDto {
        logger.info("g9j2k5l8 | 开始合并脚本血缘和永久血缘: scriptId=${script.id}")

        val allUpstream = mutableListOf<TableRelationshipDto>()
        val allDownstream = mutableListOf<TableRelationshipDto>()

        // 1. 确定目标表ID (脚本中应该只有一个目标表)
        val targetTableId = dataLineage.tableLineage.targetTable.id
        if (targetTableId == null) {
            logger.warn("a1b2c3d4 | 目标表ID未在血缘数据中找到: tableName=${dataLineage.tableLineage.targetTable.tableName}")
            throw IllegalStateException("目标表ID未在血缘数据中找到: tableName=${dataLineage.tableLineage.targetTable.tableName}")
        }

        logger.info("e5f6g7h8 | 找到目标表ID: tableId=$targetTableId, tableName=${dataLineage.tableLineage.targetTable.tableName}")

        // 2. 处理上游：脚本源表关系 + 源表的数据库上游血缘
        val sourceTables = dataLineage.tableLineage.sourceTables
        
        if (sourceTables.isEmpty()) {
            // 如果没有源表（如UPDATE语句），直接查询目标表的上游血缘，不创建脚本关系
            logger.info("a1b2c3d4 | 脚本无源表，直接查询目标表上游血缘: ${dataLineage.tableLineage.targetTable.tableName}")
            try {
                val permanentUpstream = lineageService.findUpstreamLineageByTableId(targetTableId, maxLevels)

                // 使用MySQL 5.7兼容的上游列血缘查询
                val permanentUpstreamColumnLineages =
                    lineageService.findUpstreamColumnLineageByTableIdCompatible(targetTableId, maxLevels)

                val upstreamWithColumnMappings = addColumnMappingsToTableRelationships(
                    permanentUpstream.toTableRelationshipDtos(),
                    permanentUpstreamColumnLineages
                )
                allUpstream.addAll(upstreamWithColumnMappings)
                logger.debug("i9j0k1l2 | 为目标表 $targetTableId 添加永久上游血缘: ${permanentUpstream.size}条, 列映射: ${permanentUpstreamColumnLineages.size}条")
            } catch (e: Exception) {
                logger.warn("m3n4o5p6 | 查询目标表永久上游血缘失败: tableId=$targetTableId", e)
            }
        } else {
            // 有源表的正常情况
            for (sourceTable in sourceTables) {
                // 添加脚本源表到目标表的关系，包含列映射
                val scriptRelationship = createScriptRelationshipToTarget(
                    script,
                    sourceTable,
                    targetTableId,
                    dataLineage.tableLineage.targetTable,
                    dataLineage.columnLineages
                )
                allUpstream.add(scriptRelationship)

                // 查找源表的数据库上游血缘
                val sourceTableId = sourceTable.id
                sourceTableId?.let { id ->
                    try {
                        val permanentUpstream = lineageService.findUpstreamLineageByTableId(id, maxLevels)

                        // 使用MySQL 5.7兼容的上游列血缘查询
                        val permanentUpstreamColumnLineages =
                            lineageService.findUpstreamColumnLineageByTableIdCompatible(id, maxLevels)

                        val upstreamWithColumnMappings = addColumnMappingsToTableRelationships(
                            permanentUpstream.toTableRelationshipDtos(),
                            permanentUpstreamColumnLineages
                        )
                        allUpstream.addAll(upstreamWithColumnMappings)
                        logger.debug("i9j0k1l2 | 为源表 $id 添加永久上游血缘: ${permanentUpstream.size}条, 列映射: ${permanentUpstreamColumnLineages.size}条")
                    } catch (e: Exception) {
                        logger.warn("m3n4o5p6 | 查询源表永久上游血缘失败: tableId=$id", e)
                    }
                }
            }
        }

        // 3. 处理下游：目标表的数据库下游血缘
        val permanentDownstream = lineageService.findDownstreamLineageByTableId(targetTableId, maxLevels)

        // 使用MySQL 5.7兼容的下游列血缘查询
        val permanentDownstreamColumnLineages =
            lineageService.findDownstreamColumnLineageByTableIdCompatible(targetTableId, maxLevels)

        val downstreamWithColumnMappings = addColumnMappingsToTableRelationships(
            permanentDownstream.toTableRelationshipDtos(),
            permanentDownstreamColumnLineages
        )
        allDownstream.addAll(downstreamWithColumnMappings)
        logger.info("q7r8s9t0 | 为目标表 $targetTableId 添加永久下游血缘: ${permanentDownstream.size}条, 列映射: ${permanentDownstreamColumnLineages.size}条")

        // 4. 去重并排序
        val uniqueUpstream = allUpstream.distinctBy { "${it.sourceTableId}-${it.targetTableId}" }
        val uniqueDownstream = allDownstream.distinctBy { "${it.sourceTableId}-${it.targetTableId}" }

        logger.info("y5z6a7b8 | 脚本血缘合并完成: scriptId=${script.id}, targetTableId=$targetTableId, 上游=${uniqueUpstream.size}, 下游=${uniqueDownstream.size}")

        return TableLineageDto(
            tableId = targetTableId, // 使用脚本中的目标表ID
            upstream = uniqueUpstream,
            downstream = uniqueDownstream
        )
    }


    /**
     * 为表血缘关系添加元数据信息 (Enhance table lineage with metadata information)
     */
    private fun enhanceTableLineageWithMetadata(tableLineage: TableLineageDto): TableLineageDto {
        logger.info("5d7f9h1j | 开始为表血缘关系添加元数据信息")

        return tableLineage.copy(
            upstream = enhanceRelationshipsWithMetadata(tableLineage.upstream),
            downstream = enhanceRelationshipsWithMetadata(tableLineage.downstream)
        )
    }

    /**
     * 为表血缘关系添加前端展开控制信息 (Enhance table lineage with frontend expand control)
     */
    private fun enhanceTableLineageWithExpandControl(tableLineage: TableLineageDto): TableLineageDto {
        logger.info("k3l6m9n2 | 开始为表血缘关系添加前端展开控制信息")

        return tableLineage.copy(
            upstream = enhanceRelationshipsWithExpandControl(tableLineage.upstream),
            downstream = enhanceRelationshipsWithExpandControl(tableLineage.downstream)
        )
    }

    /**
     * 为表关系列表添加前端展开控制信息 (Enhance relationships with frontend expand control)
     */
    private fun enhanceRelationshipsWithExpandControl(relationships: List<TableRelationshipDto>): List<TableRelationshipDto> {
        return relationships.map { relationship ->
            try {
                // 检查源表是否有进一步的上游关系
                val sourceHasUpstream = if (relationship.sourceTableId > 0) {
                    hasUpstreamRelationships(relationship.sourceTableId)
                } else false

                // 检查目标表是否有进一步的下游关系  
                val targetHasDownstream = if (relationship.targetTableId > 0) {
                    hasDownstreamRelationships(relationship.targetTableId)
                } else false

                relationship.copy(
                    hasUpstream = sourceHasUpstream,
                    hasDownstream = targetHasDownstream
                )

            } catch (e: Exception) {
                logger.warn(
                    "p5q8r1s4 | 检查表展开控制信息失败: sourceTableId=${relationship.sourceTableId}, targetTableId=${relationship.targetTableId}",
                    e
                )
                relationship
            }
        }
    }

    /**
     * 检查表是否有上游关系 (Check if table has upstream relationships)
     */
    private fun hasUpstreamRelationships(tableId: Long): Boolean {
        return try {
            val upstreamCount = lineageService.findUpstreamLineageByTableId(tableId, 1).size
            upstreamCount > 0
        } catch (e: Exception) {
            logger.warn("t7u0v3w6 | 检查上游关系失败: tableId=$tableId", e)
            false
        }
    }

    /**
     * 检查表是否有下游关系 (Check if table has downstream relationships)
     */
    private fun hasDownstreamRelationships(tableId: Long): Boolean {
        return try {
            val downstreamCount = lineageService.findDownstreamLineageByTableId(tableId, 1).size
            downstreamCount > 0
        } catch (e: Exception) {
            logger.warn("x9y2z5a8 | 检查下游关系失败: tableId=$tableId", e)
            false
        }
    }

    /**
     * 为表关系列表添加元数据信息 (Enhance relationships with metadata information)
     */
    private fun enhanceRelationshipsWithMetadata(relationships: List<TableRelationshipDto>): List<TableRelationshipDto> {
        return relationships.map { relationship ->
            try {
                // 查找匹配的源数据库元数据
                val sourceMetadata = findMetadataForDatasource(relationship.sourceDatasource)

                // 查找匹配的目标数据库元数据
                val targetMetadata = findMetadataForDatasource(relationship.targetDatasource)

                // 查找系统信息
                val sourceSystemInfo = findSystemInfoForDatasource(relationship.sourceDatasource)
                val targetSystemInfo = findSystemInfoForDatasource(relationship.targetDatasource)

                relationship.copy(
                    sourceMetadata = sourceMetadata,
                    targetMetadata = targetMetadata,
                    sourceSystemId = sourceSystemInfo?.id,
                    sourceSystemName = sourceSystemInfo?.systemName,
                    sourceSystemAbbreviation = sourceSystemInfo?.systemAbbreviation,
                    targetSystemId = targetSystemInfo?.id,
                    targetSystemName = targetSystemInfo?.systemName,
                    targetSystemAbbreviation = targetSystemInfo?.systemAbbreviation
                )

            } catch (e: Exception) {
                logger.warn(
                    "2k4m6o8q | 获取元数据信息失败: sourceDatasource=${relationship.sourceDatasource}, targetDatasource=${relationship.targetDatasource}",
                    e
                )
                // 如果获取元数据失败，返回原始关系（不影响主要功能）
                relationship
            }
        }
    }

    /**
     * 根据数据源名称查找匹配的元数据信息 (Find metadata for datasource by name)
     */
    private fun findMetadataForDatasource(datasourceName: String): List<MetadataDataSourceDto> {
        return try {
            // 首先根据数据源名称在lineage_datasources表中查找对应的记录
            val lineageDatasourceId = lineageService.findLineageDatasourceIdByName(datasourceName)

            if (lineageDatasourceId != null) {
                // 使用MetadataService查找匹配的元数据
                val matchedResult = metadataService.findMatchedMetadataDataSources(lineageDatasourceId)
                matchedResult.matchedDataSources
            } else {
                logger.debug("8r0t2v4x | 未找到血缘数据源: datasourceName=$datasourceName")
                emptyList()
            }

        } catch (e: Exception) {
            logger.warn("6w8y0a2c | 查找数据源元数据时发生错误: datasourceName=$datasourceName", e)
            emptyList()
        }
    }

    /**
     * 根据数据源名称查找关联的系统信息 (Find system info for datasource by name)
     */
    private fun findSystemInfoForDatasource(datasourceName: String): SimpleSystemInfo? {
        return try {
            // 使用MetadataService查找系统信息
            val metadataSystemInfo = metadataService.findSystemInfoByDatasourceName(datasourceName)

            metadataSystemInfo?.let { systemInfo ->
                SimpleSystemInfo(
                    id = systemInfo.id,
                    systemName = systemInfo.systemName,
                    systemAbbreviation = systemInfo.systemAbbreviation
                )
            }

        } catch (e: Exception) {
            logger.warn("k2l3m4n5 | 查找数据源系统信息时发生错误: datasourceName=$datasourceName", e)
            null
        }
    }

    /**
     * 获取表级血缘图数据 (Get table lineage graph data)
     *
     * UC-09: 渲染基于TableLineageView的血缘图
     *
     * 专为图可视化设计，返回节点-边结构，优化前端图渲染性能
     * 适用于图形化血缘展示、交互式导航等场景
     *
     * 与 lineage-with-columns 的区别：
     * - 返回图结构（nodes + edges）而非层级结构
     * - 支持方向性过滤（只看上游或下游）
     * - 包含图元数据和统计信息
     * - 针对可视化库优化的数据格式
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级，默认3
     * @param includeUpstream 是否包含上游血缘，默认true
     * @param includeDownstream 是否包含下游血缘，默认true
     * @return 表血缘图数据
     */
    @Operation(summary = "获取表级血缘图", description = "获取指定表的表级血缘图数据，用于可视化")
    @GetMapping("/table/{tableId}/graph")
    fun getTableLineageGraph(
        @PathVariable tableId: Long,
        @RequestParam(required = false, defaultValue = "3") maxLevels: Int,
        @RequestParam(required = false, defaultValue = "true") includeUpstream: Boolean,
        @RequestParam(required = false, defaultValue = "true") includeDownstream: Boolean
    ): ResponseEntity<ApiResponse<TableLineageGraphDto>> {
        return try {
            logger.info(
                "3f8e6b92 | 接收到表血缘图查询请求: tableId=$tableId, maxLevels=$maxLevels, " +
                        "includeUpstream=$includeUpstream, includeDownstream=$includeDownstream"
            )

            // 查询上下游血缘关系
            val upstreamLineage = if (includeUpstream) {
                lineageService.findUpstreamLineageByTableId(tableId, maxLevels)
            } else emptyList()

            val downstreamLineage = if (includeDownstream) {
                lineageService.findDownstreamLineageByTableId(tableId, maxLevels)
            } else emptyList()

            // 构建图数据
            val graphData = buildTableLineageGraph(
                tableId = tableId,
                upstreamLineage = upstreamLineage,
                downstreamLineage = downstreamLineage,
                maxLevels = maxLevels
            )

            logger.info("a7c4f3e1 | 表血缘图构建完成: tableId=$tableId, nodes=${graphData.nodes.size}, edges=${graphData.edges.size}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = graphData,
                    message = "血缘图查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("b9d5e8f2 | 获取表血缘图时发生错误: tableId=$tableId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("血缘图查询失败: ${e.message}"))
        }
    }

    /**
     * 获取列级血缘图数据 (Get column lineage graph data)
     *
     * UC-11: 渲染基于ColumnLineageView的列级血缘图
     *
     * 可视化LineageType和DataTransformation信息，支持SCRIPT_ANALYSIS vs MANUAL_INPUT的样式区分
     * 适用于列级血缘图展示、字段级影响分析等场景
     *
     * @param tableId 表ID
     * @param maxLevels 最大查询层级，默认3
     * @param includeUpstream 是否包含上游血缘，默认true
     * @param includeDownstream 是否包含下游血缘，默认true
     * @return 列级血缘图数据
     */
    @Operation(summary = "获取列级血缘图", description = "获取指定表的列级血缘图数据，用于可视化")
    @GetMapping("/column/{tableId}/graph")
    fun getColumnLineageGraph(
        @PathVariable tableId: Long,
        @RequestParam(required = false, defaultValue = "3") maxLevels: Int,
        @RequestParam(required = false, defaultValue = "true") includeUpstream: Boolean,
        @RequestParam(required = false, defaultValue = "true") includeDownstream: Boolean
    ): ResponseEntity<ApiResponse<ColumnLineageGraphDto>> {
        return try {
            logger.info(
                "7f4a9c1b | 接收到列级血缘图查询请求: tableId=${tableId}, maxLevels=${maxLevels}, " +
                        "includeUpstream=${includeUpstream}, includeDownstream=${includeDownstream}"
            )

            // 查询上下游列级血缘关系
            val upstreamColumnLineage = if (includeUpstream) {
                lineageService.findUpstreamColumnLineageByTableId(tableId, maxLevels)
            } else emptyList()

            val downstreamColumnLineage = if (includeDownstream) {
                lineageService.findDownstreamColumnLineageByTableId(tableId, maxLevels)
            } else emptyList()

            // 构建列级血缘图数据
            val columnGraphData = buildColumnLineageGraph(
                tableId = tableId,
                upstreamColumnLineage = upstreamColumnLineage,
                downstreamColumnLineage = downstreamColumnLineage,
                maxLevels = maxLevels
            )

            logger.info("2d8b6f3e | 列级血缘图构建完成: tableId=${tableId}, nodes=${columnGraphData.nodes.size}, edges=${columnGraphData.edges.size}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = columnGraphData,
                    message = "列级血缘图查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("5a7c9d2f | 获取列级血缘图时发生错误: tableId=${tableId}", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("列级血缘图查询失败: ${e.message}"))
        }
    }

    /**
     * 搜索表级血缘图节点 (Search table lineage graph nodes)
     *
     * UC-12: 在表级血缘图中按表名搜索节点
     *
     * @param tableId 根表ID
     * @param query 搜索查询
     * @param searchType 搜索类型
     * @param maxLevels 最大查询层级
     * @param includeUpstream 是否包含上游血缘
     * @param includeDownstream 是否包含下游血缘
     * @return 包含搜索结果的表级血缘图
     */
    @Operation(summary = "搜索表级血缘图节点", description = "在表级血缘图中根据表名搜索节点")
    @GetMapping("/table/{tableId}/graph/search")
    fun searchTableLineageGraph(
        @PathVariable tableId: Long,
        @RequestParam query: String,
        @RequestParam(required = false, defaultValue = "FUZZY_MATCH") searchType: String,
        @RequestParam(required = false, defaultValue = "3") maxLevels: Int,
        @RequestParam(required = false, defaultValue = "true") includeUpstream: Boolean,
        @RequestParam(required = false, defaultValue = "true") includeDownstream: Boolean
    ): ResponseEntity<ApiResponse<TableLineageGraphDto>> {
        return try {
            logger.info("8c4f7e2a | 接收到表级血缘图搜索请求: tableId=${tableId}, query='${query}', searchType=${searchType}")

            val searchRequest = GraphSearchRequestDto(
                query = query,
                searchType = SearchType.valueOf(searchType),
                searchFields = listOf("tableName", "chineseName", "datasource"),
                caseSensitive = false,
                maxResults = 100,
                includeSystemGroups = true,
                highlightMatches = true
            )

            // 获取基础血缘图数据
            val baseGraph = buildTableLineageGraph(
                tableId = tableId,
                upstreamLineage = if (includeUpstream) lineageService.findUpstreamLineageByTableId(
                    tableId,
                    maxLevels
                ) else emptyList(),
                downstreamLineage = if (includeDownstream) lineageService.findDownstreamLineageByTableId(
                    tableId,
                    maxLevels
                ) else emptyList(),
                maxLevels = maxLevels
            )

            // 应用搜索和分组
            val searchResult = applySearchAndGrouping(baseGraph, searchRequest)

            logger.info("9d5b8f3c | 表级血缘图搜索完成: 匹配节点数=${searchResult.searchMetadata?.totalMatches ?: 0}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = searchResult,
                    message = "血缘图搜索成功"
                )
            )

        } catch (e: Exception) {
            logger.error("a1e6c9f4 | 表级血缘图搜索时发生错误: tableId=${tableId}, query='${query}'", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("血缘图搜索失败: ${e.message}"))
        }
    }

    /**
     * 搜索列级血缘图节点 (Search column lineage graph nodes)
     *
     * UC-12: 在列级血缘图中按列名或表名搜索节点
     *
     * @param tableId 根表ID
     * @param query 搜索查询
     * @param searchType 搜索类型
     * @param maxLevels 最大查询层级
     * @param includeUpstream 是否包含上游血缘
     * @param includeDownstream 是否包含下游血缘
     * @return 包含搜索结果的列级血缘图
     */
    @Operation(summary = "搜索列级血缘图节点", description = "在列级血缘图中根据列名或表名搜索节点")
    @GetMapping("/column/{tableId}/graph/search")
    fun searchColumnLineageGraph(
        @Parameter(description = "根表ID", example = "123")
        @PathVariable tableId: Long,
        @RequestParam query: String,
        @RequestParam(required = false, defaultValue = "FUZZY_MATCH") searchType: String,
        @RequestParam(required = false, defaultValue = "3") maxLevels: Int,
        @RequestParam(required = false, defaultValue = "true") includeUpstream: Boolean,
        @RequestParam(required = false, defaultValue = "true") includeDownstream: Boolean
    ): ResponseEntity<ApiResponse<ColumnLineageGraphDto>> {
        return try {
            logger.info("b2f8d5e7 | 接收到列级血缘图搜索请求: tableId=${tableId}, query='${query}', searchType=${searchType}")

            val searchRequest = GraphSearchRequestDto(
                query = query,
                searchType = SearchType.valueOf(searchType),
                searchFields = listOf("columnName", "tableName", "dataType"),
                caseSensitive = false,
                maxResults = 100,
                includeSystemGroups = true,
                highlightMatches = true
            )

            // 获取基础列级血缘图数据
            val baseGraph = buildColumnLineageGraph(
                tableId = tableId,
                upstreamColumnLineage = if (includeUpstream) lineageService.findUpstreamColumnLineageByTableId(
                    tableId,
                    maxLevels
                ) else emptyList(),
                downstreamColumnLineage = if (includeDownstream) lineageService.findDownstreamColumnLineageByTableId(
                    tableId,
                    maxLevels
                ) else emptyList(),
                maxLevels = maxLevels
            )

            // 应用搜索和分组
            val searchResult = applySearchAndGroupingToColumnGraph(baseGraph, searchRequest)

            logger.info("c3g9h6k8 | 列级血缘图搜索完成: 匹配节点数=${searchResult.searchMetadata?.totalMatches ?: 0}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = searchResult,
                    message = "列级血缘图搜索成功"
                )
            )

        } catch (e: Exception) {
            logger.error("d4h0i7l9 | 列级血缘图搜索时发生错误: tableId=${tableId}, query='${query}'", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("列级血缘图搜索失败: ${e.message}"))
        }
    }

    /**
     * 根据数据库信息和表名查询表ID (Query table ID by database info and table name)
     *
     * 支持大小写不敏感匹配，支持 'hive' 与 'hive2' 的映射
     * 查询逻辑：先根据数据库信息找到匹配的数据源，再根据表名和schema查找表
     *
     * 支持两种方式提供数据库信息：
     * 1. 分别提供 host, port, databaseName, dbType 参数
     * 2. 提供 customJdbcUrl 参数，自动解析出 host, port, databaseName
     *
     * @param host 主机地址（当未提供customJdbcUrl时必填）
     * @param port 端口号（当未提供customJdbcUrl时必填）
     * @param databaseName 数据库名称（当未提供customJdbcUrl时必填）
     * @param dbType 数据库类型（必填，支持大小写不敏感，hive/hive2映射）
     * @param customJdbcUrl 自定义JDBC URL（可选，提供时会自动解析host、port、databaseName）
     * @param tableName 表名称
     * @param schema 模式名称，可选
     * @return 表ID，如果未找到则返回null
     */
    @Operation(
        summary = "根据数据库信息查询表ID",
        description = "根据数据库连接信息和表名查询对应的表ID，支持JDBC URL解析"
    )
    @GetMapping("/table-id")
    fun getTableIdByDatabaseInfo(
        @Parameter(description = "主机地址", example = "***********")
        @RequestParam(required = false) host: String?,
        @Parameter(description = "端口号", example = "3306")
        @RequestParam(required = false) port: Int?,
        @Parameter(description = "数据库名称", example = "dgp")
        @RequestParam(required = false) databaseName: String?,
        @Parameter(description = "数据库类型", example = "mysql", required = true)
        @RequestParam dbType: String,
        @Parameter(description = "自定义JDBC URL", example = "*********************************")
        @RequestParam(required = false) customJdbcUrl: String?,
        @Parameter(description = "表名称", example = "lineage_tables", required = true)
        @RequestParam tableName: String,
        @Parameter(description = "模式名称", example = "dwd")
        @RequestParam(required = false) schema: String? = null
    ): ResponseEntity<ApiResponse<Long?>> {
        return try {
            logger.info("7f2e9a5c | 接收到表ID查询请求: host=$host, port=$port, databaseName=$databaseName, dbType=$dbType, customJdbcUrl=$customJdbcUrl, tableName=$tableName, schema=$schema")

            // 解析数据库连接信息
            val (finalHost, finalPort, finalDatabaseName) = if (!customJdbcUrl.isNullOrBlank()) {
                // 使用 JDBC URL 解析
                logger.info("c8d5e2f9 | 使用JDBC URL解析数据库信息: $customJdbcUrl")
                val parseResult = JdbcUrlParser.parseJdbcUrl(customJdbcUrl)

                if (parseResult == null) {
                    logger.warn("a4b7c1d8 | JDBC URL解析失败: $customJdbcUrl")
                    return ResponseEntity.badRequest()
                        .body(ApiResponse.error("JDBC URL格式不正确，无法解析"))
                }

                logger.info("i3j6k9l2 | JDBC URL解析成功: host=${parseResult.host}, port=${parseResult.port}, database=${parseResult.database}")
                val parsedHost = parseResult.host
                val parsedPort = parseResult.port
                val effectiveDatabaseName = databaseName ?: parseResult.database
                if (effectiveDatabaseName.isNullOrBlank() || parsedHost.isNullOrBlank() || parsedPort == null) {
                    return ResponseEntity.badRequest()
                        .body(ApiResponse.error("JDBC URL解析结果不完整，缺少主机、端口或数据库信息"))
                }
                Triple(parsedHost, parsedPort, effectiveDatabaseName)
            } else {
                // 使用独立参数
                if (host.isNullOrBlank() || port == null || databaseName.isNullOrBlank()) {
                    logger.warn("m7n0o3p6 | 缺少必要的数据库连接参数: host=$host, port=$port, databaseName=$databaseName")
                    return ResponseEntity.badRequest()
                        .body(ApiResponse.error("当未提供customJdbcUrl时，host、port、databaseName参数均为必填"))
                }
                Triple(host, port, databaseName)
            }

            val tableId = lineageService.findTableIdByDatabaseInfo(
                host = finalHost,
                port = finalPort,
                databaseName = finalDatabaseName,
                dbType = dbType,
                tableName = tableName,
                schema = schema
            )

            if (tableId != null) {
                logger.info("b4d8f1g3 | 表ID查询成功: tableId=$tableId, finalHost=$finalHost, finalPort=$finalPort, finalDatabaseName=$finalDatabaseName")
                ResponseEntity.ok(
                    ApiResponse.success(
                        data = tableId,
                        message = "表ID查询成功"
                    )
                )
            } else {
                logger.info("9h5k2l6m | 未找到匹配的表: finalHost=$finalHost, finalPort=$finalPort, finalDatabaseName=$finalDatabaseName, dbType=$dbType, tableName=$tableName, schema=$schema")
                ResponseEntity.ok(
                    ApiResponse.error(
                        message = "未找到匹配的表"
                    )
                )
            }

        } catch (e: Exception) {
            logger.error(
                "3p7q8r4s | 查询表ID时发生错误: host=$host, port=$port, databaseName=$databaseName, dbType=$dbType, customJdbcUrl=$customJdbcUrl, tableName=$tableName, schema=$schema",
                e
            )
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("表ID查询失败: ${e.message}"))
        }
    }

    /**
     * 根据元数据系统信息ID查询血缘表 (Query lineage tables by metadata system info ID)
     *
     * 当 metadataSystemId 为 null 时，返回所有血缘表
     * 支持分页查询，页码从1开始
     * 支持模糊匹配过滤条件：host、databaseName、schema、tableName
     *
     * @param metadataSystemId 元数据系统信息ID，可选，为null时查询所有表
     * @param host 主机地址模糊匹配，可选
     * @param databaseName 数据库名称模糊匹配，可选
     * @param schema 模式名称模糊匹配，可选
     * @param tableName 表名称模糊匹配，可选
     * @param page 页码，从1开始，默认为1
     * @param size 每页大小，默认为20
     * @return 分页后的血缘表信息
     */
    @Operation(summary = "查询血缘表", description = "根据元数据系统信息ID查询血缘表，支持分页和模糊匹配过滤")
    @GetMapping("/tables")
    fun queryLineageTablesBySystemId(
        @Parameter(description = "元数据系统信息ID，为null时查询所有表", example = "1")
        @RequestParam(required = false) metadataSystemId: Long?,
        @Parameter(description = "主机地址模糊匹配", example = "10.0.12")
        @RequestParam(required = false) host: String?,
        @Parameter(description = "数据库名称模糊匹配", example = "test")
        @RequestParam(required = false) databaseName: String?,
        @Parameter(description = "模式名称模糊匹配", example = "dwd")
        @RequestParam(required = false) schema: String?,
        @Parameter(description = "表名称模糊匹配", example = "user")
        @RequestParam(required = false) tableName: String?,
        @Parameter(description = "页码，从1开始", example = "1")
        @RequestParam(defaultValue = "1") page: Int,
        @Parameter(description = "每页大小", example = "20")
        @RequestParam(defaultValue = "20") size: Int
    ): ResponseEntity<ApiResponse<PagedLineageTablesDto>> {
        return try {
            logger.info("k3l4m5n6 | 接收到血缘表查询请求: metadataSystemId=$metadataSystemId, host=$host, databaseName=$databaseName, schema=$schema, tableName=$tableName, page=$page, size=$size")

            // 验证分页参数
            val validatedPage = maxOf(1, page)
            val validatedSize = minOf(maxOf(1, size), 100) // 限制最大页面大小为100

            val result = lineageTableQueryService.queryLineageTablesBySystemId(
                metadataSystemId = metadataSystemId,
                host = host,
                databaseName = databaseName,
                schema = schema,
                tableName = tableName,
                page = validatedPage,
                size = validatedSize
            )

            logger.info("o7p8q9r0 | 血缘表查询完成: 返回${result.numberOfElements}条记录，总数${result.totalElements}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = result,
                    message = "血缘表查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error(
                "s1t2u3v4 | 查询血缘表时发生错误: metadataSystemId=$metadataSystemId, host=$host, databaseName=$databaseName, schema=$schema, tableName=$tableName",
                e
            )
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("血缘表查询失败: ${e.message}"))
        }
    }

    /**
     * 根据源系统ID和目标系统ID查询血缘关系 (Query lineage by source and target system IDs)
     *
     * 查询指定源系统和目标系统之间的表级血缘关系
     * 返回从源系统流向目标系统的所有表级血缘数据
     *
     * @param sourceSystemId 源系统ID
     * @param targetSystemId 目标系统ID
     * @return 血缘关系列表
     */
    @Operation(summary = "根据系统ID查询血缘关系", description = "查询指定源系统和目标系统之间的表级血缘关系")
    @GetMapping("/tables/by-systems")
    fun queryLineageBySystemIds(
        @Parameter(description = "源系统ID", example = "1", required = true)
        @RequestParam sourceSystemId: Long,
        @Parameter(description = "目标系统ID", example = "2", required = true)
        @RequestParam targetSystemId: Long
    ): ResponseEntity<ApiResponse<List<TableLineageView>>> {
        return try {
            logger.info("e5f8a2b4 | 接收到系统间血缘查询请求: sourceSystemId=$sourceSystemId, targetSystemId=$targetSystemId")

            val lineageRelations =
                lineageService.findTableLineageBySourceAndTargetSystemId(sourceSystemId, targetSystemId)

            logger.info("c3d7g1h9 | 系统间血缘查询完成: sourceSystemId=$sourceSystemId, targetSystemId=$targetSystemId, 返回${lineageRelations.size}条血缘关系")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = lineageRelations,
                    message = "系统间血缘查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error(
                "f9j2k6l8 | 查询系统间血缘关系时发生错误: sourceSystemId=$sourceSystemId, targetSystemId=$targetSystemId",
                e
            )
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("系统间血缘查询失败: ${e.message}"))
        }
    }

    /**
     * 获取表详情信息 (Get table details)
     *
     * UC-10: 点击表节点时显示详细信息，包括DatabaseInfo和置信度分数
     *
     * @param tableId 表ID
     * @return 表详情信息
     */
    @Operation(summary = "获取表详情", description = "获取指定表ID的详细信息")
    @GetMapping("/table/{tableId}/details")
    fun getTableDetails(
        @Parameter(description = "表ID", example = "123")
        @PathVariable tableId: Long
    ): ResponseEntity<ApiResponse<TableDetailsDto>> {
        return try {
            logger.info("d2a7f4b6 | 接收到表详情查询请求: tableId=$tableId")

            val tableDetails = buildTableDetails(tableId)

            logger.info("e8b3c9f7 | 表详情查询完成: tableId=$tableId, tableName=${tableDetails.tableName}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = tableDetails,
                    message = "表详情查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("f4c8d1a5 | 获取表详情时发生错误: tableId=$tableId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("表详情查询失败: ${e.message}"))
        }
    }

    /**
     * 获取表血缘统计信息 (Get table lineage statistics)
     *
     * 根据表ID查询该表的血缘统计信息，包括上下游表数量、层级等统计数据
     *
     * @param tableId 表ID
     * @return 血缘统计信息
     */
    @Operation(summary = "获取表血缘统计", description = "根据表ID获取血缘统计信息")
    @GetMapping("/table/{tableId}/statistics")
    fun getTableLineageStatistics(
        @Parameter(description = "表ID", example = "123")
        @PathVariable tableId: Long
    ): ResponseEntity<ApiResponse<LineageStatisticsDto>> {
        return try {
            logger.info("c3f8a9d2 | 接收到表血缘统计查询请求: tableId=$tableId")

            val statistics = buildLineageStatistics(tableId)

            logger.info("b7e4f1a6 | 表血缘统计查询完成: tableId=$tableId, 上游表数=${statistics.totalUpstreamTables}, 下游表数=${statistics.totalDownstreamTables}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = statistics,
                    message = "血缘统计查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("a5c7d9f3 | 获取表血缘统计时发生错误: tableId=$tableId", e)
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("血缘统计查询失败: ${e.message}"))
        }
    }

    /**
     * 构建表血缘图 (Build table lineage graph)
     */
    private fun buildTableLineageGraph(
        tableId: Long,
        upstreamLineage: List<TableLineageView>,
        downstreamLineage: List<TableLineageView>,
        maxLevels: Int
    ): TableLineageGraphDto {
        val allLineage = upstreamLineage + downstreamLineage
        val nodes = mutableMapOf<String, TableNodeDto>()
        val edges = mutableListOf<TableEdgeDto>()

        // 添加根节点（查询的起始表）
        val rootTableName = getRootTableName(tableId, allLineage)
        if (rootTableName != null) {
            val rootNode = TableNodeDto(
                id = "table_$tableId",
                tableId = tableId,
                tableName = rootTableName.first,
                schema = rootTableName.second,
                datasource = rootTableName.third,
                system = rootTableName.fourth,
                chineseName = rootTableName.fifth,
                nodeType = TableNodeType.ROOT,
                level = 0,
                confidenceScore = null,
                systemGroupId = generateSystemGroupId(rootTableName.fourth)
            )
            nodes[rootNode.id] = rootNode
        }

        // 处理血缘关系，构建节点和边
        allLineage.forEach { lineage ->
            val (sourceNode, targetNode) = lineage.toTableNodeDto(
                nodeType = if (lineage.sourceTableId == tableId || lineage.targetTableId == tableId)
                    TableNodeType.ROOT else TableNodeType.INTERMEDIATE,
                confidenceScore = lineage.confidenceScore
            )

            // 添加节点（去重）
            nodes.putIfAbsent(sourceNode.id, sourceNode)
            nodes.putIfAbsent(targetNode.id, targetNode)

            // 添加边
            val edge = lineage.toTableEdgeDto(confidence = lineage.confidenceScore)
            edges.add(edge)
        }

        // 收集涉及的系统和数据源
        val systemsInvolved = nodes.values.mapNotNull { it.system }.distinct()
        val datasourcesInvolved = nodes.values.map { it.datasource }.distinct()

        val metadata = LineageGraphMetadataDto(
            totalNodes = nodes.size,
            totalEdges = edges.size,
            maxLevels = maxLevels,
            rootTableId = tableId,
            rootTableName = rootTableName?.first ?: "unknown",
            systemsInvolved = systemsInvolved,
            datasourcesInvolved = datasourcesInvolved,
            lastUpdated = "unknown",
            queryTimestamp = LocalDateTime.now().toString()
        )

        return TableLineageGraphDto(
            nodes = nodes.values.toList(),
            edges = edges,
            metadata = metadata
        )
    }

    /**
     * 构建列级血缘图 (Build column lineage graph)
     */
    private fun buildColumnLineageGraph(
        tableId: Long,
        upstreamColumnLineage: List<ColumnLineageView>,
        downstreamColumnLineage: List<ColumnLineageView>,
        maxLevels: Int
    ): ColumnLineageGraphDto {
        val allColumnLineage = upstreamColumnLineage + downstreamColumnLineage
        val nodes = mutableMapOf<String, ColumnNodeDto>()
        val edges = mutableListOf<ColumnEdgeDto>()

        // 创建表ID映射，用于节点ID生成
        val tableIdMap = mutableMapOf<String, Long?>()
        allColumnLineage.forEach { lineage ->
            val sourceKey = "${lineage.sourceTable}_${lineage.sourceSchema}"
            val targetKey = "${lineage.targetTable}_${lineage.targetSchema}"
            tableIdMap[sourceKey] = lineage.sourceTableId
            tableIdMap[targetKey] = lineage.targetTableId
        }

        // 处理列级血缘关系，构建节点和边
        allColumnLineage.forEach { lineage ->
            // 生成列节点对
            val (sourceNode, targetNode) = lineage.toColumnNodeDto(
                nodeType = if (lineage.sourceTableId == tableId || lineage.targetTableId == tableId)
                    ColumnNodeType.ROOT else ColumnNodeType.INTERMEDIATE,
                tableIds = tableIdMap
            )

            // 添加节点（去重）
            nodes.putIfAbsent(sourceNode.id, sourceNode)
            nodes.putIfAbsent(targetNode.id, targetNode)

            // 生成边
            val edge = lineage.toColumnEdgeDto()
            // 更新边的节点ID以使用正确的表ID
            val updatedEdge = edge.copy(
                sourceNodeId = sourceNode.id,
                targetNodeId = targetNode.id
            )
            edges.add(updatedEdge)
        }

        // 收集涉及的表信息
        val tablesInvolved = mutableMapOf<String, TableSummaryDto>()
        allColumnLineage.forEach { lineage ->
            val sourceTableKey = "${lineage.sourceTable}_${lineage.sourceSchema}"
            val targetTableKey = "${lineage.targetTable}_${lineage.targetSchema}"

            if (!tablesInvolved.containsKey(sourceTableKey)) {
                tablesInvolved[sourceTableKey] = TableSummaryDto(
                    tableId = lineage.sourceTableId,
                    tableName = lineage.sourceTable,
                    schema = lineage.sourceSchema,
                    datasource = null, // 需要额外查询
                    columnCount = 0, // 将在下面计算
                    role = if (lineage.sourceTableId == tableId) "root" else "source"
                )
            }

            if (!tablesInvolved.containsKey(targetTableKey)) {
                tablesInvolved[targetTableKey] = TableSummaryDto(
                    tableId = lineage.targetTableId,
                    tableName = lineage.targetTable,
                    schema = lineage.targetSchema,
                    datasource = null, // 需要额外查询
                    columnCount = 0, // 将在下面计算
                    role = if (lineage.targetTableId == tableId) "root" else "target"
                )
            }
        }

        // 更新表的列数量统计
        nodes.values.groupBy { "${it.tableName}_${it.schema}" }.forEach { (tableKey, tableNodes) ->
            tablesInvolved[tableKey]?.let { summary ->
                tablesInvolved[tableKey] = summary.copy(columnCount = tableNodes.size)
            }
        }

        // 统计转换类型分布
        val transformationTypes = edges.mapNotNull { it.transformation?.type }
            .groupingBy { it }
            .eachCount()

        // 统计来源类型分布
        val sourceTypeDistribution = edges.map { it.sourceType.type }
            .groupingBy { it }
            .eachCount()

        // 计算平均置信度
        val avgConfidenceScore = edges.mapNotNull { it.confidenceScore }
            .takeIf { it.isNotEmpty() }
            ?.let { scores ->
                scores.fold(BigDecimal.ZERO) { acc, score -> acc.add(score) }
                    .divide(BigDecimal(scores.size), 2, RoundingMode.HALF_UP)
            }

        // 构建元数据
        val metadata = ColumnGraphMetadataDto(
            totalNodes = nodes.size,
            totalEdges = edges.size,
            tablesInvolved = tablesInvolved.values.toList(),
            transformationTypes = transformationTypes,
            sourceTypeDistribution = sourceTypeDistribution,
            avgConfidenceScore = avgConfidenceScore,
            queryMetadata = ColumnQueryMetadataDto(
                rootTableId = tableId,
                rootTableName = getRootTableName(tableId, allColumnLineage.map { it.toTableLineageView() })?.first,
                queryType = "by_table",
                maxLevels = maxLevels,
                includeUpstream = upstreamColumnLineage.isNotEmpty(),
                includeDownstream = downstreamColumnLineage.isNotEmpty(),
                queryTimestamp = LocalDateTime.now()
            ),
            visualMetadata = VisualMetadataDto(
                recommendedLayout = "hierarchical",
                nodeSpacing = 150,
                levelSpacing = 300,
                canvasSize = CanvasSizeDto(
                    width = Math.max(1200, nodes.size * 100),
                    height = Math.max(800, maxLevels * 200),
                    minZoom = 0.1,
                    maxZoom = 3.0
                ),
                colorScheme = "default"
            )
        )

        return ColumnLineageGraphDto(
            nodes = nodes.values.toList(),
            edges = edges,
            metadata = metadata
        )
    }

    /**
     * 应用搜索和分组到表级血缘图 (Apply search and grouping to table lineage graph)
     *
     * UC-12 & UC-13: 搜索节点和按系统分组
     */
    private fun applySearchAndGrouping(
        baseGraph: TableLineageGraphDto,
        searchRequest: GraphSearchRequestDto
    ): TableLineageGraphDto {
        val startTime = System.currentTimeMillis()

        // 执行搜索
        val searchResults = searchTableNodes(baseGraph.nodes, searchRequest)
        val matchedNodeIds = searchResults.map { it.id }.toSet()

        // 过滤相关的边
        val relevantEdges = baseGraph.edges.filter { edge ->
            matchedNodeIds.contains(edge.sourceNodeId) || matchedNodeIds.contains(edge.targetNodeId)
        }

        // 生成系统分组
        val systemGroups = if (searchRequest.includeSystemGroups) {
            generateSystemGroups(searchResults)
        } else emptyList()

        // 生成搜索元数据
        val searchMetadata = SearchMetadataDto(
            query = searchRequest.query,
            totalMatches = searchResults.size,
            searchType = searchRequest.searchType,
            searchFields = searchRequest.searchFields,
            suggestions = generateSearchSuggestions(baseGraph.nodes, searchRequest.query),
            searchTime = System.currentTimeMillis() - startTime
        )

        return baseGraph.copy(
            nodes = searchResults,
            edges = relevantEdges,
            systemGroups = systemGroups,
            searchMetadata = searchMetadata
        )
    }

    /**
     * 应用搜索和分组到列级血缘图 (Apply search and grouping to column lineage graph)
     */
    private fun applySearchAndGroupingToColumnGraph(
        baseGraph: ColumnLineageGraphDto,
        searchRequest: GraphSearchRequestDto
    ): ColumnLineageGraphDto {
        val startTime = System.currentTimeMillis()

        // 执行搜索
        val searchResults = searchColumnNodes(baseGraph.nodes, searchRequest)
        val matchedNodeIds = searchResults.map { it.id }.toSet()

        // 过滤相关的边
        val relevantEdges = baseGraph.edges.filter { edge ->
            matchedNodeIds.contains(edge.sourceNodeId) || matchedNodeIds.contains(edge.targetNodeId)
        }

        // 生成系统分组（基于表级信息）
        val systemGroups = if (searchRequest.includeSystemGroups) {
            generateSystemGroupsForColumns(searchResults)
        } else emptyList()

        // 生成搜索元数据
        val searchMetadata = SearchMetadataDto(
            query = searchRequest.query,
            totalMatches = searchResults.size,
            searchType = searchRequest.searchType,
            searchFields = searchRequest.searchFields,
            suggestions = generateColumnSearchSuggestions(baseGraph.nodes, searchRequest.query),
            searchTime = System.currentTimeMillis() - startTime
        )

        return baseGraph.copy(
            nodes = searchResults,
            edges = relevantEdges,
            systemGroups = systemGroups,
            searchMetadata = searchMetadata
        )
    }

    /**
     * 搜索表节点 (Search table nodes)
     */
    private fun searchTableNodes(
        nodes: List<TableNodeDto>,
        searchRequest: GraphSearchRequestDto
    ): List<TableNodeDto> {
        val query = if (searchRequest.caseSensitive) searchRequest.query else searchRequest.query.lowercase()

        return nodes.mapNotNull { node ->
            val searchScore = calculateTableNodeSearchScore(node, query, searchRequest)
            if (searchScore > 0.0) {
                val highlightTerms = if (searchRequest.highlightMatches) {
                    findHighlightTerms(node, query, searchRequest.searchFields)
                } else emptyList()

                node.copy(
                    searchScore = searchScore,
                    highlightTerms = highlightTerms,
                    systemGroupId = generateSystemGroupId(node.system)
                )
            } else null
        }.sortedByDescending { it.searchScore }.take(searchRequest.maxResults)
    }

    /**
     * 搜索列节点 (Search column nodes)
     */
    private fun searchColumnNodes(
        nodes: List<ColumnNodeDto>,
        searchRequest: GraphSearchRequestDto
    ): List<ColumnNodeDto> {
        val query = if (searchRequest.caseSensitive) searchRequest.query else searchRequest.query.lowercase()

        return nodes.mapNotNull { node ->
            val searchScore = calculateColumnNodeSearchScore(node, query, searchRequest)
            if (searchScore > 0.0) {
                val highlightTerms = if (searchRequest.highlightMatches) {
                    findColumnHighlightTerms(node, query, searchRequest.searchFields)
                } else emptyList()

                // 根据表信息推断系统分组
                val systemGroupId = generateSystemGroupIdFromTable(node.tableName, node.datasource)

                node.copy(
                    searchScore = searchScore,
                    highlightTerms = highlightTerms,
                    systemGroupId = systemGroupId
                )
            } else null
        }.sortedByDescending { it.searchScore }.take(searchRequest.maxResults)
    }

    /**
     * 计算表节点搜索分数 (Calculate table node search score)
     */
    private fun calculateTableNodeSearchScore(
        node: TableNodeDto,
        query: String,
        searchRequest: GraphSearchRequestDto
    ): Double {
        var score = 0.0
        val fields = mapOf(
            "tableName" to (if (searchRequest.caseSensitive) node.tableName else node.tableName.lowercase()),
            "chineseName" to (if (searchRequest.caseSensitive) node.chineseName ?: "" else node.chineseName?.lowercase()
                ?: ""),
            "datasource" to (if (searchRequest.caseSensitive) node.datasource else node.datasource.lowercase()),
            "schema" to (if (searchRequest.caseSensitive) node.schema ?: "" else node.schema?.lowercase() ?: "")
        )

        for (fieldName in searchRequest.searchFields) {
            val fieldValue = fields[fieldName] ?: continue
            val fieldScore = when (searchRequest.searchType) {
                SearchType.EXACT_MATCH -> if (fieldValue == query) 1.0 else 0.0
                SearchType.FUZZY_MATCH -> calculateFuzzyScore(fieldValue, query)
                SearchType.WILDCARD_MATCH -> if (fieldValue.contains(query)) 0.8 else 0.0
                SearchType.REGEX_MATCH -> try {
                    if (fieldValue.matches(query.toRegex())) 0.9 else 0.0
                } catch (e: Exception) {
                    0.0
                }
            }
            score = maxOf(score, fieldScore)
        }

        return score
    }

    /**
     * 计算列节点搜索分数 (Calculate column node search score)
     */
    private fun calculateColumnNodeSearchScore(
        node: ColumnNodeDto,
        query: String,
        searchRequest: GraphSearchRequestDto
    ): Double {
        var score = 0.0
        val fields = mapOf(
            "columnName" to (if (searchRequest.caseSensitive) node.columnName else node.columnName.lowercase()),
            "tableName" to (if (searchRequest.caseSensitive) node.tableName else node.tableName.lowercase()),
            "dataType" to (if (searchRequest.caseSensitive) node.dataType else node.dataType.lowercase()),
            "comment" to (if (searchRequest.caseSensitive) node.comment ?: "" else node.comment?.lowercase() ?: "")
        )

        for (fieldName in searchRequest.searchFields) {
            val fieldValue = fields[fieldName] ?: continue
            val fieldScore = when (searchRequest.searchType) {
                SearchType.EXACT_MATCH -> if (fieldValue == query) 1.0 else 0.0
                SearchType.FUZZY_MATCH -> calculateFuzzyScore(fieldValue, query)
                SearchType.WILDCARD_MATCH -> if (fieldValue.contains(query)) 0.8 else 0.0
                SearchType.REGEX_MATCH -> try {
                    if (fieldValue.matches(query.toRegex())) 0.9 else 0.0
                } catch (e: Exception) {
                    0.0
                }
            }
            score = maxOf(score, fieldScore)
        }

        return score
    }

    /**
     * 计算模糊匹配分数 (Calculate fuzzy match score)
     */
    private fun calculateFuzzyScore(text: String, query: String): Double {
        if (text == query) return 1.0
        if (text.contains(query)) return 0.8

        // 简单的编辑距离算法
        val distance = levenshteinDistance(text, query)
        val maxLength = maxOf(text.length, query.length)
        return if (maxLength == 0) 1.0 else 1.0 - (distance.toDouble() / maxLength)
    }

    /**
     * 计算编辑距离 (Calculate Levenshtein distance)
     */
    private fun levenshteinDistance(s1: String, s2: String): Int {
        val dp = Array(s1.length + 1) { IntArray(s2.length + 1) }

        for (i in 0..s1.length) dp[i][0] = i
        for (j in 0..s2.length) dp[0][j] = j

        for (i in 1..s1.length) {
            for (j in 1..s2.length) {
                val cost = if (s1[i - 1] == s2[j - 1]) 0 else 1
                dp[i][j] = minOf(
                    dp[i - 1][j] + 1,      // deletion
                    dp[i][j - 1] + 1,      // insertion
                    dp[i - 1][j - 1] + cost // substitution
                )
            }
        }

        return dp[s1.length][s2.length]
    }

    /**
     * 生成系统分组 (Generate system groups)
     */
    private fun generateSystemGroups(nodes: List<TableNodeDto>): List<SystemGroupDto> {
        return nodes.groupBy { it.system ?: "unknown" }
            .map { (systemName, systemNodes) ->
                val nodeIds = systemNodes.map { it.id }
                val avgConfidence = systemNodes.mapNotNull { it.confidenceScore }
                    .takeIf { it.isNotEmpty() }
                    ?.let { scores ->
                        scores.fold(BigDecimal.ZERO) { acc, score -> acc.add(score) }
                            .divide(BigDecimal(scores.size), 2, RoundingMode.HALF_UP)
                    }

                SystemGroupDto(
                    id = generateSystemGroupId(systemName),
                    systemName = systemName,
                    systemCode = systemName.take(10).uppercase(),
                    displayName = systemName,
                    description = "系统 $systemName 的血缘图节点分组",
                    nodeIds = nodeIds,
                    color = generateSystemColor(systemName),
                    position = null, // 由前端计算
                    metadata = SystemGroupMetadataDto(
                        nodeCount = systemNodes.size,
                        tableCount = systemNodes.size,
                        datasourceCount = systemNodes.map { it.datasource }.distinct().size,
                        avgConfidenceScore = avgConfidence,
                        totalRelationships = 0 // 需要从边中计算
                    )
                )
            }
    }

    /**
     * 生成列级系统分组 (Generate system groups for columns)
     */
    private fun generateSystemGroupsForColumns(nodes: List<ColumnNodeDto>): List<SystemGroupDto> {
        return nodes.groupBy { "${it.tableName}_${it.datasource}" }
            .map { (_, columnNodes) ->
                val nodeIds = columnNodes.map { it.id }
                val avgConfidence = columnNodes.mapNotNull { it.confidenceScore }
                    .takeIf { it.isNotEmpty() }
                    ?.let { scores ->
                        scores.fold(BigDecimal.ZERO) { acc, score -> acc.add(score) }
                            .divide(BigDecimal(scores.size), 2, RoundingMode.HALF_UP)
                    }

                val tableName = columnNodes.first().tableName
                val datasource = columnNodes.first().datasource ?: "unknown"

                SystemGroupDto(
                    id = generateSystemGroupIdFromTable(tableName, datasource),
                    systemName = "$tableName@$datasource",
                    systemCode = tableName.take(10).uppercase(),
                    displayName = tableName,
                    description = "表 $tableName 的列级血缘分组",
                    nodeIds = nodeIds,
                    color = generateSystemColor(tableName),
                    position = null, // 由前端计算
                    metadata = SystemGroupMetadataDto(
                        nodeCount = columnNodes.size,
                        tableCount = 1,
                        datasourceCount = 1,
                        avgConfidenceScore = avgConfidence,
                        totalRelationships = 0 // 需要从边中计算
                    )
                )
            }
    }

    /**
     * 生成系统分组ID (Generate system group ID)
     */
    private fun generateSystemGroupId(systemName: String?): String {
        return "group_${(systemName ?: "unknown").hashCode().toString().replace("-", "")}"
    }

    /**
     * 根据表信息生成系统分组ID (Generate system group ID from table info)
     */
    private fun generateSystemGroupIdFromTable(tableName: String, datasource: String?): String {
        return "group_${(tableName + "_" + (datasource ?: "unknown")).hashCode().toString().replace("-", "")}"
    }

    /**
     * 生成系统颜色 (Generate system color)
     */
    private fun generateSystemColor(systemName: String): String {
        val colors = listOf(
            "#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FECA57",
            "#FF9FF3", "#54A0FF", "#5F27CD", "#00D2D3", "#FF9F43"
        )
        return colors[Math.abs(systemName.hashCode()) % colors.size]
    }

    /**
     * 查找高亮术语 (Find highlight terms)
     */
    private fun findHighlightTerms(
        node: TableNodeDto,
        query: String,
        searchFields: List<String>
    ): List<String> {
        val terms = mutableListOf<String>()
        val fields = mapOf(
            "tableName" to node.tableName,
            "chineseName" to (node.chineseName ?: ""),
            "datasource" to node.datasource,
            "schema" to (node.schema ?: "")
        )

        for (fieldName in searchFields) {
            val fieldValue = fields[fieldName] ?: continue
            if (fieldValue.lowercase().contains(query.lowercase())) {
                terms.add(query)
            }
        }

        return terms.distinct()
    }

    /**
     * 查找列高亮术语 (Find column highlight terms)
     */
    private fun findColumnHighlightTerms(
        node: ColumnNodeDto,
        query: String,
        searchFields: List<String>
    ): List<String> {
        val terms = mutableListOf<String>()
        val fields = mapOf(
            "columnName" to node.columnName,
            "tableName" to node.tableName,
            "dataType" to node.dataType,
            "comment" to (node.comment ?: "")
        )

        for (fieldName in searchFields) {
            val fieldValue = fields[fieldName] ?: continue
            if (fieldValue.lowercase().contains(query.lowercase())) {
                terms.add(query)
            }
        }

        return terms.distinct()
    }

    /**
     * 生成搜索建议 (Generate search suggestions)
     */
    private fun generateSearchSuggestions(nodes: List<TableNodeDto>, query: String): List<String> {
        val suggestions = mutableSetOf<String>()
        val lowerQuery = query.lowercase()

        nodes.forEach { node ->
            if (node.tableName.lowercase().contains(lowerQuery) && node.tableName.lowercase() != lowerQuery) {
                suggestions.add(node.tableName)
            }
            node.chineseName?.let { chineseName ->
                if (chineseName.lowercase().contains(lowerQuery) && chineseName.lowercase() != lowerQuery) {
                    suggestions.add(chineseName)
                }
            }
        }

        return suggestions.take(5).toList()
    }

    /**
     * 生成列搜索建议 (Generate column search suggestions)
     */
    private fun generateColumnSearchSuggestions(nodes: List<ColumnNodeDto>, query: String): List<String> {
        val suggestions = mutableSetOf<String>()
        val lowerQuery = query.lowercase()

        nodes.forEach { node ->
            if (node.columnName.lowercase().contains(lowerQuery) && node.columnName.lowercase() != lowerQuery) {
                suggestions.add(node.columnName)
            }
            if (node.tableName.lowercase().contains(lowerQuery) && node.tableName.lowercase() != lowerQuery) {
                suggestions.add(node.tableName)
            }
        }

        return suggestions.take(5).toList()
    }

    /**
     * 构建表详情 (Build table details)
     */
    private fun buildTableDetails(tableId: Long): TableDetailsDto {
        // 查询基本血缘信息
        val upstreamLineage = lineageService.findUpstreamLineageByTableId(tableId, 1)
        val downstreamLineage = lineageService.findDownstreamLineageByTableId(tableId, 1)
        val allUpstream = lineageService.findUpstreamLineageByTableId(tableId, 10)
        val allDownstream = lineageService.findDownstreamLineageByTableId(tableId, 10)

        // 获取表基本信息
        val tableInfo = getTableBasicInfo(tableId)

        // 获取表属性信息
        val tableProperties = try {
            lineageTablePropertiesService.findTableInfoById(tableId)
        } catch (e: Exception) {
            logger.warn("6h8j2k9m | 获取表属性信息失败: tableId=$tableId", e)
            null
        }

        // 构建统计信息
        val statistics = buildLineageStatistics(tableId)

        // 获取完整数据源信息
        val fullDatasource = getLineageDatasourceByName(tableInfo.datasource) ?: run {
            // 如果找不到完整数据源信息，创建一个默认的对象
            logger.warn("4e8f2b6d | 未找到数据源完整信息，使用默认值: datasourceName=${tableInfo.datasource}")
            LineageDatasourceDto(
                id = -1L,
                datasourceName = tableInfo.datasource,
                dbType = "unknown",
                host = "unknown",
                port = 0,
                databaseName = "unknown",
                status = "UNKNOWN",
                systemId = null,
                connectionString = "unknown"
            )
        }

        // 获取用于提取此表血缘的SQL查询列表
        val sqlQueries = findSqlQueriesForTable(tableId)

        return TableDetailsDto(
            tableId = tableId,
            tableName = tableInfo.tableName,
            schema = tableInfo.schema,
            datasource = fullDatasource,
            description = tableProperties?.description,
            metadata = findMetadataForTable(tableId, tableInfo.datasource),
            upstreamCount = statistics.totalUpstreamTables,
            downstreamCount = statistics.totalDownstreamTables,
            columnCount = 0, // 需要查询表的列数量
            lastLineageUpdate = null,
            sourceType = null,
            syncFrequency = tableProperties?.syncFrequency,
            requirementId = tableProperties?.requirementId,
            dataSyncScope = tableProperties?.dataSyncScope,
            sqlQueries = sqlQueries
        )
    }

    /**
     * 构建血缘统计信息 (Build lineage statistics)
     */
    private fun buildLineageStatistics(tableId: Long): LineageStatisticsDto {
        // 查询基本血缘信息
        val upstreamLineage = lineageService.findUpstreamLineageByTableId(tableId, 1)
        val downstreamLineage = lineageService.findDownstreamLineageByTableId(tableId, 1)
        val allUpstream = lineageService.findUpstreamLineageByTableId(tableId, 10)
        val allDownstream = lineageService.findDownstreamLineageByTableId(tableId, 10)

        return LineageStatisticsDto(
            totalUpstreamTables = allUpstream.map { it.sourceTableId }.distinct().size,
            totalDownstreamTables = allDownstream.map { it.targetTableId }.distinct().size,
            directUpstreamTables = upstreamLineage.size,
            directDownstreamTables = downstreamLineage.size,
            maxUpstreamLevels = allUpstream.maxOfOrNull { it.level } ?: 0,
            maxDownstreamLevels = allDownstream.maxOfOrNull { it.level } ?: 0,
            totalColumnMappings = 0, // 需要单独查询列映射
            systemsInvolved = (allUpstream.mapNotNull { it.sourceSystem } +
                    allDownstream.mapNotNull { it.sourceSystem }).distinct().size
        )
    }

    /**
     * 根据表ID和数据源名称查找关联的元数据信息 (Find metadata for table by ID and datasource)
     */
    private fun findMetadataForTable(
        tableId: Long,
        datasourceName: String
    ): List<MetadataDataSourceDto> {
        return try {
            logger.info("9f7e2b1a | 查找表的元数据信息: tableId=$tableId, datasourceName=$datasourceName")

            // 使用现有的方法查找数据源的元数据
            val metadata = findMetadataForDatasource(datasourceName)

            logger.debug("4c8d6e3f | 表元数据查找完成: tableId=$tableId, 元数据数量=${metadata.size}")

            metadata

        } catch (e: Exception) {
            logger.warn("2a5b9c7d | 获取表元数据信息失败: tableId=$tableId, datasourceName=$datasourceName", e)
            emptyList()
        }
    }

    /**
     * 获取根表名称 (Get root table name)
     */
    private fun getRootTableName(
        tableId: Long,
        allLineage: List<TableLineageView>
    ): Tuple5<String, String?, String, String?, String?>? {
        // 从血缘关系中查找根表信息
        val sourceMatch = allLineage.find { it.sourceTableId == tableId }
        if (sourceMatch != null) {
            return Tuple5(
                sourceMatch.sourceTable, sourceMatch.sourceSchema,
                sourceMatch.sourceDatasource, sourceMatch.sourceSystem, sourceMatch.sourceChineseName
            )
        }

        val targetMatch = allLineage.find { it.targetTableId == tableId }
        if (targetMatch != null) {
            return Tuple5(
                targetMatch.targetTable, targetMatch.targetSchema,
                targetMatch.targetDatasource, null, null
            )
        }

        return null
    }

    /**
     * 计算表的置信度分数 (Calculate table confidence score)
     *
     * 基于相关血缘关系的置信度分数计算平均值
     */
    private fun calculateTableConfidenceScore(
        upstreamLineage: List<TableLineageView>,
        downstreamLineage: List<TableLineageView>
    ): BigDecimal? {
        val allLineage = upstreamLineage + downstreamLineage
        val confidenceScores = allLineage.mapNotNull { it.confidenceScore }

        return if (confidenceScores.isNotEmpty()) {
            // 计算平均置信度分数
            val sum = confidenceScores.fold(BigDecimal.ZERO) { acc, score -> acc.add(score) }
            sum.divide(BigDecimal(confidenceScores.size), 2, RoundingMode.HALF_UP)
        } else {
            null
        }
    }

    /**
     * 根据数据源名称获取完整数据源信息 (Get full datasource info by datasource name)
     */
    private fun getLineageDatasourceByName(datasourceName: String): LineageDatasourceDto? {
        return try {
            logger.debug("2a5b8c4e | 查询数据源信息: datasourceName=$datasourceName")

            lineageService.findLineageDatasourceByName(datasourceName)

        } catch (e: Exception) {
            logger.warn("1d8f4g2h | 查询数据源信息失败: datasourceName=$datasourceName", e)
            null
        }
    }

    /**
     * 获取表基本信息 (Get table basic info)
     */
    private fun getTableBasicInfo(tableId: Long): TableBasicInfo {
        val sql = """
            SELECT 
                t.table_name,
                t.schema_name,
                ds.datasource_name
            FROM lineage_tables t
            JOIN lineage_datasources ds ON t.datasource_id = ds.id
            WHERE t.id = ?
        """.trimIndent()

        return try {
            jdbcTemplate.queryForObject(sql, { rs, _ ->
                TableBasicInfo(
                    tableName = rs.getString("table_name"),
                    schema = rs.getString("schema_name"),
                    datasource = rs.getString("datasource_name")
                )
            }, tableId) ?: throw IllegalStateException("Table not found with id: $tableId")
        } catch (e: Exception) {
            logger.error("7e2f9c4a | Failed to get table basic info for tableId: $tableId", e)
            throw IllegalStateException("Cannot get table basic info for tableId: $tableId", e)
        }
    }

    /**
     * 查找用于提取此表血缘的SQL查询 (Find SQL queries for table lineage extraction)
     *
     * 实现数据流: lineage_tables.id -> lineage_relationships.target_table_id
     *          -> job_key -> lineage_tasks.job_key -> source_content
     */
    private fun findSqlQueriesForTable(tableId: Long): List<String> {
        logger.info("a9f3e7b2 | 查找表血缘SQL查询，tableId: $tableId")

        val sql = """
            SELECT DISTINCT lt.source_content
            FROM lineage_tasks lt
            INNER JOIN lineage_relationships lr ON lt.job_key = lr.job_key
            WHERE lr.target_table_id = ?
              AND lr.relationship_type = 'TABLE_LEVEL'
              AND lr.is_active = true
              AND lt.source_content IS NOT NULL
              AND TRIM(lt.source_content) != ''
            ORDER BY lt.source_content
        """.trimIndent()

        return try {
            val sqlQueries = jdbcTemplate.queryForList(sql, String::class.java, tableId)
                .map { it.trim() }
                .filter { it.isNotBlank() }
                .distinct()

            logger.info("b4d8c6e1 | 找到 ${sqlQueries.size} 个SQL查询，tableId: $tableId")
            sqlQueries

        } catch (e: Exception) {
            logger.error("c7f2a9d5 | 查找表血缘SQL查询失败，tableId: $tableId", e)
            emptyList()
        }
    }

    /**
     * 创建脚本源表到目标表的关系 DTO (Create script relationship from source table to target table)
     */
    private fun createScriptRelationshipToTarget(
        script: UploadedScript,
        sourceTable: TableInfo,
        targetTableId: Long,
        targetTable: TableInfo,
        columnLineages: List<ColumnLineage>
    ): TableRelationshipDto {
        // 查找源表ID
        val sourceTableId = sourceTable.id ?: -1L

        // 从列血缘中提取与当前源表相关的列映射
        val relevantColumnMappings = extractColumnMappingsForTable(sourceTable, columnLineages)

        return TableRelationshipDto(
            id = -1L, // 脚本关系没有数据库ID
            sourceTableId = sourceTableId,
            sourceTable = sourceTable.tableName,
            sourceSchema = sourceTable.schema,
            sourceDatasource = sourceTable.database.originalConnectionString.substringAfter("jdbc:")
                .substringBefore("://") + "-" + sourceTable.database.host,
            sourceDatabaseName = sourceTable.database.databaseName,
            targetTableId = targetTableId,
            targetTable = targetTable.tableName,
            targetSchema = targetTable.schema,
            targetDatasource = targetTable.database.originalConnectionString.substringAfter("jdbc:")
                .substringBefore("://") + "-" + targetTable.database.host,
            targetDatabaseName = targetTable.database.databaseName,
            lineageType = "SCRIPT_TRANSFORMATION",
            level = 0,
            sourceSystem = "SCRIPT_ANALYSIS",
            columnMappings = relevantColumnMappings, // 添加列映射
            isFromScript = true,
            scriptId = script.id,
            scriptName = script.scriptName,
            hasUpstream = sourceTableId > 0, // 如果源表ID存在，可能有上游
            hasDownstream = true // 目标表总是有可能有下游
        )
    }

    /**
     * 从列血缘中提取与指定表相关的列映射 (Extract column mappings for a specific table)
     */
    private fun extractColumnMappingsForTable(
        sourceTable: TableInfo,
        columnLineages: List<ColumnLineage>
    ): List<ColumnMappingDto> {
        return columnLineages.filter { columnLineage ->
            // 检查源列是否属于当前源表
            val sourceTableMatches = columnLineage.sourceColumn.table.tableName == sourceTable.tableName &&
                    columnLineage.sourceColumn.table.schema == sourceTable.schema
            sourceTableMatches
        }.map { columnLineage ->
            ColumnMappingDto(
                sourceColumn = columnLineage.sourceColumn.columnName,
                sourceDataType = columnLineage.sourceColumn.dataType,
                targetColumn = columnLineage.targetColumn.columnName,
                targetDataType = columnLineage.targetColumn.dataType,
                transformationType = columnLineage.transformation?.transformationType?.name,
                transformationDescription = columnLineage.transformation?.description,
                transformationExpression = columnLineage.transformation?.expression,
                confidenceScore = null, // 脚本分析暂不支持置信度
                sourceSystem = "SCRIPT_ANALYSIS"
            )
        }
    }

    /**
     * 为表关系列表添加列映射信息 (Add column mappings to table relationships)
     */
    private fun addColumnMappingsToTableRelationships(
        relationships: List<TableRelationshipDto>,
        columnLineages: List<ColumnLineageView>
    ): List<TableRelationshipDto> {
        return relationships.map { relationship ->
            // 使用表名和模式名进行匹配，而不是关系ID
            val matchingColumnMappings = columnLineages.filter { columnLineage: ColumnLineageView ->
                columnLineage.sourceTable == relationship.sourceTable &&
                columnLineage.sourceSchema == relationship.sourceSchema &&
                columnLineage.targetTable == relationship.targetTable &&
                columnLineage.targetSchema == relationship.targetSchema
            }.map { columnLineage ->
                ColumnMappingDto(
                    sourceColumn = columnLineage.sourceColumn,
                    sourceDataType = columnLineage.sourceDataType,
                    targetColumn = columnLineage.targetColumn,
                    targetDataType = columnLineage.targetDataType,
                    transformationType = columnLineage.transformationType,
                    transformationDescription = columnLineage.transformationDescription,
                    transformationExpression = columnLineage.transformationExpression,
                    confidenceScore = columnLineage.confidenceScore,
                    sourceSystem = columnLineage.lineageSource
                )
            }

            relationship.copy(columnMappings = matchingColumnMappings)
        }
    }



    /**
     * 解析表全名 (Parse table full name)
     */
    private fun parseTableFullName(tableFullName: String): Pair<String?, String> {
        val parts = tableFullName.split(".")
        return when (parts.size) {
            1 -> Pair(null, parts[0])
            2 -> Pair(parts[0], parts[1])
            else -> {
                val tableName = parts.last()
                val schema = parts.dropLast(1).joinToString(".")
                Pair(schema, tableName)
            }
        }
    }
}

/**
 * 辅助数据类 (Helper Data Classes)
 */
data class Tuple5<A, B, C, D, E>(val first: A, val second: B, val third: C, val fourth: D, val fifth: E)

data class TableBasicInfo(
    val tableName: String,
    val schema: String?,
    val datasource: String
)

private data class SimpleSystemInfo(
    val id: Long,
    val systemName: String,
    val systemAbbreviation: String?
)

