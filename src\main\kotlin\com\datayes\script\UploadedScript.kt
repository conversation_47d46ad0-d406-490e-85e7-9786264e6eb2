package com.datayes.script

import java.time.LocalDateTime

/**
 * 上传脚本实体 (Uploaded Script Entity)
 *
 * 对应数据库表 uploaded_scripts，用于存储用户上传的脚本文件信息
 */
data class UploadedScript(
    val id: Long? = null,
    val scriptName: String,
    val scriptType: ScriptType,
    val filePath: String? = null,
    val fileSize: Long? = null,
    val fileHash: String? = null,
    val scriptContent: String? = null,
    val uploadUser: String,
    val analysisStatus: AnalysisStatus = AnalysisStatus.PENDING,
    val analysisResult: String? = null,
    val temporaryLineageId: String? = null,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 脚本类型枚举 (Script Type Enum)
 */
enum class ScriptType {
    SQL, SHELL
}

/**
 * 分析状态枚举 (Analysis Status Enum)
 */
enum class AnalysisStatus {
    PENDING,      // 待分析
    ANALYZING,    // 分析中
    COMPLETED,    // 分析完成
    FAILED        // 分析失败
} 