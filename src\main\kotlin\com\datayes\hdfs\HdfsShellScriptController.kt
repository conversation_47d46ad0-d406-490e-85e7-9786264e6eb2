package com.datayes.hdfs

import com.datayes.task.LineageTask
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import io.swagger.v3.oas.annotations.tags.Tag
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter

/**
 * HDFS Shell Script REST API 控制器
 *
 * 提供HDFS shell脚本处理的REST API接口
 * 类似于LineageController，专门处理HDFS相关的血缘分析请求
 */
@Tag(name = "Hdfs", description = "HDFS Shell 脚本血缘分析接口")
@RestController
@CrossOrigin(origins = ["*"])
@RequestMapping("/api/hdfs")
class HdfsShellScriptController(
    private val hdfsShellScriptService: HdfsShellScriptService
) {

    private val logger = LoggerFactory.getLogger(HdfsShellScriptController::class.java)

    /**
     * 查询指定HDFS目录下的所有ZIP文件
     *
     * @param hdfsPath HDFS目录路径
     * @param includeBackups 是否包含备份文件（带时间戳的文件），默认false
     * @return ZIP文件查询结果
     */
    @Operation(summary = "查询HDFS ZIP文件", description = "查询指定HDFS目录下的ZIP文件")
    @GetMapping("/zip-files")
    fun queryZipFiles(
        @Parameter(description = "HDFS目录路径", example = "/data/warehouse")
        @RequestParam hdfsPath: String,
        @RequestParam(defaultValue = "false") @Parameter(description = "是否包含备份ZIP", example = "false")
        includeBackups: Boolean
    ): ResponseEntity<HdfsZipFileQueryResult> {
        logger.info("8f2e4a7c | 接收到ZIP文件查询请求: hdfsPath={}, includeBackups={}", hdfsPath, includeBackups)

        return try {
            val result = hdfsShellScriptService.queryZipFiles(hdfsPath, includeBackups)

            if (result.success) {
                logger.info(
                    "d5c9e3a8 | ZIP文件查询成功: 找到{}个文件，耗时{}ms",
                    result.totalAfterFilter, result.queryTimeMs
                )
                ResponseEntity.ok(result)
            } else {
                logger.warn("b7f4e2a6 | ZIP文件查询失败: {}", result.message)
                ResponseEntity.status(400).body(result)
            }

        } catch (e: Exception) {
            logger.error("a3e8f5c9 | ZIP文件查询发生异常", e)

            val errorResult = HdfsZipFileQueryResult(
                success = false,
                message = "查询失败: ${e.message}",
                hdfsPath = hdfsPath,
                zipFiles = emptyList()
            )

            ResponseEntity.status(500).body(errorResult)
        }
    }
}

/**
 * HDFS完整处理响应（包含LineageTask保存结果）
 */
data class HdfsFullProcessingResponse(
    val success: Boolean,
    val message: String,
    val hdfsPath: String,
    val taskCreationResults: List<HdfsLineageTaskResult>,
    val lineageProcessingResults: List<HdfsLineageProcessResult>,
    val summary: HdfsFullProcessingSummary
)

/**
 * HDFS完整处理摘要
 */
data class HdfsFullProcessingSummary(
    val totalScriptsProcessed: Int = 0,
    val lineageTasksCreated: Int = 0,
    val lineageTasksUpdated: Int = 0,
    val lineageDataUpdated: Int = 0,
    val errorCount: Int = 0,
    val processingTimeMs: Long = 0
)

/**
 * HDFS脚本到LineageTask的转换结果
 */
data class HdfsLineageTaskResult(
    val scriptJob: HdfsShellScriptJob,
    val lineageTask: LineageTask?,
    val isNewTask: Boolean,
    val success: Boolean,
    val errorMessage: String? = null
)