package com.datayes.task

import com.datayes.lineage.ColumnLineageView
import com.datayes.lineage.TableLineageView
import com.datayes.metadata.MetadataDataSourceDto
import java.math.BigDecimal

/**
 * 表血缘关系 DTO (Table Lineage DTO)
 * 
 * 包含表的上游和下游血缘关系信息，以及表本身的详细信息
 */
data class TableLineageDto(
    val tableId: Long,
    val upstream: List<TableRelationshipDto> = emptyList(),
    val downstream: List<TableRelationshipDto> = emptyList(),
    val tableInfo: TableInfoDto? = null,
    val columns: List<TableColumnDto> = emptyList()
)

/**
 * 表关系 DTO (Table Relationship DTO)
 * 
 * 表示两个表之间的血缘关系
 */
data class TableRelationshipDto(
    val id: Long,
    val sourceTableId: Long,
    val sourceTable: String,
    val sourceSchema: String?,
    val sourceDatasource: String,
    val sourceDatabaseName: String? = null,
    val targetTableId: Long,
    val targetTable: String,
    val targetSchema: String?,
    val targetDatasource: String,
    val targetDatabaseName: String? = null,
    val lineageType: String?,
    val level: Int,
    val sourceSystem: String? = null,
    val columnMappings: List<ColumnMappingDto> = emptyList(),
    val sourceMetadata: List<MetadataDataSourceDto> = emptyList(),
    val targetMetadata: List<MetadataDataSourceDto> = emptyList(),
    val sourceSystemId: Long? = null,
    val sourceSystemName: String? = null,
    val sourceSystemAbbreviation: String? = null,
    val targetSystemId: Long? = null,
    val targetSystemName: String? = null,
    val targetSystemAbbreviation: String? = null,
    // 脚本血缘相关字段 (Script lineage related fields)
    val isFromScript: Boolean = false,
    val scriptId: Long? = null,
    val scriptName: String? = null,
    val temporaryLineageId: String? = null,
    // 前端展开按钮控制字段 (Frontend expand button control fields)
    val hasUpstream: Boolean = false,
    val hasDownstream: Boolean = false
)

/**
 * 列映射 DTO (Column Mapping DTO)
 * 
 * 表示源表列和目标表列之间的映射关系
 */
data class ColumnMappingDto(
    val sourceColumn: String,
    val sourceDataType: String,
    val targetColumn: String,
    val targetDataType: String,
    val transformationType: String?,
    val transformationDescription: String?,
    val transformationExpression: String?,
    val confidenceScore: BigDecimal?,
    val sourceSystem: String? = null
)

/**
 * 将 TableLineageView 列表转换为 TableRelationshipDto 列表
 */
fun List<TableLineageView>.toTableRelationshipDtos(): List<TableRelationshipDto> {
    return this.map { view ->
        TableRelationshipDto(
            id = view.relationshipId,
            sourceTableId = view.sourceTableId,
            sourceTable = view.sourceTable,
            sourceSchema = view.sourceSchema,
            sourceDatasource = view.sourceDatasource,
            sourceDatabaseName = view.sourceDatabaseName,
            targetTableId = view.targetTableId,
            targetTable = view.targetTable,
            targetSchema = view.targetSchema,
            targetDatasource = view.targetDatasource,
            targetDatabaseName = view.targetDatabaseName,
            lineageType = view.lineageType,
            level = view.level,
            sourceSystem = view.lineageSource,
            columnMappings = emptyList(), // 初始为空，后续填充
            sourceMetadata = emptyList(), // 初始为空，后续填充
            targetMetadata = emptyList(), // 初始为空，后续填充
            // 新增字段使用默认值，在后续处理中设置
            hasUpstream = false,
            hasDownstream = false
        )
    }
}

/**
 * 将 ColumnLineageView 转换为 ColumnMappingDto
 */
fun ColumnLineageView.toColumnMappingDto(): ColumnMappingDto {
    return ColumnMappingDto(
        sourceColumn = this.sourceColumn,
        sourceDataType = this.sourceDataType,
        targetColumn = this.targetColumn,
        targetDataType = this.targetDataType,
        transformationType = this.transformationType,
        transformationDescription = this.transformationDescription,
        transformationExpression = this.transformationExpression,
        confidenceScore = this.confidenceScore,
        sourceSystem = this.lineageSource
    )
}

/**
 * 表信息 DTO (Table Info DTO)
 * 
 * 包含表的基本信息、属性和元数据信息
 */
data class TableInfoDto(
    val tableId: Long,
    val tableName: String,
    val schema: String?,
    val datasource: String,
    val databaseName: String? = null,
    val tableType: String?,
    val chineseName: String?,
    val description: String?,
    val syncFrequency: String?,
    val requirementId: String?,
    val dataSyncScope: String?,
    val status: String,
    val createdAt: String?,
    val updatedAt: String?,
    val referenceCount: Int = 0,
    val lastReferencedAt: String?,
    val metadata: List<MetadataDataSourceDto> = emptyList(),
    val systemInfo: SimpleSystemInfoDto? = null
)

/**
 * 表列 DTO (Table Column DTO)
 * 
 * 包含表的列信息
 */
data class TableColumnDto(
    val columnId: Long,
    val columnName: String,
    val dataType: String,
    val columnComment: String?,
    val isPrimaryKey: Boolean = false,
    val isNullable: Boolean = true,
    val defaultValue: String?,
    val columnOrder: Int?,
    val status: String,
    val createdAt: String?,
    val updatedAt: String?,
    val referenceCount: Int = 0,
    val lastReferencedAt: String?
)

/**
 * 简化系统信息 DTO (Simple System Info DTO)
 * 
 * 包含系统的基本信息
 */
data class SimpleSystemInfoDto(
    val id: Long,
    val systemName: String,
    val systemAbbreviation: String?
)

