package com.datayes.sql

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

class SqlParserTruncateTest {

    @Test
    @DisplayName("Should parse simple TRUNCATE TABLE statement")
    fun testParseSimpleTruncateStatement() {
        // Given
        val truncateQuery = """
            TRUNCATE TABLE users
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(truncateQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("users")
        assertThat(result.targetTable.schemaOrDatabase).isNull()
        assertThat(result.targetTable.alias).isNull()

        // Verify no target columns for TRUNCATE
        assertThat(result.targetColumns).isNull()

        // Verify no source tables for TRUNCATE (it's a standalone operation)
        assertThat(result.sourceTables).isEmpty()

        // Verify no source columns for TRUNCATE (no WHERE clause or references)
        assertThat(result.sourceColumns).isEmpty()

        // Verify no column mappings for TRUNCATE
        assertThat(result.columnMappings).isEmpty()
    }

    @Test
    @DisplayName("Should parse TRUNCATE TABLE statement with schema-qualified table")
    fun testParseTruncateStatementWithSchema() {
        // Given
        val truncateQuery = """
            TRUNCATE TABLE myschema.users
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(truncateQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table with schema
        assertThat(result.targetTable.name).isEqualTo("users")
        assertThat(result.targetTable.schemaOrDatabase).isEqualTo("myschema")
        assertThat(result.targetTable.alias).isNull()

        // Verify no target columns for TRUNCATE
        assertThat(result.targetColumns).isNull()

        // Verify no source tables for TRUNCATE
        assertThat(result.sourceTables).isEmpty()

        // Verify no source columns for TRUNCATE
        assertThat(result.sourceColumns).isEmpty()

        // Verify no column mappings for TRUNCATE
        assertThat(result.columnMappings).isEmpty()
    }

    @Test
    @DisplayName("Should parse TRUNCATE statement without TABLE keyword")
    fun testParseTruncateStatementWithoutTableKeyword() {
        // Given
        val truncateQuery = """
            TRUNCATE users
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(truncateQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("users")
        assertThat(result.targetTable.schemaOrDatabase).isNull()
        assertThat(result.targetTable.alias).isNull()

        // Verify no target columns for TRUNCATE
        assertThat(result.targetColumns).isNull()

        // Verify no source tables for TRUNCATE
        assertThat(result.sourceTables).isEmpty()

        // Verify no source columns for TRUNCATE
        assertThat(result.sourceColumns).isEmpty()

        // Verify no column mappings for TRUNCATE
        assertThat(result.columnMappings).isEmpty()
    }

    @Test
    @DisplayName("Should parse TRUNCATE TABLE statement with database.schema.table format")
    fun testParseTruncateStatementWithFullyQualifiedTable() {
        // Given
        val truncateQuery = """
            TRUNCATE TABLE database1.schema1.users
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(truncateQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table with full qualification
        assertThat(result.targetTable.name).isEqualTo("users")
        // Note: JSQLParser may handle database.schema.table differently
        // The schema might be "database1.schema1" or just "schema1" depending on implementation
        assertThat(result.targetTable.schemaOrDatabase).isNotNull()
        assertThat(result.targetTable.alias).isNull()

        // Verify no target columns for TRUNCATE
        assertThat(result.targetColumns).isNull()

        // Verify no source tables for TRUNCATE
        assertThat(result.sourceTables).isEmpty()

        // Verify no source columns for TRUNCATE
        assertThat(result.sourceColumns).isEmpty()

        // Verify no column mappings for TRUNCATE
        assertThat(result.columnMappings).isEmpty()
    }

    @Test
    @DisplayName("Should handle case insensitive TRUNCATE statement")
    fun testParseCaseInsensitiveTruncateStatement() {
        // Given
        val truncateQuery = """
            truncate table Users
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(truncateQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table (case preserved)
        assertThat(result.targetTable.name).isEqualTo("Users")
        assertThat(result.targetTable.schemaOrDatabase).isNull()
        assertThat(result.targetTable.alias).isNull()

        // Verify no target columns for TRUNCATE
        assertThat(result.targetColumns).isNull()

        // Verify no source tables for TRUNCATE
        assertThat(result.sourceTables).isEmpty()

        // Verify no source columns for TRUNCATE
        assertThat(result.sourceColumns).isEmpty()

        // Verify no column mappings for TRUNCATE
        assertThat(result.columnMappings).isEmpty()
    }
}