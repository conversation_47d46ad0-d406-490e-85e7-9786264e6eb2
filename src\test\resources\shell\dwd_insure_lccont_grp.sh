#!/bin/bash
hdfs dfs -cat /project/dwd/tbds_config.properties > tbds_config.properties
source tbds_config.properties
hdfs dfs -cat /project/dwd/tbds_config.sh > tbds_config.sh
source tbds_config.sh


#所属主题：保险业务-承保信息
#功能描述；团体分单表
#创建者；高福杰
#创建日期；2025-01-13
#修改日志
#修改日期            		修改人            		修改内容
#--团险为空字段：callback_date-回访成功日期,self_flag-自保互保标记,cooling_off_days-犹豫期,policy_service_status-保单服务状态
#--policy_source-保单来源,policy_source_subtype-保单来源详细分类,annuity_flag-年金标志,
#--prem_agent-收费员代码,prem_name-收费员名称,direct_agent-督导员代码,direct_name-督导员名称

echo "$(date +'%Y-%m-%d %T')：开始创建dwd.dwd_insure_lccont_grp 表"
${hive} -e "
set hive.optimize.skewjoin=true;
set hive.groupby.skewindata = true;
set hive.cbo.enable=true;
set hive.compute.query.using.stats=true;
set hive.stats.fetch.column.stats=true;
set hive.stats.fetch.partition.stats=true;
set hive.exec.reducers.max = 355;
set hive.exec.mode.local.auto = true;
set hive.exec.mode.local.auto.inputbytes.max=50000000;
set hive.exec.mode.local.auto.input.files.max=10;
set hive.tez.auto.reducer.parallelism = true;
set hive.tez.input.format=org.apache.hadoop.hive.ql.io.CombineHiveInputFormat;
set mapreduce.map.cpu.vcores=2;
set mapreduce.reduce.cpu.vcores=2;
set mapreduce.map.memory.mb=6144;
set mapreduce.reduce.memory.mb=6144;
insert overwrite table dwd.dwd_insure_lccont_grp
select  a.prtno                                                             as proposal_no								
       ,a.contno                                                           as contract_no
       ,a.grpcontno                                                        as group_contract_no
       ,a.proposalcontno                                                   as group_proposal_no
       ,a.prtno                                                            as proposal_print_number
       ,if(count(1) over(partition by a.prtno) > 1,'1','0')                as combined_policy_flag
	  ,a.cardflag										as card_flag
       ,a.appntno                                                          as policy_holder_no
       ,a.insuredno                                                        as policy_insured_no
       ,lcgct.grpnature                                                    as application_category
       ,a.conttype                                                         as policy_flag
       ,a.familyid                                                         as family_number
       ,a.poltype                                                          as policy_type
       ,a.managecom                                                        as managecom_code
       ,a.agentcom                                                         as agency_com
       ,a.agentcode                                                        as agent_code
       ,a.agentgroup                                                       as agent_group
       ,a.agentcode1                                                       as unite_agent_code
       ,a.signcom                                                          as application_province
       ,a.salechnl                                                         as sales_channel_code
       ,a.selltype                                                         as sale_type
       ,d.agencycode                                                       as agency_code
	   ,d.name                                                             as agency_name
       ,a.firstpaydate                                                     as pay_begin_date
       ,g.finalpaydate                                                     as pay_end_date
	  ,a.paytodate                                                        as pay_to_date
       ,a.cvalidate                                                        as effective_date
       ,case when g.status='04' then  null   else g.policyenddate end      as expire_date
	   ,null                                                               as callback_date
       ,a.uwdate                                                           as uw_finish_date
       ,a.uwflag                                                           as uw_conclusion
       ,a.signdate                                                         as sign_date
	   ,a.signtime                                                         as sign_time
	   ,a.getpoldate                                                       as get_pol_date
       ,a.getpoltime                                                       as get_pol_time
       ,a.payintv                                                          as pay_mode
       ,a.polapplydate                                                     as application_date
       ,a.inputdate                                                        as input_date
       ,if(a.customgetpoldate is null,'0','1')                             as receipt_flag
       ,a.customgetpoldate                                              	as receipt_date
       ,a.newbankcode                                                      as first_bank_code
       ,c1.name                                                            as first_bank_name
       ,a.newbankaccno                                                    as first_bank_account
       ,a.newaccname                                                        as first_bank_account_name
       ,a.bankcode                                                          as subsequent_bank_code
	   ,c.name                                                            as subsequent_bank_name
       ,a.bankaccno                                                         as subsequent_bank_account
       ,a.accname                                                           as subsequent_bank_account_name
       ,if(a.customgetpoldate is null,'0','1')              			as policy_send_flag
       ,a.mult                                                              as Copies
	   ,a.prem                                                            as premium
       ,a.sumprem                                                           as sum_premium
       ,a.amnt                                                              as amount
      ,nvl(n.amnt,a.amnt)                                                 	as first_period_amount
	   ,a.elecsignnameflag                                                as elecsignname_flag
       ,null                                                                as self_flag
       ,null                                                                as cooling_off_days
       ,a.appflag                                                           as app_flag
	   ,case when a.appflag = '4' then cx.startdate  end      as hesitate_cancel_date
       ,agt.agentcode                                                       as first_agentcode
       ,null                                                                 as policy_service_status
	   ,null                                                               as policy_source
	   ,null                                                               as policy_source_subtype
	   ,null                                                               as annuity_flag
       ,null                                                               as prem_agent
       ,null                                                               as prem_name
       ,null                                                               as direct_agent
       ,null                                                               as direct_name
       ,a.outpayflag                                                       as overpay_flag
       ,a.dif                                                              as overpay_amount
       ,if(g.renewcount>'0',a.contno,null)                                 as pre_cont_no
       ,if(a.salechnl in ('03','04','05','06'
	                     ,'07','08','301','302'
						 ,'401','402','404','405'
						 ,'411','412','413','6231'
						 ,'6232','6242','6244','6245'
						 ,'6241','6243'),a.agentcom,null)                  as im_agent_com
       ,case when e.electronicpolflag = 'Y' then '3'
             when e.electronicpolflag = 'Y' then '2'
             else '1'
        end                                                                as policy_type_code
       ,a.paymode                                                          as payway
       ,nvl(g.status,'04')                                                 as policy_status_code
       ,case when g.status = '03' and g.terminationreason <> '01' then g.terminationdate
             when g.status = '03' and g.terminationreason = '01' then g.policyenddate
             else null   end                                                                as terminated_date
       ,if(g.status = '03',g.terminationreason,null)                       as terminated_reason_code
       ,if(g.status = '02',g.suspenddate,g.last_a0a1_date)                 as suspension_date
       ,if(g.status = '02',g.suspendreason,null)                 		as suspension_reason_code
       ,if(g.status = '02',null,g.recoverdate)                             as reinstate_date
       ,g.renewcount                                                       as renew_count
       ,add3.detailed_address                                              as register_address 
      ,case when a.uwoperator is null then '1' else '2' end               as uw_type
      ,case when f.tempfeetype <> '1' and g.riskperiod = '04' then '04'
             when f.tempfeetype <> '1' and g.riskperiod = '02' then '02'
             else '01'  end                                                                as policy_source_category
	   ,'0'                                                                as new_resident_flag
       ,concat_ws(' ',a.makedate,a.maketime)                               as create_time 
       ,concat_ws(' ',a.modifydate,a.modifytime)                           as modify_time 
       ,from_unixtime(unix_timestamp(),'yyyy-MM-dd HH:mm:ss')              as first_hadoop_opt_time
       ,from_unixtime(unix_timestamp(),'yyyy-MM-dd HH:mm:ss')              as last_hadoop_opt_time
from ms_ods_lis.full_lccont a
  left join dorado_ins.full_trade_management_agency b
    on a.managecom = b.agency_code
  left join dwd.edw_bankname c
    on a.bankcode = c.code
  left join dwd.edw_bankname c1
    on a.newbankcode = c1.code
  left join ms_ods_cms.lacom d
    on a.agentcom = d.agentcom
  left join ms_ods_lis.full_lcelectronicsignresult e
    on a.prtno = e.prtno
  left join (select customer_number,detailed_address from  dwd.edw_address where address_type_code = '5' and rn=1 ) add3
    on a.insuredno = add3.customer_number
  left join (select t.contno, max(t.startdate) startdate
               from ms_ods_lis.full_lccontstate t
              where upper(t.statetype ) = 'TERMINATE'
                and t.statereason = '06'
                and t.enddate is null
              group by t.contno) cx
    on a.contno = cx.contno
  left join (select t.agentcode, t.contno
               from (select lc.contno,
                            lja.agentcode,
                            row_number() over(partition by lc.contno order by confdate) xh
                       from ms_ods_lis.full_lcpol lc
                      inner join ms_ods_lis.full_ljapayperson lja
                         on lc.mainpolno = lja.polno
                        and lja.paycount = 1) t
              where t.xh = 1) agt
    on a.contno = agt.contno 
  left join  (select contno,amnt,row_number() over(partition by contno order by modifydate) rn from ms_ods_lis.full_lpcont ) n
	 on a.contno = n.contno
	 and n.rn = 1
   left join dwd.step01_insure_lccont_grp f 
     on a.contno = f.otherno
     and f.rn = 1
   left join dwd.step02_insure_lccont_grp  g 
     on a.contno = g.contno
     and g.cc_rn = 1
   left join  ms_ods_lis.full_lcgrpcont lcgct
      on  a.grpcontno = lcgct.grpcontno
	 ;"
  exitCodeCheck $? "创建dwd.dwd_insure_lccont_grp 表失败！" "创建dwd.dwd_insure_lccont_grp 表成功！"