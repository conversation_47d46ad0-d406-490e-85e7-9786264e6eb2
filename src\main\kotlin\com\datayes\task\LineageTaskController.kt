package com.datayes.task

import com.datayes.ApiResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.format.annotation.DateTimeFormat
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.math.BigDecimal
import java.time.LocalDateTime

/**
 * 血缘任务管理控制器 (Lineage Task Management Controller)
 *
 * 提供血缘任务的 REST API 接口，包括批量处理、查询和重跑功能
 */
@RestController
@RequestMapping("/api/v1/lineage/tasks")
@CrossOrigin(origins = ["*"])
class LineageTaskController(
    private val lineageTaskService: LineageTaskService,
    private val executionLogService: ExecutionLogService
) {

    private val logger = LoggerFactory.getLogger(LineageTaskController::class.java)

    /**
     * 分页查询血缘任务 (Get lineage tasks with pagination and filtering)
     *
     * 支持按任务状态、类型、创建人、时间范围等多条件过滤，并可按指定字段排序 (multi-criteria filtering & sorting)。
     *
     * @param page 页码 (page number)，从1开始
     * @param size 每页大小 (page size)，默认20，最大100
     * @param status 任务状态 (task status) 过滤
     * @param taskType 任务类型 (task type) 过滤
     * @param taskName 任务名称 (task name) 模糊搜索
     * @param createdBy 创建人 (created by) 过滤
     * @param dateFrom 创建时间起始 (date from)，格式 yyyy-MM-dd
     * @param dateTo 创建时间结束 (date to)，格式 yyyy-MM-dd
     * @param isEnabled 是否启用 (is enabled) 过滤
     * @param batchId 批处理ID (batch id) 过滤
     * @param sortField 排序字段 (sort field)，默认为 executed_at
     * @param sortOrder 排序方向 (sort order)，默认为 desc
     * @return 分页查询结果，由框架自动推断返回类型
     */
    @GetMapping
    @Operation(
        summary = "分页查询血缘任务",
        description = "按状态、类型、创建人、日期范围等过滤条件分页返回血缘任务列表")
    fun getTasks(
        @Parameter(description = "页码 (page number)，从1开始", example = "1")
        @RequestParam(defaultValue = "1") page: Int,

        @Parameter(description = "每页大小 (page size)，最大100", example = "20")
        @RequestParam(defaultValue = "20") size: Int,

        @Parameter(description = "任务状态列表 (task status list) 过滤，支持多个状态用逗号分隔", required = false)
        @RequestParam(required = false) status: List<TaskStatus>?,

        @Parameter(description = "任务类型 (task type) 过滤", required = false)
        @RequestParam(required = false) taskType: TaskType?,

        @Parameter(description = "任务名称 (task name) 模糊搜索", required = false)
        @RequestParam(required = false) taskName: String?,

        @Parameter(description = "创建人 (created by) 过滤", required = false)
        @RequestParam(required = false) createdBy: String?,

        @Parameter(description = "创建时间起始 (date from)，格式 yyyy-MM-dd HH:mm:ss", required = false)
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") dateFrom: LocalDateTime?,

        @Parameter(description = "创建时间结束 (date to)，格式 yyyy-MM-dd HH:mm:ss", required = false)
        @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") dateTo: LocalDateTime?,

        @Parameter(description = "是否启用 (is enabled) 过滤", required = false)
        @RequestParam(required = false) isEnabled: Boolean?,

        @Parameter(description = "批处理ID (batch id) 过滤", required = false)
        @RequestParam(required = false) batchId: String?,

        @Parameter(description = "数据源ID (source id) 过滤", required = false)
        @RequestParam(required = false) sourceId: Long?,

        @Parameter(description = "排序字段 (sort field)", example = "executed_at")
        @RequestParam(defaultValue = "executed_at") sortField: String,

        @Parameter(description = "排序方向 (sort order)", example = "desc")
        @RequestParam(defaultValue = "desc") sortOrder: String
    ): ResponseEntity<ApiResponse<Page<LineageTaskDto>>> {
        return try {
            // 验证参数
            val validatedSize = if (size > 100) 100 else size
            val validatedPage = if (page < 1) 0 else page - 1

            // 构建查询条件
            val criteria = LineageTaskCustomRepository.TaskQueryCriteria(
                statuses = status?.takeIf { it.isNotEmpty() },
                taskType = taskType,
                taskName = taskName,
                createdBy = createdBy,
                dateFrom = dateFrom,
                dateTo = dateTo,
                isEnabled = isEnabled,
                batchId = batchId,
                sourceId = sourceId
            )

            // 构建分页和排序
            val pageable = createPageable(validatedPage, validatedSize, sortField, sortOrder)

            // 执行查询
            val result = lineageTaskService.findTasks(criteria, pageable)

            // 转换为DTO
            val dtoPage = result.map { it.toDto() }

            logger.debug("查询血缘任务: page=$page, size=$size, totalElements=${result.totalElements}")

            ResponseEntity.ok(ApiResponse.success(dtoPage))

        } catch (e: IllegalArgumentException) {
            logger.warn("查询参数错误", e)
            ResponseEntity.ok(ApiResponse.error("查询参数错误: ${e.message}"))

        } catch (e: Exception) {
            logger.error("查询血缘任务时发生错误", e)
            ResponseEntity.ok(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 重跑血缘任务 (Rerun lineage task)
     *
     * @param taskId 任务ID
     * @param request 重跑请求
     * @return 任务执行结果
     */
    @PostMapping("/{taskId}/rerun")
    @Operation(
        summary = "重跑血缘任务",
        description = "根据任务ID重跑血缘任务")
    fun rerunTask(
        @PathVariable taskId: Long,
        @RequestBody request: RerunTaskRequest
    ): ResponseEntity<ApiResponse<TaskExecutionResult>> {
        return try {
            logger.info("2e4f19ba | 接收到重跑血缘任务请求: taskId=$taskId, executedBy=${request.executedBy}")

            val result = lineageTaskService.rerunTask(taskId, request)

            logger.info("血缘任务重跑: taskId=$taskId, status=${result.status}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = result,
                    message = result.message
                )
            )

        } catch (e: LineageTaskNotFoundException) {
            logger.warn("任务不存在: taskId=$taskId", e)
            ResponseEntity.ok(ApiResponse.error("任务不存在: $taskId"))

        } catch (e: IllegalStateException) {
            logger.warn("任务状态不允许重跑: taskId=$taskId", e)
            ResponseEntity.ok(ApiResponse.error("任务状态不允许重跑: ${e.message}"))

        } catch (e: Exception) {
            logger.error("重跑血缘任务时发生错误: taskId=$taskId", e)
            ResponseEntity.ok(ApiResponse.error("重跑失败: ${e.message}"))
        }
    }


    /**
     * 获取任务详情 (Get task details)
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    @GetMapping("/{taskId}")
    @Operation(
        summary = "获取血缘任务详情",
        description = "根据任务ID获取血缘任务的详细信息")
    fun getTask(@PathVariable taskId: Long): ResponseEntity<ApiResponse<LineageTaskDto>> {
        return try {
            logger.info("c3f7d9e2 | 接收到获取任务详情请求: taskId=$taskId")

            val task = lineageTaskService.findById(taskId)
            if (task == null) {
                logger.warn("7f3e9a1d | 任务不存在: taskId=$taskId")
                return ResponseEntity.ok(ApiResponse.error("任务不存在: $taskId"))
            }

            logger.info("b6a8e4f1 | 任务详情查询成功: taskId=$taskId, taskName=${task.taskName}")

            ResponseEntity.ok(ApiResponse.success(task.toDto()))

        } catch (e: Exception) {
            logger.error("9d2f5c8a | 获取任务详情时发生错误: taskId=$taskId", e)
            ResponseEntity.ok(ApiResponse.error("获取任务详情失败: ${e.message}"))
        }
    }

    /**
     * 查看任务的处理历史和日志 (View task processing history and logs)
     * 
     * UC-07: 查看特定LineageTask的JobProcessingHistory和失败详情
     * 
     * @param taskId 任务ID
     * @return 任务处理历史详情，包括成功和失败记录
     */
    @GetMapping("/{taskId}/logs")
    @Operation(
        summary = "查看血缘任务处理历史和日志",
        description = "根据任务ID获取血缘任务的处理历史和日志")
    fun getTaskLogs(@PathVariable taskId: Long): ResponseEntity<ApiResponse<TaskLogsDto>> {
        return try {
            logger.info("e7f3a9b4 | 接收到查看任务处理日志请求: taskId=$taskId")

            val taskLogs = lineageTaskService.getTaskLogs(taskId)
            if (taskLogs == null) {
                logger.warn("4e2d8f7a | 任务不存在: taskId=$taskId")
                return ResponseEntity.ok(ApiResponse.error("任务不存在: $taskId"))
            }

            logger.info("2a8e6d1c | 任务日志查询成功: taskId=$taskId, 历史记录数=${taskLogs.processingHistory.size}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = taskLogs,
                    message = "查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("5b9f4e7a | 查看任务处理日志时发生错误: taskId=$taskId", e)
            ResponseEntity.ok(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 获取任务的最新执行日志信息 (Get latest execution log info for task)
     * 
     * 返回任务的最新执行日志信息，包括执行状态、时间、数据库连接、SQL查询等详细信息
     * 
     * @param taskId 任务ID
     * @return 任务的最新执行日志信息
     */
    @GetMapping("/{taskId}/execution-log")
    @Operation(
        summary = "获取任务最新执行日志信息",
        description = "根据任务ID获取血缘任务的最新执行日志信息，包括执行时间、状态、数据库连接、SQL查询等")
    fun getTaskExecutionLog(@PathVariable taskId: Long): ResponseEntity<ApiResponse<TaskLatestExecutionLogResponse>> {
        return try {
            logger.info("4f7e9a2d | 接收到查询任务执行日志请求: taskId=$taskId")

            val executionInfo = executionLogService.getTaskExecutionInfo(taskId)
            if (executionInfo == null) {
                logger.warn("2f8d4e9a | 任务不存在: taskId=$taskId")
                return ResponseEntity.ok(ApiResponse.error("任务不存在: $taskId"))
            }

            logger.info("8b3c5f1e | 任务执行日志查询成功: taskId=$taskId, 执行次数=${executionInfo.executionCount}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = executionInfo,
                    message = "查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("9d4a6e7c | 查询任务执行日志时发生错误: taskId=$taskId", e)
            ResponseEntity.ok(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 获取任务的最新执行日志摘要 (Get latest execution log summary for task)
     * 
     * 返回任务的最新执行日志摘要，包含开始时间和格式化的消息列表
     * 
     * @param taskId 任务ID
     * @return 任务的最新执行日志摘要
     */
    @GetMapping("/{taskId}/execution-logs")
    @Operation(
        summary = "获取任务的最新执行日志摘要",
        description = "根据任务ID获取血缘任务的最新执行日志摘要，包含开始时间和格式化消息列表")
    fun getTaskExecutionLogs(@PathVariable taskId: Long): ResponseEntity<ApiResponse<TaskExecutionLogSummaryResponse?>> {
        return try {
            logger.info("2e8f4b9a | 接收到查询任务最新执行日志摘要请求: taskId=$taskId")

            // 先检查任务是否存在
            val task = lineageTaskService.findById(taskId)
            if (task == null) {
                logger.warn("5c3a8e7d | 任务不存在: taskId=$taskId")
                return ResponseEntity.ok(ApiResponse.error("任务不存在: $taskId"))
            }

            val latestExecutionLog: ExecutionLogResponse? = executionLogService.getLatestExecutionLog(taskId)

            val summaryResponse = if (latestExecutionLog != null) {
                logger.info("7c5a9d3f | 任务最新执行日志查询成功: taskId=$taskId, executionId=${latestExecutionLog.executionId}")
                formatExecutionLogToSummary(latestExecutionLog)
            } else {
                logger.info("a4b8c6e2 | 任务暂无执行日志: taskId=$taskId")
                null
            }

            ResponseEntity.ok(
                ApiResponse.success(
                    data = summaryResponse,
                    message = "查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("6f1e8b4a | 查询任务最新执行日志时发生错误: taskId=$taskId", e)
            ResponseEntity.ok(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    /**
     * 手动导入血缘文件 (Manual Lineage Import)
     * 
     * UC-08: 上传文件触发MANUAL_IMPORT任务，源类型为MANUAL_INPUT
     * 
     * @param file 上传的血缘数据文件
     * @param taskName 任务名称（可选，默认生成）
     * @param createdBy 创建人
     * @return 任务创建和处理结果
     */
    @PostMapping("/manual-import", consumes = ["multipart/form-data"])
    @Operation(
        summary = "手动导入血缘文件",
        description = "上传血缘数据文件并触发MANUAL_IMPORT任务")
    fun manualImport(
        @RequestParam("file") file: MultipartFile,
        @RequestParam(required = false) taskName: String?,
        @RequestParam createdBy: String
    ): ResponseEntity<ApiResponse<ManualImportResult>> {
        return try {
            logger.info("f8e2d4a7 | 接收到手动血缘导入请求: fileName=${file.originalFilename}, createdBy=$createdBy")

            if (file.isEmpty) {
                return ResponseEntity.ok(ApiResponse.error("上传文件不能为空"))
            }

            val result = lineageTaskService.processManualImport(file, taskName, createdBy)

            logger.info("9c1b6f3e | 手动血缘导入完成: taskId=${result.taskId}, status=${result.status}")

            when (result.status) {
                TaskStatus.SUCCESS -> ResponseEntity.ok(
                    ApiResponse.success(
                        data = result,
                        message = "血缘导入成功"
                    )
                )
                TaskStatus.FAILED -> ResponseEntity.ok(
                    ApiResponse.error("血缘导入失败: ${result.errorMessage}"))
                else -> ResponseEntity.ok(
                    ApiResponse.success(
                        data = result,
                        message = "血缘导入任务已创建"
                    )
                )
            }

        } catch (e: IllegalArgumentException) {
            logger.warn("7d4f8a2b | 手动血缘导入参数错误", e)
            ResponseEntity.ok(ApiResponse.error("请求参数错误: ${e.message}"))

        } catch (e: Exception) {
            logger.error("3a9e5c1f | 手动血缘导入时发生错误", e)
            ResponseEntity.ok(ApiResponse.error("导入失败: ${e.message}"))
        }
    }

    /**
     * 创建分页对象 (Create pageable object)
     */
    /**
     * 根据源系统ID和目标系统ID获取表对关系 (Get table pairs by source and target system IDs)
     * 
     * @param sourceSystemId 源系统ID
     * @param targetSystemId 目标系统ID
     * @return 表对关系列表
     */
    @GetMapping("/table-pairs")
    @Operation(
        summary = "获取系统间表对关系",
        description = "根据源系统ID和目标系统ID获取表级血缘关系中的源表和目标表对")
    fun getTablePairsBySystemIds(
        @Parameter(description = "源系统ID", required = true)
        @RequestParam sourceSystemId: Long,
        @Parameter(description = "目标系统ID", required = true)
        @RequestParam targetSystemId: Long
    ): ResponseEntity<ApiResponse<List<TablePairDto>>> {
        return try {
            logger.info("7f2e1a9c | 接收到获取表对关系请求: sourceSystemId=$sourceSystemId, targetSystemId=$targetSystemId")

            val tablePairs = lineageTaskService.getTablePairsBySystemIds(sourceSystemId, targetSystemId)

            logger.info("3c4f8e7b | 表对关系查询成功: sourceSystemId=$sourceSystemId, targetSystemId=$targetSystemId, 结果数=${tablePairs.size}")

            ResponseEntity.ok(
                ApiResponse.success(
                    data = tablePairs,
                    message = "查询成功"
                )
            )

        } catch (e: Exception) {
            logger.error("9d1f3e5a | 获取表对关系时发生错误: sourceSystemId=$sourceSystemId, targetSystemId=$targetSystemId", e)
            ResponseEntity.ok(ApiResponse.error("查询失败: ${e.message}"))
        }
    }

    private fun createPageable(page: Int, size: Int, sortField: String, sortOrder: String): PageRequest {
        return try {
            val direction = Sort.Direction.fromString(sortOrder.trim())
            val property = sortField.trim()
            PageRequest.of(page, size, Sort.by(direction, property))
        } catch (e: Exception) {
            logger.warn("c8a9f4e1 | 解析排序参数失败，使用默认排序: sortField=$sortField, sortOrder=$sortOrder", e)
            PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "executed_at"))
        }
    }
}

/**
 * 血缘任务 DTO (Lineage Task DTO)
 */
data class LineageTaskDto(
    val id: Long,
    val taskName: String,
    val taskType: TaskType,
    val sourceType: SourceType,
    val sourceIdentifier: String?,
    val status: TaskStatus,
    val scheduleType: ScheduleType,
    val isEnabled: Boolean,
    val createdBy: String?,
    val executedAt: String?,
    val completedAt: String?,
    val processingTimeMs: Long?,
    val hasChanges: Boolean?,
    val batchId: String?,
    val executionCount: Int,
    val errorMessage: String?,
    val createdAt: String,
    val updatedAt: String
)

/**
 * 任务日志DTO (Task Logs DTO)
 * 
 * UC-07: 包含任务的处理历史和失败信息
 */
data class TaskLogsDto(
    val task: LineageTaskDto,
    val processingHistory: List<JobProcessingHistoryDto>,
    val failedExecutions: List<FailedTaskExecutionDto>
)

/**
 * 作业处理历史DTO (Job Processing History DTO)
 */
data class JobProcessingHistoryDto(
    val id: Long,
    val processedAt: String,
    val processingResult: String,
    val changesDetected: Boolean,
    val processingDurationMs: Long,
    val lineageHash: String,
    val errorMessage: String?
)

/**
 * 失败任务执行DTO (Failed Task Execution DTO)
 */
data class FailedTaskExecutionDto(
    val executionId: String?,
    val failedAt: String?,
    val errorMessage: String?,
    val processingTimeMs: Long?
)

/**
 * 手动导入结果DTO (Manual Import Result DTO)
 * 
 * UC-08: 手动血缘导入的结果信息
 */
data class ManualImportResult(
    val taskId: Long,
    val taskName: String,
    val fileName: String,
    val status: TaskStatus,
    val lineageCount: Int? = null,
    val processingTimeMs: Long? = null,
    val errorMessage: String? = null,
    val createdAt: String
)

/**
 * 表对关系DTO (Table Pair DTO)
 * 
 * 表示源系统和目标系统之间的表级血缘关系对
 */
data class TablePairDto(
    val sourceTable: String,
    val sourceSchema: String?,
    val sourceDatasource: String,
    val sourceSystemId: Long?,
    val sourceSystemName: String?,
    val targetTable: String,
    val targetSchema: String?,
    val targetDatasource: String,
    val targetSystemId: Long?,
    val targetSystemName: String?,
    val lineageType: String,
    val confidenceScore: BigDecimal?,
    val relationshipCount: Int,
    val createdAt: String?
)

/**
 * Excel解析测试结果DTO (Excel Parsing Test Result DTO)
 * 
 * 用于测试Excel文件解析功能的响应结果
 */
data class ExcelParsingTestResult(
    val fileName: String,
    val fileSize: Long,
    val processingTimeMs: Long,
    val lineageCount: Int,
    val lineageSummary: List<LineageSummary>,
    val validation: ValidationResult
)

/**
 * 血缘摘要DTO (Lineage Summary DTO)
 * 
 * 包含血缘数据的摘要信息
 */
data class LineageSummary(
    val jobId: String,
    val jobName: String,
    val sourceDatabase: String,
    val targetDatabase: String,
    val sourceTables: List<String>,
    val targetTable: String,
    val columnMappingCount: Int,
    val columnMappings: List<String>
)

/**
 * 验证结果DTO (Validation Result DTO)
 * 
 * 表示数据验证的结果
 */
data class ValidationResult(
    val isValid: Boolean,
    val errorMessage: String?
)

/**
 * 任务执行日志摘要响应 (Task Execution Log Summary Response)
 * 
 * 简化的执行日志信息，便于API客户端阅读
 */
data class TaskExecutionLogSummaryResponse(
    val startedAt: LocalDateTime?,
    val messages: List<String>
)

/**
 * LineageTask 转 DTO 扩展函数 (Extension function to convert LineageTask to DTO)
 */
fun LineageTask.toDto(): LineageTaskDto {
    return LineageTaskDto(
        id = this.id,
        taskName = this.taskName,
        taskType = this.taskType,
        sourceType = this.sourceType,
        sourceIdentifier = this.sourceIdentifier,
        status = this.taskStatus,
        scheduleType = this.scheduleType,
        isEnabled = this.isEnabled,
        createdBy = this.createdBy,
        executedAt = this.executedAt?.toString(),
        completedAt = this.completedAt?.toString(),
        processingTimeMs = this.processingTimeMs,
        hasChanges = this.hasChanges,
        batchId = this.batchId,
        executionCount = this.executionCount,
        errorMessage = this.errorMessage,
        createdAt = this.createdAt.toString(),
        updatedAt = this.updatedAt.toString()
    )
}

/**
 * 格式化执行日志为摘要信息 (Format execution log to summary info)
 * 
 * 根据不同的source_job_type生成相应的消息列表
 */
fun formatExecutionLogToSummary(executionLog: ExecutionLogResponse): TaskExecutionLogSummaryResponse {
    val messages = mutableListOf<String>()
    
    // 1. 基础状态信息
    messages.add("执行状态: ${executionLog.taskStatus ?: "未知"}")
    messages.add("处理时间: ${executionLog.processingTimeMs ?: 0}ms")
    
    // 2. 根据source_job_type生成特定信息
    when (executionLog.sourceJobType) {
        "DATA_EXCHANGE_JOB" -> {
            // 数据交换作业信息
            messages.add("作业类型: 数据交换平台")
            
            // 数据库连接信息
            executionLog.databaseConnectionString?.let { connStr ->
                if (connStr.contains("->")) {
                    val parts = connStr.split("->").map { it.trim() }
                    messages.add("源数据库: ${parts.getOrNull(0) ?: "未知"}")
                    messages.add("目标数据库: ${parts.getOrNull(1) ?: "未知"}")
                } else {
                    messages.add("数据库连接: $connStr")
                }
            }
            
            // 作业ID信息
            executionLog.sourceJobId?.let { jobId ->
                if (jobId.contains("_")) {
                    val parts = jobId.split("_")
                    messages.add("读取作业ID: ${parts.getOrNull(0) ?: "未知"}")
                    messages.add("写入作业ID: ${parts.getOrNull(1) ?: "未知"}")
                } else {
                    messages.add("作业ID: $jobId")
                }
            }
            
            // SQL查询数量
            executionLog.sqlQueries?.let { queries ->
                messages.add("SQL查询数量: ${queries.size}")
            }
            
            // 从additionalInfo中提取任务ID
            executionLog.additionalInfo?.let { info ->
                (info["task_id"] as? String)?.let { taskId ->
                    messages.add("任务ID: $taskId")
                }
            }
        }
        
        "HDFS_SHELL_SCRIPT_JOB" -> {
            // HDFS脚本作业信息
            messages.add("作业类型: 大数据平台脚本")
            
            // ZIP文件路径
            executionLog.databaseConnectionString?.let { zipPath ->
                messages.add("脚本包路径: $zipPath")
            }
            
            // 作业ID
            executionLog.sourceJobId?.let { jobId ->
                messages.add("脚本作业ID: $jobId")
            }
            
            // 从additionalInfo中提取脚本名称
            executionLog.additionalInfo?.let { info ->
                (info["task_id"] as? String)?.let { taskId ->
                    messages.add("任务ID: $taskId")
                }
                (info["script_name"] as? String)?.let { scriptName ->
                    messages.add("脚本名称: $scriptName")
                }
                (info["zip_file_path"] as? String)?.let { zipFile ->
                    messages.add("ZIP文件: $zipFile")
                }
                (info["processing_result"] as? String)?.let { result ->
                    messages.add("处理结果: $result")
                }
            }
        }
        
        else -> {
            // 通用作业信息
            messages.add("作业类型: ${executionLog.sourceJobType ?: "未知"}")
            executionLog.sourceJobId?.let { jobId ->
                messages.add("作业ID: $jobId")
            }
            
            // 从additionalInfo中提取通用信息
            executionLog.additionalInfo?.let { info ->
                (info["task_id"] as? String)?.let { taskId ->
                    messages.add("任务ID: $taskId")
                }
            }
        }
    }
    
    // 3. 执行步骤
    executionLog.executionStep?.let { step ->
        messages.add("执行步骤: $step")
    }
    
    // 4. 日志消息
    messages.add("日志信息: ${executionLog.logMessage}")
    
    // 5. 异常信息（如果有）
    executionLog.exceptionStack?.let { exception ->
        messages.add("异常信息: $exception")
    }
    
    return TaskExecutionLogSummaryResponse(
        startedAt = executionLog.startedAt?.let { LocalDateTime.parse(it) },
        messages = messages
    )
}
