package com.datayes.storage

import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import java.time.LocalDateTime

/**
 * S3存储REST API控制器 (S3 Storage REST API Controller)
 *
 * 提供文件上传和下载的REST API接口
 */
@RestController
@RequestMapping("/api/storage")
@Tag(name = "S3存储API", description = "文件上传和下载接口")
class S3StorageController(
    private val s3StorageService: S3StorageService
) {

    private val logger = LoggerFactory.getLogger(S3StorageController::class.java)

    /**
     * 上传文件 (Upload file)
     */
    @PostMapping("/upload", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @Operation(summary = "上传文件", description = "上传文件到S3存储，返回文件键用于后续下载")
    fun uploadFile(
        @Parameter(description = "上传的文件", required = true)
        @RequestParam("file") file: MultipartFile,

        @Parameter(description = "文件描述（可选）")
        @RequestParam("description", required = false) description: String?
    ): ResponseEntity<UploadResponse> {

        logger.info("6f8b1d4a | 接收文件上传请求: ${file.originalFilename}, 大小: ${file.size} 字节")

        if (file.isEmpty) {
            return ResponseEntity.badRequest().body(
                UploadResponse(
                    success = false,
                    message = "文件不能为空"
                )
            )
        }

        val originalFilename = file.originalFilename
        if (originalFilename.isNullOrBlank()) {
            return ResponseEntity.badRequest().body(
                UploadResponse(
                    success = false,
                    message = "文件名不能为空"
                )
            )
        }

        try {
            val result = s3StorageService.uploadFile(
                fileName = originalFilename,
                content = file.bytes,
                contentType = file.contentType ?: "application/octet-stream"
            )

            if (result.success) {
                logger.info("7a9c2e5b | 文件上传成功: ${result.fileKey}")
                return ResponseEntity.ok(
                    UploadResponse(
                        success = true,
                        message = "文件上传成功",
                        fileKey = result.fileKey,
                        originalFileName = result.originalFileName,
                        fileSize = result.fileSize,
                        contentType = result.contentType,
                        uploadTime = result.uploadTime,
                        description = description
                    )
                )
            } else {
                logger.error("8b1d3f6c | 文件上传失败: ${result.errorMessage}")
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                    UploadResponse(
                        success = false,
                        message = result.errorMessage ?: "上传失败"
                    )
                )
            }

        } catch (e: Exception) {
            logger.error("9c2e4a7d | 文件上传异常", e)
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                UploadResponse(
                    success = false,
                    message = "服务器内部错误: ${e.message}"
                )
            )
        }
    }

    /**
     * 下载文件 (Download file)
     */
    @GetMapping("/download")
    @Operation(summary = "下载文件", description = "根据文件键下载文件")
    fun downloadFile(
        @Parameter(description = "文件键", required = true)
        @RequestParam("fileKey") fileKey: String,

        @Parameter(description = "是否作为附件下载")
        @RequestParam("attachment", defaultValue = "true") attachment: Boolean
    ): ResponseEntity<ByteArray> {

        logger.info("1d3f5b8e | 接收文件下载请求: $fileKey")

        try {
            val result = s3StorageService.downloadFile(fileKey)

            if (result.success && result.content != null) {
                logger.info("2e4a6c9f | 文件下载成功: $fileKey, 大小: ${result.content.size} 字节")

                val headers = HttpHeaders()
                headers.contentType = MediaType.parseMediaType(result.contentType ?: "application/octet-stream")
                headers.contentLength = result.content.size.toLong()

                if (attachment) {
                    val fileName = fileKey.substringAfterLast('/')
                    headers.setContentDispositionFormData("attachment", fileName)
                }

                return ResponseEntity.ok()
                    .headers(headers)
                    .body(result.content)

            } else {
                logger.warn("3f5b7d1a | 文件下载失败: $fileKey - ${result.errorMessage}")
                return ResponseEntity.notFound().build()
            }

        } catch (e: Exception) {
            logger.error("4a6c8e2b | 文件下载异常: $fileKey", e)
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build()
        }
    }

    /**
     * 检查文件是否存在 (Check if file exists)
     */
    @GetMapping("/exists")
    @Operation(summary = "检查文件是否存在", description = "检查指定文件键的文件是否存在")
    fun checkFileExists(
        @Parameter(description = "文件键", required = true)
        @RequestParam("fileKey") fileKey: String
    ): ResponseEntity<FileExistsResponse> {

        logger.info("5b7d9f3c | 检查文件是否存在: $fileKey")

        try {
            val exists = s3StorageService.fileExists(fileKey)

            return ResponseEntity.ok(
                FileExistsResponse(
                    fileKey = fileKey,
                    exists = exists,
                    checkTime = LocalDateTime.now()
                )
            )

        } catch (e: Exception) {
            logger.error("6c8e1a4d | 检查文件存在性异常: $fileKey", e)
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                FileExistsResponse(
                    fileKey = fileKey,
                    exists = false,
                    error = e.message,
                    checkTime = LocalDateTime.now()
                )
            )
        }
    }

    /**
     * 列出文件 (List files)
     */
    @GetMapping("/list")
    @Operation(summary = "列出文件", description = "列出存储中的文件")
    fun listFiles(
        @Parameter(description = "文件前缀过滤")
        @RequestParam("prefix", required = false) prefix: String?,

        @Parameter(description = "最大返回数量")
        @RequestParam("maxKeys", defaultValue = "100") maxKeys: Int
    ): ResponseEntity<ListFilesResponse> {

        logger.info("7d9f2b5e | 列出文件请求，前缀: $prefix, 最大数量: $maxKeys")

        try {
            val files = s3StorageService.listFiles(prefix, maxKeys = maxKeys)

            logger.info("8e1a3c6f | 找到 ${files.size} 个文件")

            return ResponseEntity.ok(
                ListFilesResponse(
                    success = true,
                    files = files.map { file ->
                        FileInfo(
                            key = file.key,
                            size = file.size
                        )
                    },
                    totalCount = files.size,
                    listTime = LocalDateTime.now()
                )
            )

        } catch (e: Exception) {
            logger.error("9f2b4d7a | 列出文件异常", e)
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(
                ListFilesResponse(
                    success = false,
                    error = e.message,
                    listTime = LocalDateTime.now()
                )
            )
        }
    }
}

/**
 * 文件上传响应 (File Upload Response)
 */
data class UploadResponse(
    val success: Boolean,
    val message: String,
    val fileKey: String? = null,
    val originalFileName: String? = null,
    val fileSize: Long? = null,
    val contentType: String? = null,
    val uploadTime: LocalDateTime? = null,
    val description: String? = null
)

/**
 * 文件存在性检查响应 (File Exists Check Response)
 */
data class FileExistsResponse(
    val fileKey: String,
    val exists: Boolean,
    val checkTime: LocalDateTime,
    val error: String? = null
)

/**
 * 文件列表响应 (File List Response)
 */
data class ListFilesResponse(
    val success: Boolean,
    val files: List<FileInfo>? = null,
    val totalCount: Int? = null,
    val listTime: LocalDateTime,
    val error: String? = null
)

/**
 * 文件信息 (File Info)
 */
data class FileInfo(
    val key: String,
    val size: Long
)