package com.datayes.lineage

import com.datayes.ApiResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 数据库名称匹配控制器 (Database Name Matching Controller)
 * 
 * 提供根据数据库名称查找或创建血缘数据源的 REST API
 */
@Tag(name = "Database Name Matching", description = "数据库名称匹配接口")
@RestController
@RequestMapping("/api/lineage/database-matching")
@CrossOrigin(origins = ["*"])
class DatabaseNameMatchingController(
    private val databaseNameMatchingService: DatabaseNameMatchingService
) {
    
    private val logger = LoggerFactory.getLogger(DatabaseNameMatchingController::class.java)
    
    /**
     * 根据数据库名称查找或创建血缘数据源
     * 
     * @param databaseName 数据库名称
     * @return 血缘数据源ID
     */
    @Operation(
        summary = "根据数据库名称查找或创建血缘数据源",
        description = """
            根据数据库名称在metadata_data_source表中查找匹配记录，并返回对应的lineage_datasources记录ID。
            
            匹配逻辑：
            1. 根据DB_NAME字段查找所有匹配的有效记录
            2. 如果有多个匹配，优先选择DB_TYPE为hive相关类型（hive, hive2等）
            3. 如果多个都是hive类型，选择CREATE_TIME最早的记录
            4. 基于选中的metadata_data_source记录，查找或创建对应的lineage_datasources记录
            5. 返回lineage_datasources记录的ID
            
            如果lineage_datasources记录不存在，将自动创建新记录。
        """
    )
    @GetMapping("/find-or-create")
    fun findOrCreateLineageDatasourceByDatabaseName(
        @Parameter(description = "数据库名称", required = true, example = "example_db")
        @RequestParam databaseName: String
    ): ResponseEntity<ApiResponse<DatabaseNameMatchingResponse>> {
        logger.info("s9t0u1v2 | 接收到根据数据库名称查找血缘数据源的请求: databaseName=$databaseName")
        
        return try {
            val lineageDatasourceId = databaseNameMatchingService.findOrCreateLineageDatasourceByDatabaseName(databaseName)
            
            val response = DatabaseNameMatchingResponse(
                lineageDatasourceId = lineageDatasourceId,
                databaseName = databaseName,
                success = true
            )
            
            logger.info("w3x4y5z6 | 数据库名称匹配成功: databaseName=$databaseName, lineageDatasourceId=$lineageDatasourceId")
            
            ResponseEntity.ok(
                ApiResponse(
                    success = true,
                    data = response,
                    message = "成功查找或创建血缘数据源: lineageDatasourceId=$lineageDatasourceId"
                )
            )
        } catch (e: IllegalArgumentException) {
            logger.warn("a7b8c9d0 | 数据库名称匹配失败: databaseName=$databaseName, reason=${e.message}")
            
            ResponseEntity.badRequest().body(
                ApiResponse<DatabaseNameMatchingResponse>(
                    success = false,
                    message = "未找到匹配的数据库: ${e.message}"
                )
            )
        } catch (e: Exception) {
            logger.error("e1f2g3h4 | 数据库名称匹配过程中发生错误: databaseName=$databaseName", e)
            
            ResponseEntity.internalServerError().body(
                ApiResponse<DatabaseNameMatchingResponse>(
                    success = false,
                    message = "处理过程中发生错误: ${e.message}"
                )
            )
        }
    }
    
    /**
     * 批量处理多个数据库名称
     * 
     * @param request 包含多个数据库名称的请求
     * @return 批量处理结果
     */
    @Operation(
        summary = "批量根据数据库名称查找或创建血缘数据源",
        description = """
            批量处理多个数据库名称，为每个数据库名称查找或创建对应的血缘数据源。
            
            返回每个数据库名称的处理结果，包括成功和失败的情况。
        """
    )
    @PostMapping("/batch-find-or-create")
    fun batchFindOrCreateLineageDatasources(
        @RequestBody request: BatchDatabaseNameRequest
    ): ResponseEntity<ApiResponse<BatchDatabaseNameResponse>> {
        logger.info("i5j6k7l8 | 接收到批量数据库名称匹配请求: 数量=${request.databaseNames.size}")
        
        return try {
            val results = mutableListOf<DatabaseNameMatchingResponse>()
            var successCount = 0
            var errorCount = 0
            
            request.databaseNames.forEach { databaseName ->
                try {
                    val lineageDatasourceId = databaseNameMatchingService.findOrCreateLineageDatasourceByDatabaseName(databaseName)
                    results.add(
                        DatabaseNameMatchingResponse(
                            lineageDatasourceId = lineageDatasourceId,
                            databaseName = databaseName,
                            success = true
                        )
                    )
                    successCount++
                } catch (e: Exception) {
                    results.add(
                        DatabaseNameMatchingResponse(
                            lineageDatasourceId = null,
                            databaseName = databaseName,
                            success = false,
                            errorMessage = e.message
                        )
                    )
                    errorCount++
                    logger.warn("m9n0o1p2 | 批量处理中单个数据库名称失败: databaseName=$databaseName, error=${e.message}")
                }
            }
            
            val response = BatchDatabaseNameResponse(
                results = results,
                totalCount = request.databaseNames.size,
                successCount = successCount,
                errorCount = errorCount
            )
            
            logger.info("q3r4s5t6 | 批量数据库名称匹配完成: 总数=${response.totalCount}, 成功=${response.successCount}, 失败=${response.errorCount}")
            
            ResponseEntity.ok(
                ApiResponse(
                    success = true,
                    data = response,
                    message = "批量处理完成: 总数=${response.totalCount}, 成功=${response.successCount}, 失败=${response.errorCount}"
                )
            )
        } catch (e: Exception) {
            logger.error("u7v8w9x0 | 批量数据库名称匹配过程中发生错误", e)
            
            ResponseEntity.internalServerError().body(
                ApiResponse<BatchDatabaseNameResponse>(
                    success = false,
                    message = "批量处理过程中发生错误: ${e.message}"
                )
            )
        }
    }
}

/**
 * 数据库名称匹配响应
 */
data class DatabaseNameMatchingResponse(
    val lineageDatasourceId: Long?,
    val databaseName: String,
    val success: Boolean,
    val errorMessage: String? = null
)

/**
 * 批量数据库名称请求
 */
data class BatchDatabaseNameRequest(
    val databaseNames: List<String>
)

/**
 * 批量数据库名称响应
 */
data class BatchDatabaseNameResponse(
    val results: List<DatabaseNameMatchingResponse>,
    val totalCount: Int,
    val successCount: Int,
    val errorCount: Int
)