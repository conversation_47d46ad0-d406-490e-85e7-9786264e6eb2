# 数据库ER图（ER Diagram）

以下ER图使用 Mermaid 语法描述本项目的核心数据库表及其关联关系。

```mermaid
erDiagram
    lineage_systems {
        BIGINT id PK "主键"
        VARCHAR system_name "系统名称"
        VARCHAR system_code "系统编码"
        ENUM status "状态"
        TIM<PERSON><PERSON><PERSON> created_at
    }

    lineage_datasources {
        BIGINT id PK
        VARCHAR datasource_name
        VARCHAR db_type
        BIGINT system_id FK
        ENUM status
        TIMESTAMP created_at
    }

    lineage_tables {
        BIGINT id PK
        BIGINT datasource_id FK
        VARCHAR schema_name
        VARCHAR table_name
        ENUM table_type
        ENUM status
    }

    lineage_columns {
        BIGINT id PK
        BIGINT table_id FK
        VARCHAR column_name
        VARCHAR data_type
        BOOLEAN is_primary_key
        ENUM status
    }

    lineage_tasks {
        BIGINT id PK
        VARCHAR task_name
        ENUM task_type
        ENUM task_status
        TIMESTAMP created_at
    }

    lineage_relationships {
        BIGINT id PK
        BIGINT task_id FK
        BIGINT source_table_id FK
        BIGINT target_table_id FK
        BIGINT source_column_id FK
        BIGINT target_column_id FK
        ENUM relationship_type
        BOOLEAN is_active
    }

    lineage_execution_logs {
        BIGINT id PK
        BIGINT task_id FK
        ENUM log_level
        TIMESTAMP created_at
    }

    lineage_statistics {
        BIGINT id PK
        BIGINT system_id FK
        BIGINT datasource_id FK
        INT table_count
        INT column_count
        INT relationship_count
    }

    manual_lineage_audit_log {
        BIGINT id PK
        BIGINT relationship_id FK
        ENUM operation_type
        TIMESTAMP performed_at
    }

    manual_lineage_trigger_tasks {
        BIGINT id PK
        VARCHAR task_uuid
        BIGINT datasource_id FK
        ENUM task_status
        TIMESTAMP created_at
    }

    uploaded_scripts {
        BIGINT id PK
        VARCHAR script_name
        ENUM script_type
        VARCHAR file_path
        ENUM analysis_status
    }

    lineage_systems ||--o{ lineage_datasources : "拥有 (has)"
    lineage_datasources ||--o{ lineage_tables : "拥有 (has)"
    lineage_tables ||--o{ lineage_columns : "拥有 (has)"

    lineage_tasks ||--o{ lineage_relationships : "生成 (generates)"
    lineage_tables ||--o{ lineage_relationships : "来源表 (source)"
    lineage_tables ||--o{ lineage_relationships : "目标表 (target)"

    lineage_relationships ||--o{ manual_lineage_audit_log : "审计 (audit)"
    lineage_datasources ||--o{ manual_lineage_trigger_tasks : "触发 (trigger)"
    lineage_tasks ||--o{ lineage_execution_logs : "日志 (log)"

    lineage_systems ||--o{ lineage_statistics : "统计 (stats)"
    lineage_datasources ||--o{ lineage_statistics : "统计 (stats)"
```
 