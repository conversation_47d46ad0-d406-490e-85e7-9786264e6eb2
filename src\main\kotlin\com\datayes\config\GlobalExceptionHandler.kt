package com.datayes.config

import com.datayes.ApiResponse
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler

/**
 * 全局异常处理器 (Global Exception Handler)
 *
 * 统一处理所有REST API异常，返回标准的ApiResponse格式
 * HTTP状态码统一返回200，错误信息通过ApiResponse.success字段表示
 */
@ControllerAdvice
class GlobalExceptionHandler {

    private val logger = LoggerFactory.getLogger(GlobalExceptionHandler::class.java)

    /**
     * 处理所有异常 (Handle all exceptions)
     *
     * @param e 异常对象
     * @return 标准API响应格式，HTTP 200 + ApiResponse(success=false)
     */
    @ExceptionHandler(Exception::class)
    fun handleAllExceptions(e: Exception): ResponseEntity<ApiResponse<Any>> {
        logger.error("60f54bda | 全局异常处理器捕获异常", e)

        val errorMessage = e.message ?: "未知错误 (Unknown error)"
        val apiResponse = ApiResponse.error<Any>(errorMessage)

        val uniqueId = generateUniqueId()
        logger.info("$uniqueId | 返回错误响应: $errorMessage")

        return ResponseEntity.status(HttpStatus.OK).body(apiResponse)
    }

    /**
     * 生成唯一ID用于日志追踪
     */
    private fun generateUniqueId(): String {
        return java.util.UUID.randomUUID().toString().substring(0, 8)
    }
}