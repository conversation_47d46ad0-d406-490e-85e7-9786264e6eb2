insert into urp_bui_prip.temp_LCProduct_03
select * from(
select
row_number() over(partition by trans.busino,lcp.PolicyNo,lcp.ProductNo order by trans.busino,lcp.PolicyNo,lcp.ProductNo) rownum1,
 trans.busino busino,
       '000052' CompanyCode,
       (case when lcp.gpflag = '1' then null else lcp.GrpPolicyNo end) GrpPolicyNo,
       lcp.PolicyNo PolicyNo,
       lcp.ProductNo ProductNo,
       lcp.GPFlag GPFlag,
       lcp.PolTypeFlag PolTypeFlag,
       lcp.MainProductNo MainProductNo,
       lcp.MainProductFlag MainProductFlag,
       lcp.ProductCode ProductCode,
       substr(lcp.ManageCom,1,4) ManageCom,
       lcp.EffDate EffDate,
       lcp.EffDate PayBeginDate,
       lcp.FinalPayDate FinalPayDate,
       (case 
        when cast(lcp.PayMode as int) = 0 then lcp.signdate 
        when to_date(lcp.CurPaidToDate) = date_add(to_date(lcp.FinalPayDate),1) and cast(lcp.PayMode as int) = 1 then add_months(lcp.CurPaidToDate,-1) 
        when to_date(lcp.CurPaidToDate) = date_add(to_date(lcp.FinalPayDate),1) and cast(lcp.PayMode as int) = 12 then add_months(lcp.CurPaidToDate,-12)
        when to_date(lcp.CurPaidToDate) = date_add(to_date(lcp.FinalPayDate),1) and cast(lcp.PayMode as int) = 3 then add_months(lcp.CurPaidToDate,-3)
        when to_date(lcp.CurPaidToDate) = date_add(to_date(lcp.FinalPayDate),1) and cast(lcp.PayMode as int) = 6 then add_months(lcp.CurPaidToDate,-6)
        else lcp.CurPaidToDate end) CurPaidToDate,
       lcp.AnnStartWithdwlDate AnnStartWithdwlDate,
       lcp.AnnGetTodwlDate AnnGetTodwlDate,
       lcp.PenStartWithdwlDate PenStartWithdwlDate,
       lcp.PenGetTodwlDate PenGetTodwlDate,
       lcp.InvalidDate InvalidDate,
       cast(cast(lcp.PayMode as int) as string) PayMode,
       lcp.PayTermType PayTermType,
       (case when cast(cast(lcp.PayMode as int) as string) = '0' then 0 when cast(cast(lcp.PayMode as int) as string) = '-1' then -1 else cast(lcp.PayTerm as int) end) PayTerm,
       (case when lcp.InsurancePeriodFlag = 'Y' and lcp.InsurancePeriod >= 100 then 'O'
             when lcp.InsurancePeriodFlag = 'A' and lcp.InsurancePeriod = 1000 then 'O' 
             else lcp.InsurancePeriodFlag end) InsurancePeriodFlag,
       (case 
             when lcp.InsurancePeriodFlag = '0' then 999 
             when lcp.InsurancePeriodFlag = 'O' then 999 
             when lcp.InsurancePeriodFlag = 'Y' and lcp.InsurancePeriod >= 100 then 999
             when lcp.InsurancePeriodFlag = 'A' and lcp.InsurancePeriod = 1000 then 999
             else cast(lcp.InsurancePeriod as int) end) InsurancePeriod,
       cast(lcp.Copies as int) Copies,
       cast(nvl(lcp.currentpremium,0) as decimal(16,2)) Premium,
       0 AccumPremium,
       cast(nvl(lcp.basicsuminsured,0) as decimal(16,2)) BasicSumInsured,
       lcp.RiskAmnt RiskAmnt,
       nvl(cash.casevalue,0) CashValue,
       lcp.accumdiv AccumDiv,
       lcp.accumdivint AccumDivInt,
       lcp.accumdivsi AccumDivSI,
       lcp.lstdistridate LstDistriDate,
       '' SurrenderAmnt,
       lcp.accumannuitywd AccumAnnuityWD,
       '' AccumMaturityWD,
       cast(nvl(lcp.accumclmbenefit,0) as decimal(16,2)) AccumClmBenefit,
       cast(nvl(lcp.accumclmtimes,0) as int) AccumClmTimes,
       lcp.InsuredType InsuredType,
       lcp.UWConclusion UWConclusion,
       lcp.Status Status,
       lcp.reinsuranceflag ReInsuranceFlag,
       lcp.CoInsuranceFlag CoInsuranceFlag,
       lcp.SpecificBusiness SpecificBusiness,
       lcp.SpecificBusinessCode SpecificBusinessCode,
       lcp.ProductSuspendDate ProductSuspendDate,
       lcp.ProductRecoverDate ProductRecoverDate,
       lcp.TerminationDate TerminationDate,
       (case when length(nvl(lcp.CoInsurancePercentage,''))=0 then 0.0000 else cast(lcp.CoInsurancePercentage as decimal(12,4)) end) CoInsurancePercentage,
       '' CurrentDivAmnt,
       lcp.professionalfee ProfessionalFee,
       lcp.substandardfee SubStandardFee,
       lcp.emrate EMRate,
       lcp.WaiverFlag WaiverFlag,
       lcp.TerminationReason TerminationReason,
       trans.pushdate pushdate
  from (select * from urp_dws.full_lcpol where nvl(appflag,'') <> '3') lcp
  inner join (select busino,pushdate,policyno,busstype from urp_bui_prip.temp_lcpoltransaction where length(nvl(PolicyNo,''))>0) trans on trans.PolicyNo = lcp.PolicyNo
  and (lcp.Status not in ('04','99') or trans.busstype in('57','58'))
  inner join urp_bui_prip.busstype busstype on trans.busstype = busstype.busstype_id and busstype.tablename = 'LCPRODUCT'
  left join (select casevalue,polno from urp_dws.full_trans_cashvalue where cast(intervals as int ) = cast(curyear as int)) cash on cash.polno = lcp.productno
  ) b where b.rownum1 = 1