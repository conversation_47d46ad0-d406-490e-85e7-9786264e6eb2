// package com.datayes.dataexchange
//
// import com.fasterxml.jackson.core.type.TypeReference
// import com.fasterxml.jackson.databind.ObjectMapper
// import org.slf4j.LoggerFactory
// import org.springframework.beans.factory.annotation.Qualifier
// import org.springframework.dao.DataAccessException
// import org.springframework.jdbc.core.JdbcTemplate
// import org.springframework.jdbc.core.RowMapper
// import org.springframework.stereotype.Repository
// import java.sql.ResultSet
//
// /**
//  * 数据交互平台作业数据访问层 (Data Exchange Job Repository)
//  *
//  * 负责从数据交互平台MySQL数据库中查询作业配置信息
//  */
// @Repository
// class DataExchangeJobRepository(
//     @Qualifier("dataExchangeJdbcTemplate")
//     private val jdbcTemplate: JdbcTemplate,
//     private val objectMapper: ObjectMapper
// ) {
//
//     private val logger = LoggerFactory.getLogger(DataExchangeJobRepository::class.java)
//
//     companion object {
//         /**
//          * 查询所有数据交互作业的SQL语句
//          * 该SQL从多个表联合查询，获取完整的作业配置信息
//          */
//         private const val QUERY_ALL_JOBS_SQL = """
//             select
//             rs.reader_job_id,rs.reader_job_name,rs.db_read,rs.reader_table_name,rs.reader_sql,rs.write_job_id,rs.write_job_name,rs.db_w,rs.columns,
//             CASE
//                 WHEN INSTR(rs.db_w, 'jdbc:hive') > 0  THEN writer_table_name_hive
//                 WHEN (rs.writer_table_name1!='' and rs.writer_table_name1 is not null ) THEN rs.writer_table_name1
//                 ELSE writer_table_name3
//             END as  writer_table_name
//              from (
//                 SELECT a.id AS reader_job_id,
//                 a.job_name AS reader_job_name,
//                 dbr.url as db_read,
//                 b.`reader_table_name`,
//                 b.job_config -> '$.job.content[0].reader.parameter.connection[0].querySql[0]' AS `reader_sql`,
//                 f.id AS write_job_id,
//                 f.job_name AS write_job_name,
//                 dbw.url as db_w,
//                 f.writer_table_name1,
//                 f.writer_table_name_hive,
//                 f.writer_table_name3,
//                 b.job_build_data->'$.columns' AS columns
//                  FROM dw_job_info a
//                 JOIN dw_job_config b ON a.id=b.`job_id`
//                 JOIN (SELECT c.id,c.reader_from_job_key,c.job_name,writer_source_id,d.writer_table_name as writer_table_name1 , --  正常存的表名
//                   REPLACE(d.job_build_data -> '$.hiveWriter.writerTable', '""', '') as writer_table_name_hive , -- hive的表名
//                   REPLACE(d.job_build_data -> '$.rdbmsWriter.tables[0]', '""', '')   AS writer_table_name3  -- 其他情况的表名
//                   FROM dw_job_info c JOIN dw_job_config d ON c.id=d.`job_id` and c.status =1
//                      ) f ON a.job_key=f.reader_from_job_key
//                 left join dw_data_source  dbr on  a.reader_source_id=dbr.id
//                 left join dw_data_source  dbw  on  f.writer_source_id=dbw.id
//                 WHERE  a.status =1    --  and f.writer_table_name is null
//              ) rs
//         """
//         // limit 200
//         // where rs.reader_job_id in (473, 608)
//     }
//
//     /**
//      * 查询所有活跃的数据交互作业
//      *
//      * @return 数据交互作业列表
//      * @throws DataAccessException 当数据库访问出错时抛出
//      */
//     fun listActiveDataExchangeJobs(): List<DataExchangeJob> {
//         return try {
//             logger.info("开始查询所有活跃的数据交互作业")
//             val jobs = jdbcTemplate.query(QUERY_ALL_JOBS_SQL, DataExchangeJobRowMapper())
//             logger.info("成功查询到 ${jobs.size} 个数据交互作业")
//             jobs
//         } catch (e: DataAccessException) {
//             logger.error("查询数据交互作业时发生数据库错误", e)
//             throw e
//         } catch (e: Exception) {
//             logger.error("查询数据交互作业时发生未知错误", e)
//             throw e
//         }
//     }
//
//     /**
//      * DataExchangeJob的RowMapper实现
//      * 负责将查询结果映射为DataExchangeJob对象
//      */
//     private inner class DataExchangeJobRowMapper : RowMapper<DataExchangeJob> {
//
//         override fun mapRow(rs: ResultSet, rowNum: Int): DataExchangeJob {
//             return try {
//                 // 解析columns JSON字段
//                 val columnsJson = rs.getString("columns")
//                 val columns = parseColumnsFromJson(columnsJson)
//
//                 DataExchangeJob(
//                     readerJobId = rs.getString("reader_job_id") ?: "",
//                     readerJobName = rs.getString("reader_job_name") ?: "",
//                     dbReader = rs.getString("db_read") ?: "",
//                     readerTableName = rs.getString("reader_table_name") ?: "",
//                     readerSql = rs.getString("reader_sql")?.trim('"') ?: "",
//                     writeJobId = rs.getString("write_job_id") ?: "",
//                     writeJobName = rs.getString("write_job_name") ?: "",
//                     dbWriter = rs.getString("db_w") ?: "",
//                     columns = columns,
//                     writerTableName = rs.getString("writer_table_name") ?: ""
//                 )
//             } catch (e: Exception) {
//                 logger.error("映射数据交互作业行数据时发生错误: rowNum=$rowNum", e)
//                 throw e
//             }
//         }
//
//         /**
//          * 从JSON字符串解析列映射配置
//          *
//          * @param columnsJson JSON字符串
//          * @return 列映射配置列表
//          */
//         private fun parseColumnsFromJson(columnsJson: String?): List<DataExchangeColumnMapping> {
//             if (columnsJson.isNullOrBlank()) {
//                 logger.warn("列映射JSON为空")
//                 return emptyList()
//             }
//
//             return try {
//                 objectMapper.readValue(
//                     columnsJson,
//                     object : TypeReference<List<DataExchangeColumnMapping>>() {}
//                 )
//             } catch (e: Exception) {
//                 logger.error("解析列映射JSON时发生错误: $columnsJson", e)
//                 emptyList()
//             }
//         }
//     }
//
// }