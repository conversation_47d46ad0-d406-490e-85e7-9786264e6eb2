package com.datayes.storage

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime

/**
 * S3配置实体 (S3 Configuration Entity)
 */
@Table("s3_config")
data class S3Config(
    @Id
    val id: Long? = null,
    
    @Column("config_name")
    val configName: String,
    
    @Column("bucket_name")
    val bucketName: String,
    
    @Column("access_key")
    val accessKey: String,
    
    @Column("secret_key")
    val secretKey: String,
    
    @Column("region")
    val region: String,
    
    @Column("endpoint_url")
    val endpointUrl: String,
    
    @Column("file_prefix")
    val filePrefix: String,
    
    @Column("is_active")
    val isActive: Boolean,
    
    @Column("created_at")
    val createdAt: LocalDateTime? = null,
    
    @Column("updated_at")
    val updatedAt: LocalDateTime? = null
)