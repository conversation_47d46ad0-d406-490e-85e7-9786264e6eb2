package com.datayes.integration

import com.datayes.auth.AuthUserRepository
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import java.util.*

/**
 * AuthUser 仓库集成测试 (AuthUser Repository Integration Test)
 *
 * 测试 AuthUserRepository 的功能。
 * 
 * 前提条件:
 * - 应用程序必须已经启动
 * - 数据库中应该存在 auth_user 表和测试数据
 * - 测试遵循只读操作原则，不修改数据库数据
 */
@SpringBootTest
@DisplayName("AuthUser 仓库集成测试")
class AuthUserRepositoryIT {

    @Autowired
    private lateinit var authUserRepository: AuthUserRepository

    @Test
    @DisplayName("应该成功查询活跃用户并返回非空结果")
    fun `should successfully query active users and return non-empty result`() {
        println("a7c9f1e2 | 开始查询活跃用户")
        
        val activeUsers = authUserRepository.findActiveUsers()
        
        println("b8d4a3f5 | 查询到活跃用户数量: ${activeUsers.size}")
        
        assertThat(activeUsers)
            .isNotEmpty()
            .allSatisfy { user ->
                assertThat(user.code).isNotBlank()
                assertThat(user.name).isNotBlank()
                assertThat(user.activeFlag).isTrue()
            }
        
        println("c6e2b9d7 | 活跃用户查询测试完成")
    }

    @Test
    @DisplayName("应该成功通过工号或姓名查询用户并返回单个结果")
    fun `should successfully query user by code or name and return single result`() {
        println("d8f3a6c4 | 开始通过工号查询用户")
        
        val usersByCode = authUserRepository.findByCodeOrName("86410954")
        
        println("e9b7d2f8 | 通过工号查询到用户数量: ${usersByCode.size}")
        
        assertThat(usersByCode)
            .hasSize(1)
            .allSatisfy { user ->
                assertThat(user.code).isNotBlank()
                assertThat(user.name).isNotBlank()
                assertThat(user.activeFlag).isNotNull()
            }
        
        println("f4c8e1a9 | 开始通过姓名查询用户")
        
        val usersByName = authUserRepository.findByCodeOrName("王素芳")
        
        println("g7d9b3e6 | 通过姓名查询到用户数量: ${usersByName.size}")
        
        assertThat(usersByName)
            .hasSize(1)
            .allSatisfy { user ->
                assertThat(user.code).isNotBlank()
                assertThat(user.name).isNotBlank()
                assertThat(user.activeFlag).isNotNull()
            }
        
        println("h2f5c8b1 | 工号或姓名查询测试完成")
    }
}