package com.datayes.lineage

import com.datayes.ApiResponse
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.tags.Tag
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*

/**
 * 血缘数据源更新控制器 (Lineage Datasource Update Controller)
 * 
 * 提供血缘数据源元数据信息更新的 REST API
 */
@Tag(name = "Lineage Datasource Update", description = "血缘数据源元数据更新接口")
@RestController
@RequestMapping("/api/lineage/datasources")
@CrossOrigin(origins = ["*"])
class LineageDatasourceUpdateController(
    private val lineageDatasourceUpdateService: LineageDatasourceUpdateService
) {
    
    private val logger = LoggerFactory.getLogger(LineageDatasourceUpdateController::class.java)
    
    /**
     * 更新所有血缘数据源的元数据信息
     * 
     * 为每个lineage_datasources表中的行查找匹配的metadata_data_source，
     * 并更新metadata_data_source_id字段
     * 
     * @return 更新结果统计信息
     */
    @Operation(
        summary = "更新所有血缘数据源的元数据信息",
        description = """
            批量更新lineage_datasources表中所有记录的metadata_data_source_id字段。
            
            该接口会：
            1. 查询所有状态为ACTIVE的血缘数据源
            2. 使用现有的匹配逻辑查找对应的元数据数据源
            3. 更新metadata_data_source_id字段
            4. 返回详细的更新统计信息
            
            匹配逻辑基于：数据库类型、主机地址、端口号、数据库名称
        """
    )
    @PostMapping("/update-metadata")
    fun updateAllDatasourcesMetadata(): ResponseEntity<ApiResponse<DatasourceUpdateResult>> {
        logger.info("k5l6m7n8 | 接收到更新所有血缘数据源元数据的请求")
        
        return try {
            val result = lineageDatasourceUpdateService.updateAllDatasourcesMetadata()
            
            logger.info("o9p0q1r2 | 血缘数据源元数据更新完成: ${result.summarize()}")
            
            ResponseEntity.ok(
                ApiResponse(
                    success = true,
                    data = result,
                    message = "血缘数据源元数据更新完成: ${result.summarize()}"
                )
            )
        } catch (e: Exception) {
            logger.error("s3t4u5v6 | 更新血缘数据源元数据失败", e)
            
            ResponseEntity.internalServerError().body(
                ApiResponse<DatasourceUpdateResult>(
                    success = false,
                    message = "更新失败: ${e.message}"
                )
            )
        }
    }
    
    /**
     * 获取更新统计信息
     * 
     * 查询当前血缘数据源表中metadata_data_source_id字段的填充情况
     * 
     * @return 统计信息
     */
    @Operation(
        summary = "获取血缘数据源元数据填充统计",
        description = """
            查询lineage_datasources表中metadata_data_source_id字段的填充情况统计。
            
            返回信息包括：
            - 总数据源数量
            - 已填充metadata_data_source_id的数量
            - 未匹配的数量
        """
    )
    @GetMapping("/metadata-stats")
    fun getDatasourceMetadataStats(): ResponseEntity<ApiResponse<DatasourceMetadataStats>> {
        logger.info("d46d8d42 | 接收到查询血缘数据源元数据统计的请求")
        
        return try {
            val stats = lineageDatasourceUpdateService.getDatasourceMetadataStats()
            
            ResponseEntity.ok(
                ApiResponse(
                    success = true,
                    data = stats,
                    message = "查询成功"
                )
            )
        } catch (e: Exception) {
            logger.error("a1b2c3d4 | 查询血缘数据源元数据统计失败", e)
            
            ResponseEntity.internalServerError().body(
                ApiResponse<DatasourceMetadataStats>(
                    success = false,
                    message = "查询失败: ${e.message}"
                )
            )
        }
    }
}