package com.datayes.util

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import java.time.Month

/**
 * CronExpressionUtil 单元测试
 */
@DisplayName("CronExpressionUtil 测试")
class CronExpressionUtilTest {

    @Test
    @DisplayName("应该正确计算每日2点执行的下次时间")
    fun `should calculate next schedule time for daily 2am execution`() {
        val cronExpression = "0 0 2 * * ?"
        val fromTime = LocalDateTime.of(2025, 6, 13, 10, 30, 0)
        
        val nextScheduleTime = CronExpressionUtil.calculateNextScheduleTime(cronExpression, fromTime)
        
        assertThat(nextScheduleTime).isNotNull
        assertThat(nextScheduleTime!!.hour).isEqualTo(2)
        assertThat(nextScheduleTime.minute).isEqualTo(0)
        assertThat(nextScheduleTime.second).isEqualTo(0)
        assertThat(nextScheduleTime.toLocalDate()).isEqualTo(fromTime.plusDays(1).toLocalDate())
    }

    @Test
    @DisplayName("应该正确计算每小时执行的下次时间")
    fun `should calculate next schedule time for hourly execution`() {
        val cronExpression = "0 0 * * * ?"
        val fromTime = LocalDateTime.of(2025, 6, 13, 10, 30, 0)
        
        val nextScheduleTime = CronExpressionUtil.calculateNextScheduleTime(cronExpression, fromTime)
        
        assertThat(nextScheduleTime).isNotNull
        assertThat(nextScheduleTime!!.hour).isEqualTo(11)
        assertThat(nextScheduleTime.minute).isEqualTo(0)
        assertThat(nextScheduleTime.second).isEqualTo(0)
        assertThat(nextScheduleTime.toLocalDate()).isEqualTo(fromTime.toLocalDate())
    }

    @Test
    @DisplayName("应该正确计算每周一9点执行的下次时间")
    fun `should calculate next schedule time for monday 9am execution`() {
        val cronExpression = "0 0 9 ? * MON"
        val fromTime = LocalDateTime.of(2025, 6, 13, 10, 0, 0) // 假设是周五
        
        val nextScheduleTime = CronExpressionUtil.calculateNextScheduleTime(cronExpression, fromTime)
        
        assertThat(nextScheduleTime).isNotNull
        assertThat(nextScheduleTime!!.hour).isEqualTo(9)
        assertThat(nextScheduleTime.minute).isEqualTo(0)
        assertThat(nextScheduleTime.dayOfWeek.value).isEqualTo(1) // 周一
    }

    @Test
    @DisplayName("应该正确计算每月1号执行的下次时间")
    fun `should calculate next schedule time for monthly first day execution`() {
        val cronExpression = "0 0 0 1 * ?"
        val fromTime = LocalDateTime.of(2025, 6, 15, 10, 0, 0)
        
        val nextScheduleTime = CronExpressionUtil.calculateNextScheduleTime(cronExpression, fromTime)
        
        assertThat(nextScheduleTime).isNotNull
        assertThat(nextScheduleTime!!.dayOfMonth).isEqualTo(1)
        assertThat(nextScheduleTime.hour).isEqualTo(0)
        assertThat(nextScheduleTime.minute).isEqualTo(0)
        assertThat(nextScheduleTime.month).isEqualTo(Month.JULY)
    }

    @Test
    @DisplayName("对于null或空字符串应该返回null")
    fun `should return null for null or empty cron expression`() {
        assertThat(CronExpressionUtil.calculateNextScheduleTime(null)).isNull()
        assertThat(CronExpressionUtil.calculateNextScheduleTime("")).isNull()
        assertThat(CronExpressionUtil.calculateNextScheduleTime("   ")).isNull()
    }

    @Test
    @DisplayName("对于无效的Cron表达式应该返回null")
    fun `should return null for invalid cron expression`() {
        val invalidCronExpressions = listOf(
            "invalid cron",
            "0 0 25 * * ?", // 无效小时
            "0 60 * * * ?", // 无效分钟
            "* * * * * * *", // 过多字段
            "0 0", // 字段不足
            "abc def ghi jkl mno pqr"
        )
        
        invalidCronExpressions.forEach { cronExpression ->
            val result = CronExpressionUtil.calculateNextScheduleTime(cronExpression)
            assertThat(result).withFailMessage("Expected null for invalid cron: ${'$'}cronExpression").isNull()
        }
    }

    @Test
    @DisplayName("应该正确验证Cron表达式的有效性")
    fun `should validate cron expression correctly`() {
        // 有效的Cron表达式
        val validExpressions = listOf(
            "0 0 2 * * ?",
            "0 0 * * * ?",
            "0 0 9 ? * MON",
            "0 0 0 1 * ?",
            "0 0/5 * * * ?",
            "0 15 10 ? * *",
            "0 50 9 ? * MON"
        )
        
        validExpressions.forEach { cronExpression ->
            assertThat(CronExpressionUtil.isValidCronExpression(cronExpression))
                .withFailMessage("Expected valid cron: $cronExpression")
                .isTrue()
        }
        
        // 无效的Cron表达式
        val invalidExpressions = listOf(
            null,
            "",
            "   ",
            "invalid cron",
            "0 0 25 * * ?",
            "0 60 * * * ?",
            "* * * * * * *",
            "0 0"
        )
        
        invalidExpressions.forEach { cronExpression ->
            assertThat(CronExpressionUtil.isValidCronExpression(cronExpression))
                .withFailMessage("Expected invalid cron: ${'$'}cronExpression")
                .isFalse()
        }
    }

    @Test
    @DisplayName("应该正确计算时间范围内的所有执行时间")
    fun `should calculate all schedule times in range`() {
        val cronExpression = "0 0 */6 * * ?" // 每6小时执行一次
        val fromTime = LocalDateTime.of(2025, 6, 13, 0, 0, 0)
        val toTime = LocalDateTime.of(2025, 6, 14, 0, 0, 0) // 24小时范围
        
        val scheduleTimes = CronExpressionUtil.calculateScheduleTimesInRange(
            cronExpression, fromTime, toTime, maxCount = 10
        )
        
        assertThat(scheduleTimes).hasSize(4) // 0点、6点、12点、18点
        assertThat(scheduleTimes[0].hour).isEqualTo(6)
        assertThat(scheduleTimes[1].hour).isEqualTo(12)
        assertThat(scheduleTimes[2].hour).isEqualTo(18)
        assertThat(scheduleTimes[3].hour).isEqualTo(0)
        assertThat(scheduleTimes[3].dayOfMonth).isEqualTo(14)
    }

    @Test
    @DisplayName("对于无效的时间范围应该返回空列表")
    fun `should return empty list for invalid time range`() {
        val cronExpression = "0 0 2 * * ?"
        val fromTime = LocalDateTime.of(2025, 6, 13, 10, 0, 0)
        val toTime = LocalDateTime.of(2025, 6, 12, 10, 0, 0) // toTime < fromTime
        
        val scheduleTimes = CronExpressionUtil.calculateScheduleTimesInRange(
            cronExpression, fromTime, toTime
        )
        
        assertThat(scheduleTimes).isEmpty()
    }

    @Test
    @DisplayName("对于无效的Cron表达式应该返回空列表")
    fun `should return empty list for invalid cron expression in range calculation`() {
        val invalidCronExpression = "invalid cron"
        val fromTime = LocalDateTime.of(2025, 6, 13, 0, 0, 0)
        val toTime = LocalDateTime.of(2025, 6, 14, 0, 0, 0)
        
        val scheduleTimes = CronExpressionUtil.calculateScheduleTimesInRange(
            invalidCronExpression, fromTime, toTime
        )
        
        assertThat(scheduleTimes).isEmpty()
    }

    @Test
    @DisplayName("应该限制返回的最大数量防止无限循环")
    fun `should limit max count to prevent infinite loop`() {
        val cronExpression = "0 0/1 * * * ?" // 每分钟执行一次
        val fromTime = LocalDateTime.of(2025, 6, 13, 0, 0, 0)
        val toTime = LocalDateTime.of(2025, 6, 14, 0, 0, 0) // 24小时范围，理论上1440次
        
        val scheduleTimes = CronExpressionUtil.calculateScheduleTimesInRange(
            cronExpression, fromTime, toTime, maxCount = 5
        )
        
        assertThat(scheduleTimes).hasSize(5) // 限制为5次
    }

    @Test
    @DisplayName("应该正确处理跨月份的Cron表达式")
    fun `should handle cron expression across months correctly`() {
        val cronExpression = "0 0 0 31 * ?" // 每月31号执行
        val fromTime = LocalDateTime.of(2025, 6, 15, 10, 0, 0) // 6月中旬
        
        val nextScheduleTime = CronExpressionUtil.calculateNextScheduleTime(cronExpression, fromTime)
        
        assertThat(nextScheduleTime).isNotNull
        assertThat(nextScheduleTime!!.month).isEqualTo(Month.JULY) // 7月31号
        assertThat(nextScheduleTime.dayOfMonth).isEqualTo(31)
    }

    @Test
    @DisplayName("应该正确处理当前时间后的最近执行时间")
    fun `should handle next execution time correctly when current time is before scheduled time`() {
        val cronExpression = "0 0 2 * * ?" // 每天2点执行
        val fromTime = LocalDateTime.of(2025, 6, 13, 1, 0, 0) // 当天1点
        
        val nextScheduleTime = CronExpressionUtil.calculateNextScheduleTime(cronExpression, fromTime)
        
        assertThat(nextScheduleTime).isNotNull
        assertThat(nextScheduleTime!!.toLocalDate()).isEqualTo(fromTime.toLocalDate()) // 同一天
        assertThat(nextScheduleTime.hour).isEqualTo(2)
    }
}