package com.datayes.hdfs

import com.datayes.lineage.JobLineageHashHistory
import com.datayes.lineage.JobProcessingHistoryRepository
import com.datayes.lineage.JobType
import com.datayes.lineage.ProcessingResult
import com.datayes.lineage.DataLineage
import com.datayes.lineage.LineageChangeDetectionService
import com.datayes.lineage.LineageRepository
import com.datayes.lineage.LineageResult
import com.datayes.lineage.DatabaseNameMatchingService
import com.datayes.lineage.DatabaseInfo
import com.datayes.sql.TableReference as SqlTableReference
import com.datayes.task.LineageTask
import com.datayes.task.LineageTaskRepository
import com.datayes.task.TaskType
import com.datayes.task.SourceType
import com.datayes.task.TaskStatus
import com.datayes.task.ScheduleType
import org.apache.hadoop.fs.Path
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.util.UUID

/**
 * HDFS Shell Script 服务层
 *
 * 提供HDFS shell脚本的发现、提取、处理和血缘分析功能
 * 类似于DataExchangeJobService，但专门处理HDFS中的shell脚本
 */
@Service
class HdfsShellScriptService(
    private val lineageRepository: LineageRepository,
    private val changeDetectionService: LineageChangeDetectionService,
    private val processingHistoryRepository: JobProcessingHistoryRepository,
    private val lineageTaskRepository: LineageTaskRepository,
    private val hdfsDatasourceMappingRepository: HdfsDatasourceMappingRepository,
    private val databaseNameMatchingService: DatabaseNameMatchingService,
    @Value("\${hdfs.tbds.username:ms_urp}") private val hdfsUsername: String,
    @Value("\${hdfs.tbds.secureid:ze2OFe78OHPrGnvc6WsdJ0Gruum1cpqLowhC}") private val hdfsSecureid: String,
    @Value("\${hdfs.tbds.securekey:aYE90rxcOX01KhXgzFegiHKRAjsmUPYy}") private val hdfsSecurekey: String
) {

    private val logger = LoggerFactory.getLogger(HdfsShellScriptService::class.java)

    /**
     * 处理指定HDFS路径下的所有shell脚本
     *
     * @param hdfsPath HDFS路径
     * @param config 处理配置
     * @return 处理结果
     */
    fun processHdfsShellScripts(
        hdfsPath: String,
        config: HdfsProcessingConfig = HdfsProcessingConfig()
    ): HdfsShellScriptProcessingResult {
        val startTime = System.currentTimeMillis()
        val errors = mutableListOf<String>()
        val warnings = mutableListOf<String>()

        return try {
            logger.info("5a3e8f2d | 开始处理HDFS路径: {}", hdfsPath)

            // 1. 连接HDFS
            val fileSystem = HdfsUtils.createHdfsConnection(hdfsUsername, hdfsSecureid, hdfsSecurekey)
            val path = Path(hdfsPath)

            // 2. 验证路径存在
            if (!HdfsUtils.pathExists(fileSystem, path)) {
                errors.add("HDFS路径不存在: $hdfsPath")
                return HdfsShellScriptProcessingResult(
                    jobs = emptyList(),
                    hdfsPath = hdfsPath,
                    errors = errors,
                    warnings = warnings
                )
            }

            // 3. 处理ZIP文件并提取shell脚本
            val zipProcessingResult = HdfsUtils.processZipFilesForShellScripts(fileSystem, path)
            logger.info(
                "b7c4e9f1 | 处理了{}个ZIP文件，发现{}个shell脚本",
                zipProcessingResult.totalZipFilesProcessed,
                zipProcessingResult.totalShellScriptsFound
            )

            // 4. 转换为HdfsShellScriptJob对象
            val jobs = mutableListOf<HdfsShellScriptJob>()

            for (zipResult in zipProcessingResult.results) {
                for (shellScript in zipResult.shellScripts) {
                    // 检查脚本大小限制
                    if (shellScript.sizeBytes > config.maxScriptSizeBytes) {
                        warnings.add("脚本文件过大，跳过处理: ${shellScript.name} (${shellScript.sizeBytes} bytes)")
                        continue
                    }

                    val job = HdfsShellScriptJob.fromShellScript(zipResult.zipFilePath, shellScript)
                    jobs.add(job)

                    logger.debug("c6d2a8e4 | 创建作业: {} from {}", job.jobId, shellScript.name)
                }
            }

            // 5. 关闭HDFS连接
            fileSystem.close()

            val processingTime = System.currentTimeMillis() - startTime
            logger.info("9e5f3b7a | HDFS处理完成，耗时{}ms，共发现{}个shell脚本作业", processingTime, jobs.size)

            HdfsShellScriptProcessingResult(
                jobs = jobs,
                hdfsPath = hdfsPath,
                errors = errors,
                warnings = warnings
            )

        } catch (e: Exception) {
            logger.error("1a8d4f6e | 处理HDFS shell脚本时发生异常", e)
            errors.add("处理异常: ${e.message}")

            HdfsShellScriptProcessingResult(
                jobs = emptyList(),
                hdfsPath = hdfsPath,
                errors = errors,
                warnings = warnings
            )
        }
    }

    /**
     * 处理单个shell脚本作业的血缘分析（带变更检测）
     *
     * @param job HDFS shell脚本作业
     * @param taskId 可选的任务ID
     * @return 血缘处理结果
     */
    fun processJobLineageWithChangeDetection(job: HdfsShellScriptJob, taskId: Long? = null): HdfsLineageProcessResult {
        val startTime = System.currentTimeMillis()
        val jobKey = job.jobId

        return try {
            logger.info("f2e7c9b3 | 开始处理shell脚本血缘: {}", jobKey)

            // 1. 转换为血缘信息
            val lineageResult = HdfsShellScriptLineageConverter.convertToLineage(
                job,
                hdfsDatasourceMappingRepository.findAll().toList(),
                this::createDatabaseInfoFromTableRef
            )

            if (!lineageResult.success) {
                return createFailureResult(job, lineageResult, startTime, "血缘转换失败")
            }

            val dataLineage = lineageResult.lineage!!

            // 2. 检测变更
            val changeDetection = changeDetectionService.detectChanges(jobKey, dataLineage)

            val processingResult = if (changeDetection.hasChanges) {
                // 3a. 有变更：更新数据库
                logger.info("e8a4f2c6 | 检测到shell脚本血缘变更，更新数据库: {}", jobKey)
                updateHdfsLineageInDatabase(jobKey, dataLineage, taskId)
                HdfsProcessingResult.UPDATED
            } else {
                // 3b. 无变更：仅更新处理时间
                logger.debug("d3b6e9f5 | 未检测到shell脚本血缘变更，跳过数据库更新: {}", jobKey)
                HdfsProcessingResult.NO_CHANGE
            }

            val processingTime = System.currentTimeMillis() - startTime

            // 4. 记录处理历史（与DataExchangeJobService保持一致）
            recordHdfsProcessingHistory(
                jobKey = jobKey,
                result = processingResult,
                lineageHash = changeDetection.currentHash,
                processingTime = processingTime
            )

            HdfsLineageProcessResult(
                job = job,
                lineageResult = lineageResult,
                processingTimeMs = processingTime,
                hasChanges = changeDetection.hasChanges,
                processingResult = processingResult
            )

        } catch (e: Exception) {
            logger.error("a7e3d8f2 | 处理shell脚本血缘时发生异常: {}", jobKey, e)
            createFailureResult(job, null, startTime, "处理异常: ${e.message}")
        }
    }

    /**
     * 批量处理多个shell脚本作业的血缘分析
     *
     * @param jobs shell脚本作业列表
     * @return 血缘处理结果列表
     */
    fun processMultipleJobsLineage(jobs: List<HdfsShellScriptJob>): List<HdfsLineageProcessResult> {
        logger.info("c5f8e2a9 | 开始批量处理{}个shell脚本作业的血缘", jobs.size)

        val results: List<HdfsLineageProcessResult> = jobs.map { job ->
            processJobLineageWithChangeDetection(job)
        }

        val successCount = results.count { it.processingResult != HdfsProcessingResult.FAILED }
        val updateCount = results.count { it.processingResult == HdfsProcessingResult.UPDATED }

        logger.info(
            "b9d4e7f3 | 批量处理完成：成功{}个，更新{}个，失败{}个",
            successCount, updateCount, results.size - successCount
        )

        return results
    }

    /**
     * 完整的HDFS路径处理工作流：
     * 1. 获取HDFS路径下所有非备份ZIP文件
     * 2. 解析每个ZIP中的shell脚本为DataLineage
     * 3. 将每个脚本映射为lineage_tasks表中的一行并保存
     * 4. 保存DataLineage血缘数据
     *
     * @param hdfsPath HDFS路径
     * @param config 处理配置
     * @param taskCreatedBy 任务创建者
     * @param batchId 批处理ID
     * @return 完整的处理结果
     */
    fun processHdfsPathAndSaveLineageTasks(
        hdfsPath: String,
        config: HdfsProcessingConfig = HdfsProcessingConfig(),
        taskCreatedBy: String? = null,
        batchId: String? = null,
        executionLogService: com.datayes.task.ExecutionLogService? = null
    ): HdfsFullProcessingResponse {
        val startTime = System.currentTimeMillis()
        val actualBatchId = batchId ?: "hdfs_batch_${UUID.randomUUID().toString().take(8)}"

        logger.info("e7f2a9c4 | 开始完整HDFS处理工作流: path={}, batchId={}", hdfsPath, actualBatchId)

        return try {
            // 1. 处理HDFS shell脚本
            val hdfsProcessingResult = processHdfsShellScripts(hdfsPath, config)

            if (hdfsProcessingResult.errors.isNotEmpty()) {
                return createErrorResponse(hdfsPath, hdfsProcessingResult.errors, startTime)
            }

            val jobs = hdfsProcessingResult.jobs
            logger.info("b3c8e5a7 | 发现{}个shell脚本作业", jobs.size)

            // 2. 为每个脚本创建或更新LineageTask
            val lineageTaskResults = jobs.map { job ->
                createOrUpdateLineageTask(job, taskCreatedBy, actualBatchId)
            }

            // 3. 处理血缘分析（只对成功创建LineageTask的作业）
            val successfulTasks = lineageTaskResults.filter { it.success }
            val lineageResults = successfulTasks.map { taskResult ->
                val executionId = UUID.randomUUID().toString()
                val taskId = taskResult.lineageTask!!.id
                val scriptJob = taskResult.scriptJob
                
                // 立即记录任务执行开始日志
                executionLogService?.let { logService ->
                    try {
                        logService.logTaskExecutionStart(taskId, executionId, scriptJob)
                        logger.info("a7c3b9e5 | 记录任务执行开始: taskId={}, executionId={}", taskId, executionId)
                    } catch (e: Exception) {
                        logger.error("8f2d4c6a | 记录任务执行开始失败: taskId={}", taskId, e)
                    }
                }
                
                val lineageProcessingStart = System.currentTimeMillis()
                val lineageResult = processJobLineageWithTask(scriptJob, taskId)
                val lineageProcessingTime = System.currentTimeMillis() - lineageProcessingStart

                // 4. 更新LineageTask状态
                updateLineageTaskStatus(taskId, lineageResult)
                
                // 立即记录任务执行完成日志
                executionLogService?.let { logService ->
                    try {
                        val taskStatus = if (lineageResult.processingResult != HdfsProcessingResult.FAILED) {
                            TaskStatus.SUCCESS
                        } else {
                            TaskStatus.FAILED
                        }
                        
                        val additionalInfo = mapOf(
                            "task_type" to "BIG_DATA_PLATFORM",
                            "executed_by" to (taskCreatedBy ?: "system"),
                            "has_changes" to lineageResult.hasChanges,
                            "processing_result" to lineageResult.processingResult.name,
                            "script_name" to scriptJob.scriptName,
                            "zip_file_path" to scriptJob.zipFilePath
                        )
                        
                        logService.logTaskExecutionCompletion(
                            executionId = executionId,
                            taskStatus = taskStatus,
                            processingTimeMs = lineageProcessingTime,
                            errorMessage = if (lineageResult.processingResult == HdfsProcessingResult.FAILED)
                                lineageResult.lineageResult.errors.joinToString("; ") else null,
                            additionalInfo = additionalInfo
                        )
                        
                        logger.info("c5e8f2a9 | 记录任务执行完成: taskId={}, executionId={}, status={}", 
                                   taskId, executionId, taskStatus)
                    } catch (e: Exception) {
                        logger.error("3b6d9f1c | 记录任务执行完成失败: taskId={}", taskId, e)
                    }
                }

                lineageResult
            }

            // 5. 处理失败的LineageTask创建
            val failedTasks = lineageTaskResults.filter { !it.success }
            failedTasks.forEach { taskResult ->
                if (taskResult.lineageTask != null) {
                    val executionId = UUID.randomUUID().toString()
                    val taskId = taskResult.lineageTask!!.id
                    val scriptJob = taskResult.scriptJob
                    
                    // 为失败的任务记录执行日志
                    executionLogService?.let { logService ->
                        try {
                            logService.logTaskExecutionStart(taskId, executionId, scriptJob)
                            logService.logTaskExecutionCompletion(
                                executionId = executionId,
                                taskStatus = TaskStatus.FAILED,
                                processingTimeMs = 0L,
                                errorMessage = taskResult.errorMessage,
                                additionalInfo = mapOf(
                                    "task_type" to "BIG_DATA_PLATFORM",
                                    "executed_by" to (taskCreatedBy ?: "system"),
                                    "has_changes" to false,
                                    "processing_result" to "FAILED",
                                    "script_name" to scriptJob.scriptName,
                                    "zip_file_path" to scriptJob.zipFilePath,
                                    "failure_reason" to "Task creation failed"
                                )
                            )
                            logger.info("e9a4c7b6 | 记录失败任务执行日志: taskId={}, executionId={}", taskId, executionId)
                        } catch (e: Exception) {
                            logger.error("f2d8a5c3 | 记录失败任务执行日志失败: taskId={}", taskId, e)
                        }
                    }
                    
                    // 如果任务创建了但标记为失败，更新状态为FAILED
                    updateFailedLineageTaskStatus(taskId, taskResult.errorMessage)
                }
            }

            val processingTime = System.currentTimeMillis() - startTime

            // 4. 构建响应
            val summary = HdfsFullProcessingSummary(
                totalScriptsProcessed = jobs.size,
                lineageTasksCreated = lineageTaskResults.count { it.isNewTask && it.success },
                lineageTasksUpdated = lineageTaskResults.count { !it.isNewTask && it.success },
                lineageDataUpdated = lineageResults.count { it.processingResult == HdfsProcessingResult.UPDATED },
                errorCount = lineageTaskResults.count { !it.success } + lineageResults.count { it.processingResult == HdfsProcessingResult.FAILED },
                processingTimeMs = processingTime
            )

            logger.info(
                "a9d6f4c2 | HDFS完整处理工作流完成: 脚本{}个，任务创建{}个，任务更新{}个，血缘更新{}个，耗时{}ms",
                summary.totalScriptsProcessed,
                summary.lineageTasksCreated,
                summary.lineageTasksUpdated,
                summary.lineageDataUpdated,
                processingTime
            )

            HdfsFullProcessingResponse(
                success = summary.errorCount == 0,
                message = if (summary.errorCount == 0) {
                    "成功处理${summary.totalScriptsProcessed}个脚本，创建${summary.lineageTasksCreated}个任务，更新${summary.lineageTasksUpdated}个任务"
                } else {
                    "处理完成但有${summary.errorCount}个错误"
                },
                hdfsPath = hdfsPath,
                taskCreationResults = lineageTaskResults,
                lineageProcessingResults = lineageResults,
                summary = summary
            )

        } catch (e: Exception) {
            logger.error("f5c2e8a3 | HDFS完整处理工作流发生异常", e)
            createErrorResponse(hdfsPath, listOf("系统异常: ${e.message}"), startTime)
        }
    }

    /**
     * 更新LineageTask状态基于血缘处理结果 (Update LineageTask status based on lineage processing result)
     */
    private fun updateLineageTaskStatus(taskId: Long, lineageResult: HdfsLineageProcessResult) {
        try {
            val task = lineageTaskRepository.findById(taskId).orElse(null)
            if (task == null) {
                logger.warn("6e3f8a2d | 无法找到LineageTask进行状态更新: taskId=$taskId")
                return
            }

            val newStatus = when (lineageResult.processingResult) {
                HdfsProcessingResult.UPDATED, HdfsProcessingResult.NO_CHANGE -> TaskStatus.SUCCESS
                HdfsProcessingResult.FAILED -> TaskStatus.FAILED
            }

            val errorMessage = if (lineageResult.processingResult == HdfsProcessingResult.FAILED) {
                if (!lineageResult.lineageResult.success) {
                    lineageResult.lineageResult.errors.joinToString("; ")
                } else {
                    "血缘处理失败"
                }
            } else null

            val updatedTask = task.copy(
                taskStatus = newStatus,
                executedAt = task.executedAt ?: LocalDateTime.now(),
                completedAt = LocalDateTime.now(),
                processingTimeMs = lineageResult.processingTimeMs,
                errorMessage = errorMessage,
                hasChanges = lineageResult.hasChanges
            )

            lineageTaskRepository.save(updatedTask)

            logger.info("8f2a5c9e | LineageTask状态已更新: taskId=$taskId, status=$newStatus, hasChanges=${lineageResult.hasChanges}")

        } catch (e: Exception) {
            logger.error("9d4b7e3f | 更新LineageTask状态时发生异常: taskId=$taskId", e)
        }
    }

    /**
     * 更新失败的LineageTask状态 (Update failed LineageTask status)
     */
    private fun updateFailedLineageTaskStatus(taskId: Long, errorMessage: String?) {
        try {
            val task = lineageTaskRepository.findById(taskId).orElse(null)
            if (task == null) {
                logger.warn("4c7a9e2f | 无法找到LineageTask进行失败状态更新: taskId=$taskId")
                return
            }

            val updatedTask = task.copy(
                taskStatus = TaskStatus.FAILED,
                executedAt = if (task.executedAt == null) LocalDateTime.now() else task.executedAt,
                completedAt = LocalDateTime.now(),
                errorMessage = errorMessage ?: "LineageTask创建或处理失败",
                hasChanges = false
            )

            lineageTaskRepository.save(updatedTask)

            logger.debug("2b9e6f3a | LineageTask失败状态已更新: taskId=$taskId, error=$errorMessage")

        } catch (e: Exception) {
            logger.error("5d8c4e7b | 更新失败LineageTask状态时发生异常: taskId=$taskId", e)
        }
    }

    /**
     * 查询指定HDFS目录下的所有ZIP文件
     *
     * @param hdfsPath HDFS目录路径
     * @param includeBackups 是否包含备份文件（带时间戳的文件）
     * @return ZIP文件查询结果
     */
    fun queryZipFiles(hdfsPath: String, includeBackups: Boolean = false): HdfsZipFileQueryResult {
        val startTime = System.currentTimeMillis()

        return try {
            logger.info("e4a7b8f2 | 开始查询HDFS ZIP文件: {}", hdfsPath)

            // 1. 连接HDFS
            val fileSystem = HdfsUtils.createHdfsConnection(hdfsUsername, hdfsSecureid, hdfsSecurekey)
            val path = Path(hdfsPath)

            // 2. 验证路径存在
            if (!HdfsUtils.pathExists(fileSystem, path)) {
                return HdfsZipFileQueryResult(
                    success = false,
                    message = "HDFS路径不存在: $hdfsPath",
                    hdfsPath = hdfsPath,
                    zipFiles = emptyList(),
                    queryTimeMs = System.currentTimeMillis() - startTime
                )
            }

            // 3. 查找所有ZIP文件
            val allZipFiles = HdfsUtils.findZipFiles(fileSystem, path)
            logger.debug("f2c6e9a4 | 发现{}个ZIP文件", allZipFiles.size)

            // 4. 根据配置过滤备份文件
            val filteredZipFiles = if (includeBackups) {
                allZipFiles
            } else {
                HdfsUtils.filterBackupZipFiles(allZipFiles)
            }

            // 5. 转换为详细信息
            val zipFileInfos = filteredZipFiles.map { zipFilePath ->
                val fileName = zipFilePath.substringAfterLast("/")
                val isBackup = isBackupFile(fileName)

                HdfsZipFileInfo(
                    filePath = zipFilePath,
                    fileName = fileName,
                    isBackup = isBackup
                )
            }

            // 6. 关闭HDFS连接
            fileSystem.close()

            val queryTime = System.currentTimeMillis() - startTime
            logger.info(
                "9c3a5e7b | ZIP文件查询完成，耗时{}ms，找到{}个文件（总共{}个）",
                queryTime, filteredZipFiles.size, allZipFiles.size
            )

            HdfsZipFileQueryResult(
                success = true,
                message = "成功查询到${filteredZipFiles.size}个ZIP文件",
                hdfsPath = hdfsPath,
                zipFiles = zipFileInfos,
                totalFound = allZipFiles.size,
                totalAfterFilter = filteredZipFiles.size,
                queryTimeMs = queryTime
            )

        } catch (e: Exception) {
            logger.error("b6d8f4a9 | 查询HDFS ZIP文件时发生异常", e)

            HdfsZipFileQueryResult(
                success = false,
                message = "查询失败: ${e.message}",
                hdfsPath = hdfsPath,
                zipFiles = emptyList(),
                queryTimeMs = System.currentTimeMillis() - startTime
            )
        }
    }

    /**
     * 获取活动的shell脚本作业（模拟数据库查询）
     * 注意：这里是占位符实现，实际应该连接数据库
     */
    fun getActiveShellScriptJobs(): List<HdfsShellScriptJob> {
        // TODO: 实现从数据库查询活动的shell脚本作业
        logger.warn("6e2a9f4c | getActiveShellScriptJobs() 尚未实现数据库查询")
        return emptyList()
    }

    /**
     * 为HdfsShellScriptJob创建或更新LineageTask
     *
     * @param job HDFS shell脚本作业
     * @param createdBy 任务创建者
     * @param batchId 批处理ID
     * @return LineageTask创建/更新结果
     */
    private fun createOrUpdateLineageTask(
        job: HdfsShellScriptJob,
        createdBy: String?,
        batchId: String
    ): HdfsLineageTaskResult {
        return try {
            logger.debug("c7e4f9a2 | 开始为脚本{}创建或更新LineageTask", job.jobId)

            // 检查是否已存在同样的任务
            val existingTask = lineageTaskRepository.findByJobKey(job.jobId)

            val lineageTask = if (existingTask != null) {
                // 更新现有任务
                logger.debug("d8f5e3a7 | 发现现有LineageTask，更新: {}", existingTask.id)
                updateExistingLineageTask(existingTask, job, batchId)
            } else {
                // 创建新任务
                logger.debug("a4c7e2f9 | 创建新的LineageTask for {}", job.jobId)
                createNewLineageTask(job, createdBy, batchId)
            }

            HdfsLineageTaskResult(
                scriptJob = job,
                lineageTask = lineageTask,
                isNewTask = existingTask == null,
                success = true
            )

        } catch (e: Exception) {
            logger.error("b6f9c4e3 | 创建或更新LineageTask失败: {}", job.jobId, e)
            HdfsLineageTaskResult(
                scriptJob = job,
                lineageTask = null,
                isNewTask = false,
                success = false,
                errorMessage = "创建任务失败: ${e.message}"
            )
        }
    }

    /**
     * 创建新的LineageTask
     */
    private fun createNewLineageTask(
        job: HdfsShellScriptJob,
        createdBy: String?,
        batchId: String
    ): LineageTask {
        val taskName = "HDFS脚本任务-${job.jobName}"

        val newTask = LineageTask(
            taskName = taskName,
            taskType = TaskType.BIG_DATA_PLATFORM,
            sourceType = SourceType.SCRIPT_ANALYSIS,
            sourceIdentifier = job.jobId,
            sourceContent = job.scriptContent,
            taskStatus = TaskStatus.PENDING,
            scheduleType = ScheduleType.MANUAL,
            isEnabled = true,
            createdBy = createdBy,
            jobKey = job.jobId,
            batchId = batchId,
            executionCount = 1,
            createdAt = LocalDateTime.now(),
            updatedAt = LocalDateTime.now()
        )

        return lineageTaskRepository.save(newTask)
    }

    /**
     * 更新现有的LineageTask
     */
    private fun updateExistingLineageTask(
        existingTask: LineageTask,
        job: HdfsShellScriptJob,
        batchId: String
    ): LineageTask {
        val updatedTask = existingTask.copy(
            sourceContent = job.scriptContent, // 更新脚本内容
            batchId = batchId,
            executionCount = existingTask.executionCount + 1,
            taskStatus = TaskStatus.PENDING, // 重置状态为PENDING，等待血缘处理完成
            executedAt = null, // 重置执行时间
            completedAt = null, // 重置完成时间
            errorMessage = null, // 清空错误信息
            hasChanges = null, // 重置变更标识
            updatedAt = LocalDateTime.now()
        )

        return lineageTaskRepository.save(updatedTask)
    }

    /**
     * 处理作业血缘分析（带LineageTask ID）
     */
    private fun processJobLineageWithTask(
        job: HdfsShellScriptJob,
        taskId: Long
    ): HdfsLineageProcessResult {
        return processJobLineageWithChangeDetection(job, taskId)
    }

    /**
     * 创建错误响应
     */
    private fun createErrorResponse(
        hdfsPath: String,
        errors: List<String>,
        startTime: Long
    ): HdfsFullProcessingResponse {
        return HdfsFullProcessingResponse(
            success = false,
            message = "处理失败: ${errors.firstOrNull() ?: "未知错误"}",
            hdfsPath = hdfsPath,
            taskCreationResults = emptyList(),
            lineageProcessingResults = emptyList(),
            summary = HdfsFullProcessingSummary(
                errorCount = errors.size,
                processingTimeMs = System.currentTimeMillis() - startTime
            )
        )
    }

    /**
     * 更新HDFS shell脚本血缘信息到数据库
     * 临时实现，直接调用现有的血缘存储逻辑
     */
    private fun updateHdfsLineageInDatabase(
        jobKey: String,
        dataLineage: DataLineage,
        taskId: Long?
    ) {
        try {
            // 1. 标记旧的血缘关系为非活跃
            lineageRepository.deactivateLineageByJobKey(jobKey)

            // 2. 保存新的血缘信息
            val relationshipIds = lineageRepository.saveDataLineage(dataLineage, taskId, jobKey)

            // 3. 更新引用计数
            lineageRepository.updateReferenceCounts(dataLineage)

            logger.debug("f8e4c2a9 | 成功更新HDFS血缘到数据库，关系ID: {}", relationshipIds)

        } catch (e: Exception) {
            logger.error("a3f7e5b8 | 更新HDFS血缘到数据库失败: {}", jobKey, e)
            throw e
        }
    }

    /**
     * 记录HDFS shell脚本处理历史
     * 使用JobProcessingHistory表存储HDFS作业的处理记录
     */
    private fun recordHdfsProcessingHistory(
        jobKey: String,
        result: HdfsProcessingResult,
        lineageHash: String,
        processingTime: Long
    ) {
        try {
            // 将HDFS处理结果转换为标准格式
            val processingResult = when (result) {
                HdfsProcessingResult.UPDATED -> ProcessingResult.UPDATED
                HdfsProcessingResult.NO_CHANGE -> ProcessingResult.NO_CHANGE
                HdfsProcessingResult.FAILED -> ProcessingResult.FAILED
            }

            val history = JobLineageHashHistory(
                jobKey = jobKey,
                jobType = JobType.HDFS_SHELL_SCRIPT,
                readerJobId = null, // HDFS作业不使用这些字段
                writeJobId = null,  // HDFS作业不使用这些字段
                processingResult = processingResult,
                changesDetected = result == HdfsProcessingResult.UPDATED,
                processingDurationMs = processingTime,
                lineageHash = lineageHash
            )

            processingHistoryRepository.save(history)
            logger.debug("a5e8f3c2 | 成功记录HDFS处理历史: {}", jobKey)

        } catch (e: Exception) {
            logger.error("d7c4f9e6 | 记录HDFS处理历史失败: {}", jobKey, e)
            // 不抛出异常，避免影响主流程
        }
    }

    private fun createFailureResult(
        job: HdfsShellScriptJob,
        lineageResult: LineageResult?,
        startTime: Long,
        errorMessage: String
    ): HdfsLineageProcessResult {
        val failureResult = lineageResult ?: LineageResult(
            lineage = null,
            warnings = emptyList(),
            errors = listOf(errorMessage),
            success = false
        )

        return HdfsLineageProcessResult(
            job = job,
            lineageResult = failureResult,
            processingTimeMs = System.currentTimeMillis() - startTime,
            hasChanges = false,
            processingResult = HdfsProcessingResult.FAILED
        )
    }

    /**
     * 检查文件名是否为备份文件（包含时间戳）
     */
    private fun isBackupFile(fileName: String): Boolean {
        val lowerFileName = fileName.lowercase()
        val backupPatterns = listOf(
            Regex(".*\\.\\d{14}\\.zip$"),  // filename.YYYYMMDDHHMMSS.zip
            Regex(".*\\d{14}\\.zip$")     // filenameYYYYMMDDHHMMSS.zip
        )
        return backupPatterns.any { pattern -> pattern.matches(lowerFileName) }
    }
    
    /**
     * 创建数据库信息从表引用 (Create DatabaseInfo from table reference)
     * 
     * 使用DatabaseNameMatchingService查找或创建对应的血缘数据源，并返回完整的数据库信息
     * 
     * @param tableRef SQL表引用
     * @param warnings 警告信息收集器
     * @return 数据库信息
     * @throws IllegalArgumentException 如果数据库名称为null或找不到匹配的元数据数据源
     */
    fun createDatabaseInfoFromTableRef(
        tableRef: SqlTableReference,
        warnings: MutableList<String>
    ): DatabaseInfo {
        val databaseName = tableRef.schemaOrDatabase 
            ?: throw IllegalArgumentException("database name is null for table ${tableRef.name}")
        
        logger.info("create_db_info | 开始为表引用创建数据库信息: databaseName=$databaseName, tableName=${tableRef.name}")
        
        return try {
            // 使用DatabaseNameMatchingService获取完整的数据库信息
            val databaseInfo = databaseNameMatchingService.findOrCreateDatabaseInfoByDatabaseName(databaseName)
            
            logger.info("create_db_info_success | 成功创建数据库信息: databaseName=$databaseName, dbType=${databaseInfo.dbType}, host=${databaseInfo.host}")
            
            databaseInfo
        } catch (e: Exception) {
            val errorMsg = "使用DatabaseNameMatchingService创建数据库信息失败: ${e.message}"
            warnings.add("create_db_info_error | $errorMsg")
            logger.error("create_db_info_error | $errorMsg", e)
            
            // 重新抛出异常，让调用方处理
            throw IllegalArgumentException("无法为数据库 '$databaseName' 创建数据库信息: ${e.message}", e)
        }
    }
}

/**
 * HDFS处理结果枚举
 */
enum class HdfsProcessingResult {
    NO_CHANGE,  // 无变更
    UPDATED,    // 已更新
    FAILED      // 处理失败
}

/**
 * HDFS血缘处理结果
 */
data class HdfsLineageProcessResult(
    val job: HdfsShellScriptJob,
    val lineageResult: LineageResult,
    val processingTimeMs: Long,
    val hasChanges: Boolean = false,
    val processingResult: HdfsProcessingResult = HdfsProcessingResult.NO_CHANGE
)

/**
 * HDFS ZIP文件信息
 */
data class HdfsZipFileInfo(
    val filePath: String,
    val fileName: String,
    val isBackup: Boolean = false
)

/**
 * HDFS ZIP文件查询结果
 */
data class HdfsZipFileQueryResult(
    val success: Boolean,
    val message: String,
    val hdfsPath: String,
    val zipFiles: List<HdfsZipFileInfo>,
    val totalFound: Int = zipFiles.size,
    val totalAfterFilter: Int = zipFiles.size,
    val queryTimeMs: Long = 0
)

