package com.datayes.dataexchange

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table

/**
 * 数据交互作业实体 (Data Exchange Job Entity)
 *
 * 对应 data_exchange_jobs 表的实体类，用于持久化数据交互作业信息
 */
@Table("data_exchange_jobs")
data class DataExchangeJobEntity(
    @Id
    val id: Long? = null,
    
    @Column("reader_job_id")
    val readerJobId: String,
    
    @Column("reader_job_name")
    val readerJobName: String,
    
    @Column("db_read")
    val dbRead: String,
    
    @Column("reader_table_name")
    val readerTableName: String?,
    
    @Column("reader_sql")
    val readerSql: String?,
    
    @Column("write_job_id")
    val writeJobId: String,
    
    @Column("write_job_name")
    val writeJobName: String,
    
    @Column("db_w")
    val dbW: String,
    
    @Column("columns")
    val columns: String?, // JSON string representation
    
    @Column("writer_table_name")
    val writerTableName: String
)

/**
 * 将 DataExchangeJob 转换为 DataExchangeJobEntity
 */
fun DataExchangeJob.toEntity(): DataExchangeJobEntity {
    return DataExchangeJobEntity(
        readerJobId = this.readerJobId,
        readerJobName = this.readerJobName,
        dbRead = this.dbReader,
        readerTableName = this.readerTableName,
        readerSql = this.readerSql,
        writeJobId = this.writeJobId,
        writeJobName = this.writeJobName,
        dbW = this.dbWriter,
        columns = if (this.columns.isNotEmpty()) {
            // Convert columns to JSON string
            com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(this.columns)
        } else null,
        writerTableName = this.writerTableName
    )
}