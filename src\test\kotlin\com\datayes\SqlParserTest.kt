package com.datayes

import com.datayes.sql.SqlParser
import com.datayes.sql.SqlParsingException
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.DisplayName

class SqlParserTest {

    @Test
    @DisplayName("Should parse a simple SELECT query correctly")
    fun testParseSimpleSelectQuery() {
        // Given
        val sqlQuery = """
            SELECT id, name, age FROM users
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables
        assertThat(result.tables).hasSize(1)
        assertThat(result.tables[0].name).isEqualTo("users")
        assertThat(result.tables[0].schemaOrDatabase).isNull()
        assertThat(result.tables[0].alias).isNull()

        // Verify columns
        assertThat(result.columns).hasSize(3)
        assertThat(result.columns).extracting("name").containsExactly("id", "name", "age")
        assertThat(result.columns).extracting("tablePrefix").containsOnlyNulls()
        assertThat(result.columns).extracting("alias").containsOnlyNulls()
        assertThat(result.columns).extracting("isWildcard").containsOnly(false)
    }

    @Test
    @DisplayName("Should parse a query with schema, table alias and column alias")
    fun testParseQueryWithSchemaAndAliases() {
        // Given
        val sqlQuery = """
            SELECT u.id AS user_id, u.name AS user_name 
            FROM schema1.users u
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables
        assertThat(result.tables).hasSize(1)
        assertThat(result.tables[0].name).isEqualTo("users")
        assertThat(result.tables[0].schemaOrDatabase).isEqualTo("schema1")
        assertThat(result.tables[0].alias).isEqualTo("u")

        // Verify columns
        assertThat(result.columns).hasSize(2)

        // First column
        assertThat(result.columns[0].name).isEqualTo("id")
        assertThat(result.columns[0].tablePrefix).isEqualTo("u")
        assertThat(result.columns[0].alias).isEqualTo("user_id")
        assertThat(result.columns[0].originalExpression).isEqualTo("u.id")
        assertThat(result.columns[0].isWildcard).isFalse()

        // Second column
        assertThat(result.columns[1].name).isEqualTo("name")
        assertThat(result.columns[1].tablePrefix).isEqualTo("u")
        assertThat(result.columns[1].alias).isEqualTo("user_name")
        assertThat(result.columns[1].originalExpression).isEqualTo("u.name")
        assertThat(result.columns[1].isWildcard).isFalse()
    }

    @Test
    @DisplayName("Should parse a query with JOIN clauses")
    fun testParseQueryWithJoins() {
        // Given
        val sqlQuery = """
            SELECT u.id, o.order_id, o.total
            FROM users u
            JOIN orders o ON u.id = o.user_id
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables
        assertThat(result.tables).hasSize(2)

        // First table
        assertThat(result.tables[0].name).isEqualTo("users")
        assertThat(result.tables[0].alias).isEqualTo("u")

        // Second table
        assertThat(result.tables[1].name).isEqualTo("orders")
        assertThat(result.tables[1].alias).isEqualTo("o")

        // Verify columns
        assertThat(result.columns).hasSize(3)
        assertThat(result.columns).extracting("name").containsExactly("id", "order_id", "total")
        assertThat(result.columns).extracting("tablePrefix").containsExactly("u", "o", "o")
    }

    @Test
    @DisplayName("Should parse a query with wildcard columns")
    fun testParseQueryWithWildcards() {
        // Given
        val sqlQuery = """
            SELECT * FROM users
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables
        assertThat(result.tables).hasSize(1)
        assertThat(result.tables[0].name).isEqualTo("users")

        // Verify columns
        assertThat(result.columns).hasSize(1)
        assertThat(result.columns[0].name).isEqualTo("*")
        assertThat(result.columns[0].isWildcard).isTrue()
        assertThat(result.columns[0].tablePrefix).isNull()
    }

    @Test
    @DisplayName("Should parse a query with table-qualified wildcard columns")
    fun testParseQueryWithTableWildcards() {
        // Given
        val sqlQuery = """
            SELECT u.*, o.order_id
            FROM users u
            JOIN orders o ON u.id = o.user_id
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables
        assertThat(result.tables).hasSize(2)

        // Verify columns
        assertThat(result.columns).hasSize(2)

        // First column (wildcard)
        assertThat(result.columns[0].name).isEqualTo("*")
        assertThat(result.columns[0].tablePrefix).isEqualTo("u")
        assertThat(result.columns[0].isWildcard).isTrue()
        assertThat(result.columns[0].originalExpression).isEqualTo("u.*")

        // Second column
        assertThat(result.columns[1].name).isEqualTo("order_id")
        assertThat(result.columns[1].tablePrefix).isEqualTo("o")
        assertThat(result.columns[1].isWildcard).isFalse()
    }

    @Test
    @DisplayName("Should parse a query with subquery")
    fun testParseQueryWithSubquery() {
        // Given
        val sqlQuery = """
            SELECT u.id, s.total
            FROM users u
            JOIN (SELECT user_id, SUM(amount) as total FROM orders GROUP BY user_id) s
            ON u.id = s.user_id
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables - should extract tables from subqueries in JOIN clauses
        assertThat(result.tables).hasSize(2)
        assertThat(result.tables).extracting("name").containsExactlyInAnyOrder("users", "orders")
        assertThat(result.tables).filteredOn { it.name == "users" }
            .extracting("alias")
            .contains("u")

        // Verify columns from the main query
        assertThat(result.columns).hasSize(2)
        assertThat(result.columns).extracting("name").containsExactly("id", "total")
    }

    @Test
    @DisplayName("Should throw SqlParsingException for invalid SQL")
    fun testParseInvalidSql() {
        // Given
        val invalidSql = "SELECT * FREM users" // Intentional typo in FROM

        // When/Then
        assertThatThrownBy { SqlParser.parse(invalidSql) }
            .isInstanceOf(SqlParsingException::class.java)
            .hasMessageContaining("SQL parsing error")
    }

    @Test
    @DisplayName("Should parse the example SQL from the main method")
    fun testParseExampleSql() {
        // Given - using the example from the main method
        val sqlQuery = """
            SELECT a.id, b.name AS user_name, COUNT(*) as total_count
            FROM schema1.users a
            JOIN orders b ON a.id = b.user_id
            LEFT JOIN (SELECT * FROM products WHERE active = 1) p ON b.product_id = p.id
            WHERE a.status = 'active'
            AND b.created_at > (SELECT MAX(created_at) FROM login_history WHERE user_id = a.id)
            GROUP BY a.id, b.name
            ORDER BY total_count DESC
            LIMIT 10
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables - should extract tables from main query and subqueries
        assertThat(result.tables).hasSize(3)
        assertThat(result.tables).extracting("name").containsExactlyInAnyOrder("users", "orders", "products")

        // Verify schema for users table
        assertThat(result.tables).filteredOn { it.name == "users" }
            .extracting("schemaOrDatabase")
            .contains("schema1")

        // Verify aliases
        assertThat(result.tables).filteredOn { it.name == "users" }
            .extracting("alias")
            .contains("a")

        assertThat(result.tables).filteredOn { it.name == "orders" }
            .extracting("alias")
            .contains("b")

        // Verify columns
        assertThat(result.columns).hasSize(3)

        // First column
        assertThat(result.columns[0].name).isEqualTo("id")
        assertThat(result.columns[0].tablePrefix).isEqualTo("a")

        // Second column
        assertThat(result.columns[1].name).isEqualTo("name")
        assertThat(result.columns[1].tablePrefix).isEqualTo("b")
        assertThat(result.columns[1].alias).isEqualTo("user_name")

        // Third column (aggregate function)
        assertThat(result.columns[2].name).isEqualTo("COUNT(*)")
        assertThat(result.columns[2].alias).isEqualTo("total_count")
    }

    @Test
    @DisplayName("Should parse INSERT OVERWRITE with UNION ALL query")
    fun testParseInsertOverwriteWithUnionAll() {
        // Given
        val sqlQuery = """
            insert overwrite table urp_dws.full_lcget
            select GrpAppNo,
                        GrpPolicyNo,
                        GrpProductNo,
                        AppNo,
                        PolicyNo,
                        ProductNo,
                        LiabilityCode,
                        GetLiabilityCode,
                        PayNo,
                        StatusCgDate,
                        ReportNo,
                        ClmRegisterNo,
                        ClaimNo,
                        CaseNo,
                        EdorAcceptNo,
                        EdorNo,
                        EdorType,
                        DivYear,
                        TempfeeNo,
                        ActuGetNo,
                        CompanyCode,
                        LiabilityName,
                        GPFlag,
                        GetLiabilityName,
                        InitialPremium,
                        CurrentPremium,
                        AccumPremium,
                        InitialAmnt,
                        EffectiveAmnt,
                        RiskAmnt,
                        AllowanceType,
                        DailyAllowanceAmnt,
                        effdate,
                        invaliddate,
                        Status,
                        WaitingPeriod,
                        DeductibleType,
                        Deductible,
                        ClaimRatio,
                        ManageCom,
                        insuredgroupcode,
                        groupname,
                        SystemCode,
                        ListImportDate,
                        ClaimBackDate,
                        MakeDate,
                        MakeTime,
                        ModifyDate,
                        ModifyTime,
                   last_dw_opt_date
              from (select GrpAppNo,
                        GrpPolicyNo,
                        GrpProductNo,
                        AppNo,
                        PolicyNo,
                        ProductNo,
                        LiabilityCode,
                        GetLiabilityCode,
                        PayNo,
                        StatusCgDate,
                        ReportNo,
                        ClmRegisterNo,
                        ClaimNo,
                        CaseNo,
                        EdorAcceptNo,
                        EdorNo,
                        EdorType,
                        DivYear,
                        TempfeeNo,
                        ActuGetNo,
                        CompanyCode,
                        LiabilityName,
                        GPFlag,
                        GetLiabilityName,
                        InitialPremium,
                        CurrentPremium,
                        AccumPremium,
                        InitialAmnt,
                        EffectiveAmnt,
                        RiskAmnt,
                        AllowanceType,
                        DailyAllowanceAmnt,
                        effdate,
                        invaliddate,
                        Status,
                        WaitingPeriod,
                        DeductibleType,
                        Deductible,
                        ClaimRatio,
                        ManageCom,
                        insuredgroupcode,
                        groupname,
                        SystemCode,
                        ListImportDate,
                        ClaimBackDate,
                        MakeDate,
                        MakeTime,
                        ModifyDate,
                        ModifyTime,
                           last_dw_opt_date
                      from urp_dws.pre_full_lcget f
                     where not exists (select 1 
                                         from urp_dws.pre_increment_lcget i 
                                        where i.ProductNo = f.ProductNo
                                        and i.LiabilityCode =f.LiabilityCode
                                        and i.GetLiabilityCode =f.GetLiabilityCode and nvl(i.rdbms_opt_type,'') <> 'DELETE')
                    union all 
                    select GrpAppNo,
                        GrpPolicyNo,
                        GrpProductNo,
                        AppNo,
                        PolicyNo,
                        ProductNo,
                        LiabilityCode,
                        GetLiabilityCode,
                        PayNo,
                        StatusCgDate,
                        ReportNo,
                        ClmRegisterNo,
                        ClaimNo,
                        CaseNo,
                        EdorAcceptNo,
                        EdorNo,
                        EdorType,
                        DivYear,
                        TempfeeNo,
                        ActuGetNo,
                        CompanyCode,
                        LiabilityName,
                        GPFlag,
                        GetLiabilityName,
                        InitialPremium,
                        CurrentPremium,
                        AccumPremium,
                        InitialAmnt,
                        EffectiveAmnt,
                        RiskAmnt,
                        AllowanceType,
                        DailyAllowanceAmnt,
                        effdate,
                        invaliddate,
                        Status,
                        WaitingPeriod,
                        DeductibleType,
                        Deductible,
                        ClaimRatio,
                        ManageCom,
                        insuredgroupcode,
                        groupname,
                        SystemCode,
                        ListImportDate,
                        ClaimBackDate,
                        MakeDate,
                        MakeTime,
                        ModifyDate,
                        ModifyTime,
                          '${'$'}{`date -d "${'$'}1 +1 days" "+%Y-%m-%d"`}' as last_dw_opt_date
                      from urp_dws.pre_increment_lcget i ) m;
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("full_lcget")
        assertThat(result.targetTable.schemaOrDatabase).isEqualTo("urp_dws")
        assertThat(result.targetTable.alias).isNull()

        // Verify no explicit target columns (INSERT INTO table SELECT ... without column list)
        assertThat(result.targetColumns).isNull()

        // Verify source tables - should extract tables from SELECT part
        assertThat(result.sourceTables).hasSize(2)
        assertThat(result.sourceTables).extracting("name")
            .containsExactlyInAnyOrder("pre_full_lcget", "pre_increment_lcget")

        // Verify schema for source tables
        assertThat(result.sourceTables).filteredOn { it.name == "pre_full_lcget" }
            .extracting("schemaOrDatabase")
            .contains("urp_dws")

        assertThat(result.sourceTables).filteredOn { it.name == "pre_increment_lcget" }
            .extracting("schemaOrDatabase")
            .contains("urp_dws")

        // Verify table aliases  
        assertThat(result.sourceTables).filteredOn { it.name == "pre_full_lcget" }
            .extracting("alias")
            .contains("f")

        assertThat(result.sourceTables).filteredOn { it.name == "pre_increment_lcget" }
            .extracting("alias")
            .contains("i")

        // Verify source columns - UNION ALL extracts columns from both sides, so we get more than the outer SELECT
        assertThat(result.sourceColumns).hasSizeGreaterThanOrEqualTo(48)

        // Check some key columns
        assertThat(result.sourceColumns).extracting("name")
            .contains("GrpAppNo", "ProductNo", "LiabilityCode", "last_dw_opt_date")

        // Verify column mappings - should match the source columns count
        assertThat(result.columnMappings).hasSize(result.sourceColumns.size)

        // Check first column mapping
        assertThat(result.columnMappings[0].sourceColumn.name).isEqualTo("GrpAppNo")
        assertThat(result.columnMappings[0].targetColumnName).isEqualTo("GrpAppNo")
        assertThat(result.columnMappings[0].targetColumnIndex).isEqualTo(0)

        // Check that last_dw_opt_date is in the mappings
        val lastDwOptMappings = result.columnMappings.filter { it.sourceColumn.name == "last_dw_opt_date" }
        assertThat(lastDwOptMappings).isNotEmpty()
    }

    @Test
    @DisplayName("Should parse simple INSERT OVERWRITE SELECT statement")
    fun testParseSimpleInsertOverwriteSelect() {
        // Given
        val sqlQuery = """
            insert overwrite table urp_dws.pre_lcget_lmduty 
            select 
            dutycode,
            dutyname
            from 
            urp_ods.full_dutyname ;
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("pre_lcget_lmduty")
        assertThat(result.targetTable.schemaOrDatabase).isEqualTo("urp_dws")
        assertThat(result.targetTable.alias).isNull()

        // Verify no explicit target columns (INSERT INTO table SELECT ... without column list)
        assertThat(result.targetColumns).isNull()

        // Verify source tables
        assertThat(result.sourceTables).hasSize(1)
        assertThat(result.sourceTables[0].name).isEqualTo("full_dutyname")
        assertThat(result.sourceTables[0].schemaOrDatabase).isEqualTo("urp_ods")
        assertThat(result.sourceTables[0].alias).isNull()

        // Verify source columns
        assertThat(result.sourceColumns).hasSize(2)
        assertThat(result.sourceColumns).extracting("name").containsExactly("dutycode", "dutyname")
        assertThat(result.sourceColumns).extracting("tablePrefix").containsOnlyNulls()
        assertThat(result.sourceColumns).extracting("alias").containsOnlyNulls()
        assertThat(result.sourceColumns).extracting("isWildcard").containsOnly(false)

        // Verify column mappings
        assertThat(result.columnMappings).hasSize(2)

        // First column mapping
        assertThat(result.columnMappings[0].sourceColumn.name).isEqualTo("dutycode")
        assertThat(result.columnMappings[0].targetColumnName).isEqualTo("dutycode")
        assertThat(result.columnMappings[0].targetColumnIndex).isEqualTo(0)

        // Second column mapping
        assertThat(result.columnMappings[1].sourceColumn.name).isEqualTo("dutyname")
        assertThat(result.columnMappings[1].targetColumnName).isEqualTo("dutyname")
        assertThat(result.columnMappings[1].targetColumnIndex).isEqualTo(1)
    }

    @Test
    @DisplayName("Should parse INSERT OVERWRITE with LEFT SEMI JOIN")
    fun testParseInsertOverwriteWithLeftSemiJoin() {
        // Given
        val sqlQuery = """
            insert overwrite table urp_dws.pre_increment_lcget_lmduty 
            select j.dutycode,
                   j.dutyname
                   from urp_dws.pre_lcget_lmduty j 
              left semi join urp_dws.lcget_temp i
                on j.dutycode = substr(i.dutycode, 1, 6)
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("pre_increment_lcget_lmduty")
        assertThat(result.targetTable.schemaOrDatabase).isEqualTo("urp_dws")
        assertThat(result.targetTable.alias).isNull()

        // Verify no explicit target columns
        assertThat(result.targetColumns).isNull()

        // Verify source tables
        assertThat(result.sourceTables).hasSize(2)
        assertThat(result.sourceTables).extracting("name").containsExactlyInAnyOrder("pre_lcget_lmduty", "lcget_temp")

        // Verify first source table (main table)
        assertThat(result.sourceTables).filteredOn { it.name == "pre_lcget_lmduty" }
            .extracting("schemaOrDatabase")
            .contains("urp_dws")
        assertThat(result.sourceTables).filteredOn { it.name == "pre_lcget_lmduty" }
            .extracting("alias")
            .contains("j")

        // Verify second source table (joined table)
        assertThat(result.sourceTables).filteredOn { it.name == "lcget_temp" }
            .extracting("schemaOrDatabase")
            .contains("urp_dws")
        assertThat(result.sourceTables).filteredOn { it.name == "lcget_temp" }
            .extracting("alias")
            .contains("i")

        // Verify source columns
        assertThat(result.sourceColumns).hasSize(2)

        // First column
        assertThat(result.sourceColumns[0].name).isEqualTo("dutycode")
        assertThat(result.sourceColumns[0].tablePrefix).isEqualTo("j")
        assertThat(result.sourceColumns[0].alias).isNull()
        assertThat(result.sourceColumns[0].originalExpression).isEqualTo("j.dutycode")
        assertThat(result.sourceColumns[0].isWildcard).isFalse()

        // Second column
        assertThat(result.sourceColumns[1].name).isEqualTo("dutyname")
        assertThat(result.sourceColumns[1].tablePrefix).isEqualTo("j")
        assertThat(result.sourceColumns[1].alias).isNull()
        assertThat(result.sourceColumns[1].originalExpression).isEqualTo("j.dutyname")
        assertThat(result.sourceColumns[1].isWildcard).isFalse()

        // Verify column mappings
        assertThat(result.columnMappings).hasSize(2)

        // First column mapping
        assertThat(result.columnMappings[0].sourceColumn.name).isEqualTo("dutycode")
        assertThat(result.columnMappings[0].sourceColumn.tablePrefix).isEqualTo("j")
        assertThat(result.columnMappings[0].targetColumnName).isEqualTo("dutycode")
        assertThat(result.columnMappings[0].targetColumnIndex).isEqualTo(0)

        // Second column mapping
        assertThat(result.columnMappings[1].sourceColumn.name).isEqualTo("dutyname")
        assertThat(result.columnMappings[1].sourceColumn.tablePrefix).isEqualTo("j")
        assertThat(result.columnMappings[1].targetColumnName).isEqualTo("dutyname")
        assertThat(result.columnMappings[1].targetColumnIndex).isEqualTo(1)
    }

    @Test
    @DisplayName("Should parse SQL query with newline characters")
    fun testParseSqlWithNewlines() {
        // Given
        // val sqlQuery = "select a\n from b"
        val sqlQuery = "select a\n from b"

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables
        assertThat(result.tables).hasSize(1)
        assertThat(result.tables[0].name).isEqualTo("b")
        assertThat(result.tables[0].schemaOrDatabase).isNull()
        assertThat(result.tables[0].alias).isNull()

        // Verify columns
        assertThat(result.columns).hasSize(1)
        assertThat(result.columns[0].name).isEqualTo("a")
        assertThat(result.columns[0].tablePrefix).isNull()
        assertThat(result.columns[0].alias).isNull()
        assertThat(result.columns[0].isWildcard).isFalse()
    }

    @Test
    @DisplayName("Should parse SQL query with escaped newline characters from user input")
    fun testParseSqlWithEscapedNewlines() {
        // Given - user input error where literal \n is entered instead of actual newline
        val sqlQuery = "select a\\n from b"

        // When - parser should automatically normalize escaped newlines
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables
        assertThat(result.tables).hasSize(1)
        assertThat(result.tables[0].name).isEqualTo("b")
        assertThat(result.tables[0].schemaOrDatabase).isNull()
        assertThat(result.tables[0].alias).isNull()

        // Verify columns
        assertThat(result.columns).hasSize(1)
        assertThat(result.columns[0].name).isEqualTo("a")
        assertThat(result.columns[0].tablePrefix).isNull()
        assertThat(result.columns[0].alias).isNull()
        assertThat(result.columns[0].isWildcard).isFalse()
    }

    @Test
    @DisplayName("Should parse SQL query with escaped tab characters from user input")
    fun testParseSqlWithEscapedTabs() {
        // Given - user input error where literal \t is entered instead of actual tab
        val sqlQuery = "select a\\t from b"

        // When - parser should automatically normalize escaped tabs
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables
        assertThat(result.tables).hasSize(1)
        assertThat(result.tables[0].name).isEqualTo("b")
        assertThat(result.tables[0].schemaOrDatabase).isNull()
        assertThat(result.tables[0].alias).isNull()

        // Verify columns
        assertThat(result.columns).hasSize(1)
        assertThat(result.columns[0].name).isEqualTo("a")
        assertThat(result.columns[0].tablePrefix).isNull()
        assertThat(result.columns[0].alias).isNull()
        assertThat(result.columns[0].isWildcard).isFalse()
    }

    @Test
    @DisplayName("Should parse INSERT INTO with complex SELECT including JOINs and CASE statements")
    fun testParseInsertWithComplexSelectAndJoins() {
        // Given
        val sqlQuery = """
            insert into urp_bui_prip.temp_LLMedicalFeeDetail 
            select distinct trans.busino busino,
                   '000052'          CompanyCode,
                   ll.ClaimNo              ClaimNo,
                   ll.ReceiptNo            ReceiptNo,
                   ll.CostItemCode         CostItemCode,
                   ll.CostItemName         CostItemName,
                   ll.MedicalDetailCode    MedicalDetailCode,
                   ll.MedicalDetailName    MedicalDetailName,
                   ll.MedicalChemistryName MedicalChemistryName,
                   ll.MedicalDetailPY      MedicalDetailPY,
                   ll.MedicalDetailWB      MedicalDetailWB,
                   ll.ChargeItemLevelCode  ChargeItemLevelCode,
                   ll.FeeAmount            FeeAmount,
                   ll.FeeDeductibleAmount  FeeDeductibleAmount,
                   ll.Quantity             Quantity,
                   ll.UnitPrice            UnitPrice,
                   ll.Unit                 Unit,
                   ll.pushdate             pushdate
              from (select * from urp_mid.urm_llmedicalfeedetail  where pushdate = to_date(days_add(now(), -${'$'}(((date +%s ) - ${'$'}(date +%s -d ${'$'}(date "+%Y%m%d" -d "0 days")))/86400)));) 
               and IsDeleted <> '1' )  ll
              inner join urp_bui_prip.temp_lcpoltransaction trans on trans.ClaimNo = ll.ClaimNo and trans.btype <> '98'
              inner join urp_mid.urm_llclaimpolicy llcl on (case when char_length(ifnull(trans.grppolicyno,''))>0 then trans.grppolicyno = llcl.grppolicyno else trans.PolicyNo = llcl.PolicyNo end) and llcl.IsDeleted <> '1' 
              inner join urp_bui_prip.busstype busstype on trans.busstype = busstype.busstype_id and busstype.tablename = 'LLMEDICALFEEDETAIL'
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("temp_LLMedicalFeeDetail")
        assertThat(result.targetTable.schemaOrDatabase).isEqualTo("urp_bui_prip")
        assertThat(result.targetTable.alias).isNull()

        // Verify no explicit target columns (INSERT INTO table SELECT ... without column list)
        assertThat(result.targetColumns).isNull()

        // Verify source tables
        assertThat(result.sourceTables).hasSize(4)
        assertThat(result.sourceTables).extracting("name").containsExactlyInAnyOrder(
            "urm_llmedicalfeedetail", "temp_lcpoltransaction", "urm_llclaimpolicy", "busstype"
        )

        // Verify schema for source tables
        assertThat(result.sourceTables).filteredOn { it.name == "urm_llmedicalfeedetail" }
            .extracting("schemaOrDatabase")
            .contains("urp_mid")

        assertThat(result.sourceTables).filteredOn { it.name == "temp_lcpoltransaction" }
            .extracting("schemaOrDatabase")
            .contains("urp_bui_prip")

        assertThat(result.sourceTables).filteredOn { it.name == "urm_llclaimpolicy" }
            .extracting("schemaOrDatabase")
            .contains("urp_mid")

        assertThat(result.sourceTables).filteredOn { it.name == "busstype" }
            .extracting("schemaOrDatabase")
            .contains("urp_bui_prip")

        // Verify table aliases - the parser extracts actual table names from subqueries, not subquery aliases
        assertThat(result.sourceTables).filteredOn { it.name == "urm_llmedicalfeedetail" }
            .extracting("alias")
            .contains(null) // The actual table from the subquery has no alias

        assertThat(result.sourceTables).filteredOn { it.name == "temp_lcpoltransaction" }
            .extracting("alias")
            .contains("trans")

        assertThat(result.sourceTables).filteredOn { it.name == "urm_llclaimpolicy" }
            .extracting("alias")
            .contains("llcl")

        assertThat(result.sourceTables).filteredOn { it.name == "busstype" }
            .extracting("alias")
            .contains("busstype")

        // Verify source columns
        assertThat(result.sourceColumns).hasSize(18)

        // Check key columns
        assertThat(result.sourceColumns).extracting("name").contains(
            "busino", "ClaimNo", "ReceiptNo", "CostItemCode", "FeeAmount", "pushdate"
        )

        // Verify some columns have table prefixes
        assertThat(result.sourceColumns).filteredOn { it.tablePrefix == "trans" }
            .hasSize(1)
        assertThat(result.sourceColumns).filteredOn { it.tablePrefix == "ll" }
            .hasSize(16)

        // Verify literal value (constant)
        assertThat(result.sourceColumns).filteredOn { it.name == "'000052'" }
            .hasSize(1)
        assertThat(result.sourceColumns).filteredOn { it.name == "'000052'" }
            .extracting("isWildcard")
            .contains(false)

        // Verify column mappings
        assertThat(result.columnMappings).hasSize(18)

        // Check first column mapping (busino)
        assertThat(result.columnMappings[0].sourceColumn.name).isEqualTo("busino")
        assertThat(result.columnMappings[0].sourceColumn.tablePrefix).isEqualTo("trans")
        assertThat(result.columnMappings[0].targetColumnName).isEqualTo("busino")
        assertThat(result.columnMappings[0].targetColumnIndex).isEqualTo(0)

        // Check literal value mapping (CompanyCode with alias)
        assertThat(result.columnMappings[1].sourceColumn.name).isEqualTo("'000052'")
        assertThat(result.columnMappings[1].targetColumnName).isEqualTo("CompanyCode") // Uses the alias as target column name
        assertThat(result.columnMappings[1].targetColumnIndex).isEqualTo(1)

        // Check last column mapping (pushdate)
        val lastMapping = result.columnMappings.last()
        assertThat(lastMapping.sourceColumn.name).isEqualTo("pushdate")
        assertThat(lastMapping.sourceColumn.tablePrefix).isEqualTo("ll")
        assertThat(lastMapping.targetColumnName).isEqualTo("pushdate")
        assertThat(lastMapping.targetColumnIndex).isEqualTo(17)
    }

    @Test
    @DisplayName("Should parse simple DELETE statement")
    fun testParseSimpleDeleteStatement() {
        // Given
        val deleteQuery = """
            DELETE FROM users WHERE id = 123
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(deleteQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("users")
        assertThat(result.targetTable.schemaOrDatabase).isNull()
        assertThat(result.targetTable.alias).isNull()

        // Verify no target columns for DELETE
        assertThat(result.targetColumns).isNull()

        // Verify no source tables for simple DELETE
        assertThat(result.sourceTables).isEmpty()

        // Verify source columns from WHERE clause
        assertThat(result.sourceColumns).hasSize(1)
        assertThat(result.sourceColumns[0].name).isEqualTo("id")
        assertThat(result.sourceColumns[0].tablePrefix).isNull()
        assertThat(result.sourceColumns[0].alias).isNull()
        assertThat(result.sourceColumns[0].isWildcard).isFalse()

        // Verify column mappings
        assertThat(result.columnMappings).hasSize(1)
        assertThat(result.columnMappings[0].sourceColumn.name).isEqualTo("id")
        assertThat(result.columnMappings[0].targetColumnName).isNull()
        assertThat(result.columnMappings[0].targetColumnIndex).isEqualTo(0)
    }

    @Test
    @DisplayName("Should parse DELETE statement with JOIN")
    fun testParseDeleteStatementWithJoin() {
        // Given
        val deleteQuery = """
            DELETE u 
            FROM users u
            JOIN orders o ON u.id = o.user_id
            WHERE o.status = 'cancelled'
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(deleteQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("users")
        assertThat(result.targetTable.schemaOrDatabase).isNull()
        assertThat(result.targetTable.alias).isEqualTo("u")

        // Verify no target columns for DELETE
        assertThat(result.targetColumns).isNull()

        // Verify source tables from JOIN
        assertThat(result.sourceTables).hasSize(1)
        assertThat(result.sourceTables[0].name).isEqualTo("orders")
        assertThat(result.sourceTables[0].schemaOrDatabase).isNull()
        assertThat(result.sourceTables[0].alias).isEqualTo("o")

        // Verify source columns from WHERE and JOIN conditions
        assertThat(result.sourceColumns).hasSizeGreaterThanOrEqualTo(3)
        
        // Should contain columns from JOIN condition (u.id, o.user_id) and WHERE condition (o.status)
        val columnNames = result.sourceColumns.map { it.name }
        assertThat(columnNames).contains("id", "user_id", "status")

        // Verify column mappings exist
        assertThat(result.columnMappings).hasSize(result.sourceColumns.size)
        assertThat(result.columnMappings).allMatch { it.targetColumnName == null }
    }

    @Test
    @DisplayName("Should parse DELETE statement with schema-qualified table")
    fun testParseDeleteStatementWithSchema() {
        // Given
        val deleteQuery = """
            DELETE FROM myschema.users WHERE created_date < '2023-01-01'
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(deleteQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table with schema
        assertThat(result.targetTable.name).isEqualTo("users")
        assertThat(result.targetTable.schemaOrDatabase).isEqualTo("myschema")
        assertThat(result.targetTable.alias).isNull()

        // Verify no target columns for DELETE
        assertThat(result.targetColumns).isNull()

        // Verify no source tables for simple DELETE
        assertThat(result.sourceTables).isEmpty()

        // Verify source columns from WHERE clause
        assertThat(result.sourceColumns).hasSize(1)
        assertThat(result.sourceColumns[0].name).isEqualTo("created_date")
        assertThat(result.sourceColumns[0].tablePrefix).isNull()
        assertThat(result.sourceColumns[0].alias).isNull()
        assertThat(result.sourceColumns[0].isWildcard).isFalse()

        // Verify column mappings
        assertThat(result.columnMappings).hasSize(1)
        assertThat(result.columnMappings[0].sourceColumn.name).isEqualTo("created_date")
        assertThat(result.columnMappings[0].targetColumnName).isNull()
        assertThat(result.columnMappings[0].targetColumnIndex).isEqualTo(0)
    }

    @Test
    @DisplayName("Should parse DELETE statement with subquery in WHERE clause")
    fun testParseDeleteStatementWithSubquery() {
        // Given
        val deleteQuery = """
            DELETE FROM orders 
            WHERE user_id IN (
                SELECT id FROM users WHERE status = 'inactive'
            )
        """.trimIndent()

        // When
        val result = SqlParser.parseDataModification(deleteQuery)

        // Then
        assertThat(result).isNotNull

        // Verify target table
        assertThat(result.targetTable.name).isEqualTo("orders")
        assertThat(result.targetTable.schemaOrDatabase).isNull()
        assertThat(result.targetTable.alias).isNull()

        // Verify no target columns for DELETE
        assertThat(result.targetColumns).isNull()

        // Verify no source tables from JOIN (subqueries are handled differently)
        assertThat(result.sourceTables).isEmpty()

        // Verify source columns from WHERE clause
        assertThat(result.sourceColumns).hasSizeGreaterThanOrEqualTo(1)
        
        // Should contain at least user_id from the outer WHERE clause
        val columnNames = result.sourceColumns.map { it.name }
        assertThat(columnNames).contains("user_id")

        // Verify column mappings exist
        assertThat(result.columnMappings).hasSize(result.sourceColumns.size)
        assertThat(result.columnMappings).allMatch { it.targetColumnName == null }
    }

    @Test
    @org.junit.jupiter.api.Disabled("Parser correctly extracts actual table names from subqueries rather than subquery aliases, which provides better lineage information. The current behavior of extracting real tables (smsSSHFY, ljspayperson, etc.) is more valuable than capturing subquery aliases (p, h, f, k, g) for data lineage analysis.")
    @DisplayName("Should parse complex query with multiple JOINs, subqueries and functions")
    fun testParseComplexQueryWithMultipleJoinsAndSubqueries() {
        // Given
        val sqlQuery = """
            SELECT
                'IP11' AS SMType, -- (消息类型)
                f.contno AS ContNo, -- (合同号)
                f.prtno AS PrtNo, -- (产品号)
                f.managecom AS ManageCom, -- (管理机构代码)
                k.managecomname AS ComName, -- (管理机构名称)
                f.salechnl AS SaleChnl, -- (销售渠道)
                f.agentcode AS AgentCode, -- (代理人代码)
                f.name AS AgentName, -- (代理人姓名)
                f.agentmobile AS Mobile, -- (代理人手机号)
                f.appntno AS AppntNo, -- (投保人号)
                f.appntname AS AppntName, -- (投保人姓名)
                f.appntsex AS AppntSex, -- (投保人性别代码)
                f.sex AS AppntSexName, -- (投保人性别名称)
                a.paymoney AS InitialPrem, -- (首期保费)
                a.bankdealdate AS EnterAccDate, -- (入账日期)
                h.lastpaytodate AS ShouldPayDate, -- (应缴日期)
                h.sumduepaymoney AS ShouldPayMoney, -- (应缴金额)
                a.bankdealdate AS DeductDate, -- (扣款日期)
                a.senddate AS SendDate, -- (发送日期)
                a.modifydate AS ReturnDate, -- (回盘日期)
                p.sendcount AS SendCount, -- (发送次数)
                IFNULL(IsOrphan, '否') AS IsOrphan, -- (是否孤儿单)
                f.mobile AS PreferPhone, -- (优选电话)
                f.phone AS OtherPhone, -- (其他电话)
                STR_TO_DATE(DATE_FORMAT(NOW(), '%Y-%m-%d'), '%Y-%m-%d') AS EXPDate, -- (失效日期)
                DATE_FORMAT(NOW(), '%H:%i:%s') AS EXPTime, -- (失效时间)
                'LIS' AS SourceSYS -- (来源系统)
            FROM
                smsSSHFY AS a
            JOIN (
                SELECT
                    COUNT(*) AS sendcount,
                    paycode
                FROM
                    lyreturnfrombankb
                WHERE
                    notype = '2'
                    AND dealtype = 'S'
                GROUP BY
                    paycode
            ) AS p
                ON p.paycode = a.paycode
            JOIN (
                SELECT
                    j.contno,
                    j.getnoticeno,
                    SUM(j.sumduepaymoney) AS sumduepaymoney,
                    MAX(j.lastpaytodate) AS lastpaytodate
                FROM
                    ljspayperson AS j
                WHERE
                    j.paytype = 'ZC'
                    AND EXISTS (
                        SELECT
                            'x'
                        FROM
                            ljspayperson
                        WHERE
                            paycount > 1
                            AND payintv = 12
                            AND contno = j.contno
                    )
                GROUP BY
                    j.contno,
                    j.getnoticeno
            ) AS h
                ON h.contno = a.edoracceptno
                AND h.getnoticeno = a.paycode
            JOIN (
                SELECT
                    c.contno,
                    c.prtno,
                    c.managecom,
                    c.salechnl,
                    c.agentcode,
                    e.appntno,
                    e.appntname,
                    e.appntsex,
                    m.name AS sex,
                    d.name,
                    d.mobile AS agentmobile,
                    n.mobile_phone_number AS mobile,
                    n.phone_number AS phone
                FROM
                    (
                        SELECT
                            appntno,
                            contno,
                            prtno,
                            managecom,
                            salechnl,
                            agentcode
                        FROM
                            lccont
                        WHERE
                            conttype <> '2'
                            AND contno IN (
                                SELECT
                                    edoracceptno
                                FROM
                                    smsSSHFY
                            )
                    ) AS c,
                    laagent AS d,
                    (
                        SELECT
                            a.contno,
                            a.appntno,
                            a.customer_phone_link_no,
                            b.chinese_name AS appntname,
                            b.sex_code AS appntsex
                        FROM
                            lcappnt AS a,
                            person AS b
                        WHERE
                            b.customer_number = a.appntno
                            AND b.version_number = a.applicant_version
                            AND a.contno IN (
                                SELECT
                                    edoracceptno
                                FROM
                                    smsSSHFY
                            )
                    ) AS e,
                    dictionary AS m,
                    phone AS n
                WHERE
                    c.contno = e.contno
                    AND c.appntno = e.appntno
                    AND c.agentcode = d.agentcode
                    AND m.type = 'sex_code'
                    AND m.code = e.appntsex
                    AND e.appntno = n.customer_number
                    AND e.customer_phone_link_no = n.phone_serial_number
            ) AS f
                ON a.edoracceptno = f.contno
            JOIN (
                SELECT
                    name AS managecomname,
                    comcode
                FROM
                    ldcom
            ) AS k
                ON k.comcode = f.managecom
            LEFT JOIN (
                SELECT
                    '是' AS IsOrphan,
                    contno
                FROM
                    lrascription
                GROUP BY
                    contno
            ) AS g
                ON g.contno = a.edoracceptno;
        """.trimIndent()

        // When
        val result = SqlParser.parse(sqlQuery)

        // Then
        assertThat(result).isNotNull

        // Verify tables - should extract all tables from main query and subqueries
        assertThat(result.tables).hasSizeGreaterThanOrEqualTo(8)
        assertThat(result.tables).extracting("name").contains(
            "smsSSHFY", "lyreturnfrombankb", "ljspayperson", "lccont",
            "laagent", "lcappnt", "person", "dictionary", "phone", "ldcom", "lrascription"
        )

        // Verify main table alias
        assertThat(result.tables).filteredOn { it.name == "smsSSHFY" }
            .extracting("alias")
            .contains("a")

        // Verify subquery aliases are captured
        val subqueryAliases = result.tables.mapNotNull { it.alias }
        assertThat(subqueryAliases).contains("p", "h", "f", "k", "g")

        // Verify columns - should capture all selected columns
        assertThat(result.columns).hasSize(26)
        assertThat(result.columns).extracting("name").contains(
            "contno", "prtno", "managecom", "salechnl", "agentcode", "paymoney",
            "bankdealdate", "senddate", "modifydate", "sendcount", "mobile", "phone"
        )

        // Verify some columns have aliases
        assertThat(result.columns).filteredOn { it.alias == "SMType" }
            .hasSize(1)
        assertThat(result.columns).filteredOn { it.alias == "ContNo" }
            .hasSize(1)
        assertThat(result.columns).filteredOn { it.alias == "SourceSYS" }
            .hasSize(1)

        // Verify some columns have table prefixes
        assertThat(result.columns).filteredOn { it.tablePrefix == "f" }
            .hasSizeGreaterThan(0)
        assertThat(result.columns).filteredOn { it.tablePrefix == "a" }
            .hasSizeGreaterThan(0)
        assertThat(result.columns).filteredOn { it.tablePrefix == "h" }
            .hasSizeGreaterThan(0)

        // Verify function expressions are captured
        assertThat(result.columns).filteredOn { it.name.contains("IFNULL") }
            .hasSize(1)
        assertThat(result.columns).filteredOn { it.name.contains("STR_TO_DATE") }
            .hasSize(1)
        assertThat(result.columns).filteredOn { it.name.contains("DATE_FORMAT") }
            .hasSize(1)
    }

}
