package com.datayes.lineage

import com.datayes.task.LineageTaskProcessingException
import com.datayes.task.LineageTaskService
import com.datayes.task.SystemLineageCollectionRequest
import com.datayes.task.TaskType
import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service

/**
 * 异步血缘处理服务 (Async Lineage Processing Service)
 *
 * 专门处理需要异步执行的血缘收集任务
 */
@Service
class AsyncLineageService(
    private val lineageTaskService: LineageTaskService,
    private val jobStatusManager: JobStatusManager
) {

    private val logger = LoggerFactory.getLogger(AsyncLineageService::class.java)

    /**
     * 在后台异步处理系统血缘收集 (Process system lineage collection in background)
     */
    @Async("asyncExecutor")
    fun processSystemLineageInBackground(
        taskType: TaskType,
        systemType: String,
        request: SystemLineageCollectionRequest
    ) {
        try {
            logger.info("3f2b8a1d | 开始后台处理系统血缘收集: systemType=$systemType, executedBy=${request.executedBy}")

            // 执行系统血缘收集 (execute system lineage collection)
            lineageTaskService.processSystemLineageCollection(taskType, request)

            logger.info("6c9e4f2a | 后台系统血缘收集处理完成: systemType=$systemType, executedBy=${request.executedBy}")

        } catch (e: LineageTaskProcessingException) {
            logger.error("3f8a1d6b | 后台系统血缘收集处理失败: systemType=$systemType", e)
        } catch (e: Exception) {
            logger.error("9c2e5f8d | 后台系统血缘收集时发生未知错误: systemType=$systemType", e)
        } finally {
            // 确保无论成功还是失败都取消注册任务状态
            jobStatusManager.unregisterRunningJob(systemType)
        }
    }
}