package com.datayes.lineage

import com.datayes.util.JdbcUrlParser
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

/**
 * 手动血缘管理服务 (Manual Lineage Management Service)
 *
 * UC-14, UC-15, UC-16: 处理手动血缘记录的创建、编辑和删除业务逻辑
 */
@Service
class ManualLineageService(
    private val lineageRepository: LineageRepository,
    private val lineageTablePropertiesRepository: LineageTablePropertiesRepository
) {

    private val logger = LoggerFactory.getLogger(ManualLineageService::class.java)

    companion object {
        const val MANUAL_INPUT_SOURCE = "MANUAL_INPUT"
        const val MANUAL_INPUT_JOB_KEY = "MANUAL_LINEAGE_INPUT"
    }


    // ====================================================================
    // 简化的CRUD操作方法 (Simplified CRUD Operations)
    // ====================================================================

    /**
     * 创建血缘关系 (Create lineage relationship)
     *
     * 自动处理数据源、表和列的创建，创建表级和列级血缘关系
     */
    @Transactional
    fun createLineageRelationship(request: CreateLineageRequest): LineageOperationResponse {
        return try {
            logger.info("c1d2e3f4 | 开始创建简化血缘关系: sourceTable=${request.source}, targetTable=${request.target}")

            // 1. 验证请求
            val validation = validateCreateLineageRequest(request)
            if (!validation.success) {
                return LineageOperationResponse(
                    success = false,
                    message = "验证失败",
                    errors = validation.errors
                )
            }

            // 2. 创建或获取源表和目标表
            val sourceTableId = getTableId(request.source, "源表")
            val targetTableId = getTableId(request.target, "目标表")

            // 3. 检查是否已存在相同的表级血缘关系
            val existingTableRelationship =
                lineageRepository.findExistingTableRelationship(sourceTableId, targetTableId)
            if (existingTableRelationship != null) {
                return LineageOperationResponse(
                    success = false,
                    message = "表级血缘关系已存在",
                    errors = listOf("源表和目标表之间已存在血缘关系: ID=$existingTableRelationship")
                )
            }

            // 4. 创建表级血缘关系
            val tableRelationshipId = lineageRepository.createSimplifiedTableLineage(
                sourceTableId = sourceTableId,
                targetTableId = targetTableId,
                createdBy = request.updateBy
            )

            // 5. 创建列级血缘关系
            var columnRelationshipsCreated = 0
            for (columnMapping in request.columns) {
                try {
                    // 确保源列和目标列存在
                    val sourceColumnId = lineageRepository.getOrCreateColumnWithDetails(
                        tableId = sourceTableId,
                        columnName = columnMapping.sourceColumnName,
                        dataType = columnMapping.sourceDataType ?: "VARCHAR",
                        comment = columnMapping.sourceColumnComment,
                        isPrimaryKey = columnMapping.sourceIsPrimaryKey ?: false,
                        isNullable = columnMapping.sourceIsNullable ?: true,
                        defaultValue = columnMapping.sourceDefaultValue,
                        columnOrder = columnMapping.sourceColumnOrder
                    )

                    val targetColumnId = lineageRepository.getOrCreateColumnWithDetails(
                        tableId = targetTableId,
                        columnName = columnMapping.targetColumnName,
                        dataType = columnMapping.targetDataType ?: "VARCHAR",
                        comment = columnMapping.targetColumnComment,
                        isPrimaryKey = columnMapping.targetIsPrimaryKey ?: false,
                        isNullable = columnMapping.targetIsNullable ?: true,
                        defaultValue = columnMapping.targetDefaultValue,
                        columnOrder = columnMapping.targetColumnOrder
                    )

                    // 创建列级血缘关系
                    lineageRepository.createSimplifiedColumnLineage(
                        sourceTableId = sourceTableId,
                        targetTableId = targetTableId,
                        sourceColumnId = sourceColumnId,
                        targetColumnId = targetColumnId,
                        createdBy = request.updateBy
                    )

                    columnRelationshipsCreated++

                } catch (e: Exception) {
                    logger.error(
                        "g5h6i7j8 | 创建列级血缘关系失败: sourceColumn=${columnMapping.sourceColumnName}, targetColumn=${columnMapping.targetColumnName}",
                        e
                    )
                }
            }

            logger.info("k9l0m1n2 | 简化血缘关系创建成功: tableRelationshipId=$tableRelationshipId, columnRelationships=$columnRelationshipsCreated")

            LineageOperationResponse(
                success = true,
                message = "血缘关系创建成功",
                tableRelationshipId = tableRelationshipId,
                affectedTableRelationships = 1,
                affectedColumnRelationships = columnRelationshipsCreated
            )

        } catch (e: Exception) {
            logger.error("o3p4q5r6 | 创建简化血缘关系时发生错误", e)
            LineageOperationResponse(
                success = false,
                message = "创建失败",
                errors = listOf("系统错误: ${e.message}")
            )
        }
    }

    /**
     * 更新血缘关系 (Update lineage relationship)
     *
     * 根据表级关系ID更新列映射，只影响MANUAL_INPUT的数据
     */
    @Transactional
    fun updateLineageRelationship(request: UpdateLineageRequest): LineageOperationResponse {
        return try {
            logger.info("s7t8u9v0 | 开始更新简化血缘关系: tableRelationshipId=${request.tableRelationshipId}")

            // 1. 验证表级关系是否存在且为MANUAL_INPUT
            val tableRelationship = lineageRepository.findTableRelationshipById(request.tableRelationshipId)
            if (tableRelationship == null) {
                return LineageOperationResponse(
                    success = false,
                    message = "表级血缘关系不存在",
                    errors = listOf("找不到ID为 ${request.tableRelationshipId} 的表级血缘关系")
                )
            }

            if (tableRelationship.sourceSystem != MANUAL_INPUT_SOURCE) {
                return LineageOperationResponse(
                    success = false,
                    message = "只能更新手动输入的血缘关系",
                    errors = listOf("该血缘关系的来源为 ${tableRelationship.sourceSystem}，只能更新来源为 MANUAL_INPUT 的关系")
                )
            }

            // 2. 删除现有的列级血缘关系（只删除MANUAL_INPUT的）
            val deletedColumnRelationships = lineageRepository.deleteColumnRelationshipsByTableRelationship(
                sourceTableId = tableRelationship.sourceTableId,
                targetTableId = tableRelationship.targetTableId
            )

            // 3. 创建新的列级血缘关系
            var columnRelationshipsCreated = 0
            for (columnMapping in request.columns) {
                try {
                    // 确保源列和目标列存在
                    val sourceColumnId = lineageRepository.getOrCreateColumnWithDetails(
                        tableId = tableRelationship.sourceTableId,
                        columnName = columnMapping.sourceColumnName,
                        dataType = columnMapping.sourceDataType ?: "VARCHAR",
                        comment = columnMapping.sourceColumnComment,
                        isPrimaryKey = columnMapping.sourceIsPrimaryKey ?: false,
                        isNullable = columnMapping.sourceIsNullable ?: true,
                        defaultValue = columnMapping.sourceDefaultValue,
                        columnOrder = columnMapping.sourceColumnOrder
                    )

                    val targetColumnId = lineageRepository.getOrCreateColumnWithDetails(
                        tableId = tableRelationship.targetTableId,
                        columnName = columnMapping.targetColumnName,
                        dataType = columnMapping.targetDataType ?: "VARCHAR",
                        comment = columnMapping.targetColumnComment,
                        isPrimaryKey = columnMapping.targetIsPrimaryKey ?: false,
                        isNullable = columnMapping.targetIsNullable ?: true,
                        defaultValue = columnMapping.targetDefaultValue,
                        columnOrder = columnMapping.targetColumnOrder
                    )

                    // 创建列级血缘关系
                    lineageRepository.createSimplifiedColumnLineage(
                        sourceTableId = tableRelationship.sourceTableId,
                        targetTableId = tableRelationship.targetTableId,
                        sourceColumnId = sourceColumnId,
                        targetColumnId = targetColumnId,
                        createdBy = request.updateBy
                    )

                    columnRelationshipsCreated++

                } catch (e: Exception) {
                    logger.error(
                        "w1x2y3z4 | 更新列级血缘关系失败: sourceColumn=${columnMapping.sourceColumnName}, targetColumn=${columnMapping.targetColumnName}",
                        e
                    )
                }
            }

            logger.info("a5b6c7d8 | 简化血缘关系更新成功: tableRelationshipId=${request.tableRelationshipId}, deletedColumns=$deletedColumnRelationships, createdColumns=$columnRelationshipsCreated")

            LineageOperationResponse(
                success = true,
                message = "血缘关系更新成功",
                tableRelationshipId = request.tableRelationshipId,
                affectedTableRelationships = 1,
                affectedColumnRelationships = columnRelationshipsCreated
            )

        } catch (e: Exception) {
            logger.error("e9f0g1h2 | 更新简化血缘关系时发生错误", e)
            LineageOperationResponse(
                success = false,
                message = "更新失败",
                errors = listOf("系统错误: ${e.message}")
            )
        }
    }

    /**
     * 删除血缘关系 (Delete lineage relationships)
     *
     * 批量删除表级和相关列级血缘关系，只删除MANUAL_INPUT的数据
     */
    @Transactional
    fun deleteLineageRelationships(request: DeleteLineageRequest): LineageOperationResponse {
        return try {
            logger.info("i3j4k5l6 | 开始删除简化血缘关系: count=${request.tableRelationshipIdList.size}")

            var deletedTableRelationships = 0
            var deletedColumnRelationships = 0
            val errors = mutableListOf<String>()

            for (tableRelationshipId in request.tableRelationshipIdList) {
                try {
                    // 验证表级关系是否存在且为MANUAL_INPUT
                    val tableRelationship = lineageRepository.findTableRelationshipById(tableRelationshipId)
                    if (tableRelationship == null) {
                        errors.add("找不到ID为 $tableRelationshipId 的表级血缘关系")
                        continue
                    }

                    if (tableRelationship.sourceSystem != MANUAL_INPUT_SOURCE) {
                        errors.add("ID为 $tableRelationshipId 的血缘关系来源为 ${tableRelationship.sourceSystem}，只能删除来源为 MANUAL_INPUT 的关系")
                        continue
                    }

                    // 删除列级血缘关系
                    val deletedColumns = lineageRepository.deleteColumnRelationshipsByTableRelationship(
                        sourceTableId = tableRelationship.sourceTableId,
                        targetTableId = tableRelationship.targetTableId
                    )

                    // 删除表级血缘关系
                    val deletedTable = lineageRepository.deleteTableRelationshipById(tableRelationshipId)

                    deletedTableRelationships += deletedTable
                    deletedColumnRelationships += deletedColumns

                } catch (e: Exception) {
                    logger.error("m7n8o9p0 | 删除表级血缘关系失败: tableRelationshipId=$tableRelationshipId", e)
                    errors.add("删除ID为 $tableRelationshipId 的血缘关系时发生错误: ${e.message}")
                }
            }

            logger.info("q1r2s3t4 | 简化血缘关系删除完成: deletedTableRelationships=$deletedTableRelationships, deletedColumnRelationships=$deletedColumnRelationships, errors=${errors.size}")

            LineageOperationResponse(
                success = errors.isEmpty(),
                message = if (errors.isEmpty()) "血缘关系删除成功" else "部分血缘关系删除失败",
                affectedTableRelationships = deletedTableRelationships,
                affectedColumnRelationships = deletedColumnRelationships,
                errors = errors
            )

        } catch (e: Exception) {
            logger.error("u5v6w7x8 | 删除简化血缘关系时发生错误", e)
            LineageOperationResponse(
                success = false,
                message = "删除失败",
                errors = listOf("系统错误: ${e.message}")
            )
        }
    }

    /**
     * 获取血缘关系详情 (Get lineage relationship details)
     */
    fun getLineageRelationshipDetails(tableRelationshipId: Long): LineageRelationshipDetails? {
        return try {
            logger.info("y9z0a1b2 | 查询简化血缘关系详情: tableRelationshipId=$tableRelationshipId")

            lineageRepository.getSimplifiedLineageDetails(tableRelationshipId)

        } catch (e: Exception) {
            logger.error("c3d4e5f6 | 查询简化血缘关系详情时发生错误", e)
            null
        }
    }

    /**
     * 根据表ID删除所有相关的手动血缘关系 (Delete all manual lineage relationships by table ID)
     *
     * 删除指定表ID作为源表或目标表的所有MANUAL_INPUT血缘关系，包括TABLE_LEVEL和COLUMN_LEVEL
     */
    @Transactional
    fun deleteManualLineageByTableId(tableId: Long): LineageOperationResponse {
        return try {
            logger.info("f8g2h5i9 | 开始根据表ID删除手动血缘关系: tableId=$tableId")

            // 1. 验证表是否存在
            val tableExists = lineageTablePropertiesRepository.existsTable(tableId)
            if (!tableExists) {
                return LineageOperationResponse(
                    success = false,
                    message = "表不存在",
                    errors = listOf("找不到ID为 $tableId 的表")
                )
            }

            // 2. 执行删除操作
            val deletionResult = lineageRepository.deleteManualLineageRelationshipsByTableId(tableId)

            logger.info("j3k6l0m4 | 表ID血缘关系删除完成: tableId=$tableId, 删除表级关系=${deletionResult.deletedTableRelationships}, 删除列级关系=${deletionResult.deletedColumnRelationships}, 总计=${deletionResult.totalDeleted}")

            LineageOperationResponse(
                success = true,
                message = "手动血缘关系删除成功",
                affectedTableRelationships = deletionResult.deletedTableRelationships,
                affectedColumnRelationships = deletionResult.deletedColumnRelationships
            )

        } catch (e: Exception) {
            logger.error("n7o1p4q8 | 根据表ID删除手动血缘关系时发生错误: tableId=$tableId", e)
            LineageOperationResponse(
                success = false,
                message = "删除失败",
                errors = listOf("系统错误: ${e.message}")
            )
        }
    }

    // ====================================================================
    // 私有辅助方法 (Private Helper Methods)
    // ====================================================================

    /**
     * 验证创建血缘关系请求
     */
    private fun validateCreateLineageRequest(request: CreateLineageRequest): LineageOperationResponse {
        val errors = mutableListOf<String>()

        // 验证源表信息
        if (!request.source.isValid()) {
            errors.add("源表信息无效：必须提供完整的数据源信息（datasourceName, dbType, databaseName, tableName）或有效的ID引用（databaseId, tableId）")
        } else if (request.source.isDetailedReference()) {
            // 详细信息方式的验证
            if (request.source.datasourceName.isNullOrBlank()) {
                errors.add("源数据源名称不能为空")
            }
            if (request.source.dbType.isNullOrBlank()) {
                errors.add("源数据库类型不能为空")
            }
            if (request.source.databaseName.isNullOrBlank()) {
                errors.add("源数据库名称不能为空")
            }
            if (request.source.tableName.isNullOrBlank()) {
                errors.add("源表名称不能为空")
            }
            
            // 验证连接信息
            if (request.source.customJdbcUrl.isNullOrBlank()) {
                if (request.source.host.isNullOrBlank() || request.source.port == null) {
                    errors.add("源表必须提供customJdbcUrl或者同时提供host和port")
                }
            }
        }

        // 验证目标表信息
        if (!request.target.isValid()) {
            errors.add("目标表信息无效：必须提供完整的数据源信息（datasourceName, dbType, databaseName, tableName）或有效的ID引用（databaseId, tableId）")
        } else if (request.target.isDetailedReference()) {
            // 详细信息方式的验证
            if (request.target.datasourceName.isNullOrBlank()) {
                errors.add("目标数据源名称不能为空")
            }
            if (request.target.dbType.isNullOrBlank()) {
                errors.add("目标数据库类型不能为空")
            }
            if (request.target.databaseName.isNullOrBlank()) {
                errors.add("目标数据库名称不能为空")
            }
            if (request.target.tableName.isNullOrBlank()) {
                errors.add("目标表名称不能为空")
            }
            
            // 验证连接信息
            if (request.target.customJdbcUrl.isNullOrBlank()) {
                if (request.target.host.isNullOrBlank() || request.target.port == null) {
                    errors.add("目标表必须提供customJdbcUrl或者同时提供host和port")
                }
            }
        }

        // 验证操作人
        if (request.updateBy.isBlank()) {
            errors.add("操作人不能为空")
        }

        // 检查源表和目标表是否相同（仅在使用详细信息时检查）
        if (request.source.isDetailedReference() && request.target.isDetailedReference()) {
            if (request.source.datasourceName == request.target.datasourceName &&
                request.source.tableName == request.target.tableName
            ) {
                errors.add("源表和目标表不能相同")
            }
        } else if (request.source.isIdReference() && request.target.isIdReference()) {
            if (request.source.tableId == request.target.tableId) {
                errors.add("源表和目标表不能相同")
            }
        }

        return LineageOperationResponse(
            success = errors.isEmpty(),
            message = if (errors.isEmpty()) "验证通过" else "验证失败",
            errors = errors
        )
    }

    /**
     * 获取表ID - 支持两种方式：详细信息创建 或 ID直接引用
     */
    private fun getTableId(tableRef: TableReference, tableType: String): Long {
        return if (tableRef.isIdReference()) {
            // 方式2：直接使用已存在的tableId
            logger.info("t1u2v3w4 | 使用ID引用方式获取${tableType}ID: tableId=${tableRef.tableId}")
            
            // 验证表是否存在
            val exists = lineageTablePropertiesRepository.existsTable(tableRef.tableId!!)
            if (!exists) {
                throw IllegalArgumentException("${tableType}不存在，tableId=${tableRef.tableId}")
            }
            
            tableRef.tableId
        } else {
            // 方式1：通过详细信息创建或获取
            ensureTableExistsForTableReference(tableRef, tableType)
        }
    }

    /**
     * 确保表存在（通过TableReference）
     */
    private fun ensureTableExistsForTableReference(tableRef: TableReference, tableType: String): Long {
        return try {
            logger.info("08de582b | 开始确保${tableType}存在: datasource=${tableRef.datasourceName}, table=${tableRef.tableName}")

            // 解析主机和端口信息
            val (finalHost, finalPort, connectionString) = parseConnectionInfo(
                tableRef.customJdbcUrl, 
                tableRef.host, 
                tableRef.port, 
                tableRef.dbType!!, 
                tableRef.databaseName!!
            )

            // 创建 DatabaseInfo 对象
            val databaseInfo = DatabaseInfo(
                dbType = tableRef.dbType,
                host = finalHost,
                port = finalPort,
                databaseName = tableRef.databaseName,
                originalConnectionString = connectionString
            )

            // 获取或创建数据源
            val datasourceId = lineageRepository.getOrCreateDatasourceForManualLineage(databaseInfo)
            logger.info("8210346d | ${tableType}数据源ID获取成功: datasourceId=$datasourceId")

            // 创建 TableInfo 对象
            val tableInfo = TableInfo(
                tableName = tableRef.tableName!!,
                schema = tableRef.schemaName,
                database = databaseInfo
            )

            // 获取或创建表
            val tableId = lineageRepository.getOrCreateTableForManualLineage(tableInfo, datasourceId)
            logger.info("7948938f | ${tableType}ID获取成功: tableId=$tableId")

            tableId

        } catch (e: Exception) {
            logger.error("bf7dfef4 | 确保${tableType}存在时发生错误", e)
            throw e
        }
    }

    /**
     * 解析连接信息
     */
    private fun parseConnectionInfo(
        customJdbcUrl: String?,
        providedHost: String?,
        providedPort: Int?,
        dbType: String,
        databaseName: String
    ): Triple<String, Int, String> {
        return if (!customJdbcUrl.isNullOrBlank()) {
            // 从customJdbcUrl解析
            val parseResult = JdbcUrlParser.parseJdbcUrl(customJdbcUrl)
            if (parseResult != null && parseResult.host != null && parseResult.port != null) {
                logger.info("w3x4y5z6 | 从customJdbcUrl解析得到: host=${parseResult.host}, port=${parseResult.port}")
                Triple(parseResult.host, parseResult.port, customJdbcUrl)
            } else {
                logger.warn("a7b8c9d0 | 无法解析customJdbcUrl，回退到直接提供的host/port: $customJdbcUrl")
                if (providedHost != null && providedPort != null) {
                    val fallbackConnectionString =
                        "jdbc:${dbType.lowercase()}://$providedHost:$providedPort/$databaseName"
                    Triple(providedHost, providedPort, fallbackConnectionString)
                } else {
                    throw IllegalArgumentException("无法获取有效的主机和端口信息")
                }
            }
        } else {
            // 使用直接提供的host/port
            if (providedHost != null && providedPort != null) {
                val directConnectionString = "jdbc:${dbType.lowercase()}://$providedHost:$providedPort/$databaseName"
                Triple(providedHost, providedPort, directConnectionString)
            } else {
                throw IllegalArgumentException("未提供customJdbcUrl且host/port为空")
            }
        }
    }
}

/**
 * 血缘关系信息 (Lineage Relationship Info)
 *
 * 用于权限验证的内部数据类
 */
data class LineageRelationshipInfo(
    val relationshipId: Long,
    val sourceType: String,
    val createdBy: String?,
    val createdAt: LocalDateTime?
)

/**
 * 简化的表级血缘关系信息 (Simplified Table Relationship Info)
 */
data class SimpleTableRelationshipInfo(
    val relationshipId: Long,
    val sourceTableId: Long,
    val targetTableId: Long,
    val sourceSystem: String,
    val createdBy: String?,
    val createdAt: LocalDateTime?
)