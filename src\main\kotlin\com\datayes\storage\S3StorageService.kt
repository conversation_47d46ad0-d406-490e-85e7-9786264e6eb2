package com.datayes.storage

import io.minio.*
import io.minio.errors.ErrorResponseException
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.*

/**
 * S3存储服务 (S3 Storage Service)
 *
 * 提供文件上传、下载等基础存储功能
 * 配置信息从数据库动态读取
 */
@Service
class S3StorageService(
    private val s3ConfigRepository: S3ConfigRepository
) {

    private val logger = LoggerFactory.getLogger(S3StorageService::class.java)

    /**
     * 获取S3配置并创建客户端 (Get S3 config and create client)
     */
    private fun createMinioClient(configName: String = "default"): Pair<MinioClient, S3Config> {
        val config = s3ConfigRepository.findByConfigNameAndIsActiveTrue(configName)
            ?: throw IllegalStateException("S3配置不存在或未启用: $configName")

        logger.debug("b7e5f3a1 | 使用S3配置: ${config.configName}, Endpoint: ${config.endpointUrl}")

        val client = MinioClient.builder()
            .endpoint(config.endpointUrl)
            .region(config.region)
            .credentials(config.accessKey, config.secretKey)
            .build()

        return Pair(client, config)
    }

    /**
     * 上传文件并返回文件键 (Upload file and return file key)
     */
    fun uploadFile(
        fileName: String,
        content: ByteArray,
        contentType: String = "application/octet-stream",
        configName: String = "default"
    ): S3UploadResult {
        logger.info("5b7d2f4c | 开始上传文件: $fileName, 大小: ${content.size} 字节")

        try {
            val (minioClient, config) = createMinioClient(configName)
            val fileKey = generateFileKey(fileName, config.filePrefix)

            val result = minioClient.putObject(
                PutObjectArgs.builder()
                    .bucket(config.bucketName)
                    .`object`(fileKey)
                    .stream(ByteArrayInputStream(content), content.size.toLong(), -1)
                    .contentType(contentType)
                    .build()
            )

            logger.info("6c8e3a5d | 文件上传成功: $fileKey, ETag: ${result.etag()}")

            return S3UploadResult(
                success = true,
                fileKey = fileKey,
                originalFileName = fileName,
                fileSize = content.size.toLong(),
                contentType = contentType,
                uploadTime = LocalDateTime.now()
            )

        } catch (e: ErrorResponseException) {
            val errorResponse = e.errorResponse()
            val errorMessage =
                "S3上传错误 (S3 upload error): code=${errorResponse.code()}, message=${errorResponse.message()}"
            logger.error("7d9f4b6e | 文件上传失败 (File upload failed): $fileName. $errorMessage", e)
            return S3UploadResult(
                success = false,
                errorMessage = errorMessage
            )
        } catch (e: Exception) {
            logger.error("7d9f4b6e | 文件上传失败 (File upload failed): $fileName", e)
            return S3UploadResult(
                success = false,
                errorMessage = e.message ?: "上传失败 (Upload failed)"
            )
        }
    }

    /**
     * 根据文件键下载文件 (Download file by file key)
     */
    fun downloadFile(fileKey: String, configName: String = "default"): S3DownloadResult {
        logger.info("8e1a5c7f | 开始下载文件: $fileKey")

        try {
            val (minioClient, config) = createMinioClient(configName)

            val response = minioClient.getObject(
                GetObjectArgs.builder()
                    .bucket(config.bucketName)
                    .`object`(fileKey)
                    .build()
            )

            val content = ByteArrayOutputStream().use { outputStream ->
                response.transferTo(outputStream)
                outputStream.toByteArray()
            }
            response.close()

            // 获取文件元数据
            val metadata = minioClient.statObject(
                StatObjectArgs.builder()
                    .bucket(config.bucketName)
                    .`object`(fileKey)
                    .build()
            )

            logger.info("9f2b6d8a | 文件下载成功: $fileKey, 大小: ${content.size} 字节")

            return S3DownloadResult(
                success = true,
                fileKey = fileKey,
                content = content,
                contentType = metadata.contentType() ?: "application/octet-stream",
                fileSize = content.size.toLong(),
                lastModified = metadata.lastModified()?.toInstant()
            )

        } catch (e: ErrorResponseException) {
            if (e.errorResponse().code() == "NoSuchKey") {
                logger.warn("1a3c7e9b | 文件不存在: $fileKey")
                return S3DownloadResult(
                    success = false,
                    errorMessage = "文件不存在: $fileKey"
                )
            } else {
                logger.error("2b4d8f1c | 文件下载失败: $fileKey", e)
                return S3DownloadResult(
                    success = false,
                    errorMessage = e.message ?: "下载失败"
                )
            }
        } catch (e: Exception) {
            logger.error("2b4d8f1c | 文件下载失败: $fileKey", e)
            return S3DownloadResult(
                success = false,
                errorMessage = e.message ?: "下载失败"
            )
        }
    }

    /**
     * 检查文件是否存在 (Check if file exists)
     */
    fun fileExists(fileKey: String, configName: String = "default"): Boolean {
        return try {
            val (minioClient, config) = createMinioClient(configName)
            minioClient.statObject(
                StatObjectArgs.builder()
                    .bucket(config.bucketName)
                    .`object`(fileKey)
                    .build()
            )
            true
        } catch (e: ErrorResponseException) {
            if (e.errorResponse().code() == "NoSuchKey") {
                false
            } else {
                logger.error("3c5e9a2d | 检查文件存在性失败: $fileKey", e)
                false
            }
        } catch (e: Exception) {
            logger.error("3c5e9a2d | 检查文件存在性失败: $fileKey", e)
            false
        }
    }

    /**
     * 列出文件 (List files)
     */
    fun listFiles(prefix: String? = null, maxKeys: Int = 100, configName: String = "default"): List<S3FileInfo> {
        val (minioClient, config) = createMinioClient(configName)
        val actualPrefix = prefix ?: config.filePrefix
        
        logger.info("4d6f1b3e | 列出文件，前缀: $actualPrefix")

        val results = minioClient.listObjects(
            ListObjectsArgs.builder()
                .bucket(config.bucketName)
                .prefix(actualPrefix)
                .maxKeys(maxKeys)
                .build()
        )

        return results.mapNotNull { result ->
            try {
                val item = result.get()
                S3FileInfo(
                    key = item.objectName(),
                    size = item.size()
                )
            } catch (e: Exception) {
                logger.warn("a1b2c3d4 | 跳过无法处理的文件项: ${e.message}")
                null
            }
        }
    }

    /**
     * 生成唯一的文件键 (Generate unique file key)
     */
    private fun generateFileKey(originalFileName: String, filePrefix: String): String {
        val timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd/HHmmss"))
        val uuid = UUID.randomUUID().toString().substring(0, 8)
        val extension = originalFileName.substringAfterLast('.', "")
        val nameWithoutExtension = originalFileName.substringBeforeLast('.')

        return if (extension.isNotEmpty()) {
            "${filePrefix}${timestamp}/${nameWithoutExtension}-${uuid}.${extension}"
        } else {
            "${filePrefix}${timestamp}/${originalFileName}-${uuid}"
        }
    }
}

/**
 * S3上传结果 (S3 Upload Result)
 */
data class S3UploadResult(
    val success: Boolean,
    val fileKey: String? = null,
    val originalFileName: String? = null,
    val fileSize: Long? = null,
    val contentType: String? = null,
    val uploadTime: LocalDateTime? = null,
    val errorMessage: String? = null
)

/**
 * S3下载结果 (S3 Download Result)
 */
data class S3DownloadResult(
    val success: Boolean,
    val fileKey: String? = null,
    val content: ByteArray? = null,
    val contentType: String? = null,
    val fileSize: Long? = null,
    val lastModified: java.time.Instant? = null,
    val errorMessage: String? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as S3DownloadResult

        if (success != other.success) return false
        if (fileKey != other.fileKey) return false
        if (content != null) {
            if (other.content == null) return false
            if (!content.contentEquals(other.content)) return false
        } else if (other.content != null) return false
        if (contentType != other.contentType) return false
        if (fileSize != other.fileSize) return false
        if (lastModified != other.lastModified) return false
        if (errorMessage != other.errorMessage) return false

        return true
    }

    override fun hashCode(): Int {
        var result = success.hashCode()
        result = 31 * result + (fileKey?.hashCode() ?: 0)
        result = 31 * result + (content?.contentHashCode() ?: 0)
        result = 31 * result + (contentType?.hashCode() ?: 0)
        result = 31 * result + (fileSize?.hashCode() ?: 0)
        result = 31 * result + (lastModified?.hashCode() ?: 0)
        result = 31 * result + (errorMessage?.hashCode() ?: 0)
        return result
    }
}

/**
 * S3文件信息 (S3 File Info)
 */
data class S3FileInfo(
    val key: String,
    val size: Long
)