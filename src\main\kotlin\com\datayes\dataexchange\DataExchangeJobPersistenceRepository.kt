package com.datayes.dataexchange

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.dao.DataAccessException
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.sql.ResultSet

/**
 * 数据交互作业持久化仓库 (Data Exchange Job Persistence Repository)
 * 
 * 负责将数据交互作业保存到主数据库的 data_exchange_jobs 表中
 */
@Repository
class DataExchangeJobPersistenceRepository(
    private val jdbcTemplate: JdbcTemplate,
    private val objectMapper: ObjectMapper
) {
    
    private val logger = LoggerFactory.getLogger(DataExchangeJobPersistenceRepository::class.java)
    
    /**
     * 逐个保存数据交互作业到 data_exchange_jobs 表
     * 如果某个作业保存失败，会跳过该作业并继续处理下一个
     * 
     * @param jobs 要保存的数据交互作业列表
     * @return 保存结果统计
     */
    fun saveDataExchangeJobsOneByOne(jobs: List<DataExchangeJob>): SaveResult {
        if (jobs.isEmpty()) {
            logger.info("a7b8c9d2 | 没有数据需要保存")
            return SaveResult(0, 0, emptyList())
        }
        
        logger.info("f4e5d6c7 | 开始逐个保存 ${jobs.size} 个数据交互作业")
        
        val sql = """
            INSERT INTO data_exchange_jobs (
                reader_job_id, reader_job_name, db_read, reader_table_name, reader_sql,
                write_job_id, write_job_name, db_w, columns, writer_table_name
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        var successCount = 0
        var failureCount = 0
        val failures = mutableListOf<JobSaveFailure>()
        
        jobs.forEachIndexed { index, job ->
            try {
                val args = arrayOf(
                    job.readerJobId,
                    job.readerJobName,
                    job.dbReader,
                    job.readerTableName.ifBlank { null },
                    job.readerSql.ifBlank { null },
                    job.writeJobId,
                    job.writeJobName,
                    job.dbWriter,
                    if (job.columns.isNotEmpty()) {
                        com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(job.columns)
                    } else null,
                    job.writerTableName
                )
                
                jdbcTemplate.update(sql, *args)
                successCount++
                
                if (index % 100 == 0) {
                    logger.debug("m5n6o7p8 | 已处理 ${index + 1}/${jobs.size} 个作业")
                }
                
            } catch (e: Exception) {
                failureCount++
                val failure = JobSaveFailure(
                    jobIndex = index,
                    readerJobId = job.readerJobId,
                    writeJobId = job.writeJobId,
                    errorMessage = e.message ?: "未知错误"
                )
                failures.add(failure)
                
                logger.warn("r2s3t4u5 | 保存作业失败，跳过处理: readerJobId=${job.readerJobId}, writeJobId=${job.writeJobId}, error=${e.message}")
            }
        }
        
        val result = SaveResult(successCount, failureCount, failures)
        logger.info("b9a8c7d6 | 数据交互作业保存完成: 成功 $successCount 个，失败 $failureCount 个")
        
        return result
    }
    
    /**
     * 清空 data_exchange_jobs 表中的所有数据
     * 
     * @return 删除的记录数量
     */
    @Transactional
    fun clearDataExchangeJobs(): Int {
        logger.info("h6i7j8k9 | 开始清空 data_exchange_jobs 表")
        
        return try {
            val deletedCount = jdbcTemplate.update("DELETE FROM data_exchange_jobs")
            logger.info("k9l0m1n2 | 成功删除 $deletedCount 条记录")
            deletedCount
        } catch (e: Exception) {
            logger.error("n2o3p4q5 | 清空 data_exchange_jobs 表时发生错误", e)
            throw e
        }
    }
    
    /**
     * 获取 data_exchange_jobs 表中的记录总数
     * 
     * @return 记录总数
     */
    fun countDataExchangeJobs(): Long {
        return try {
            val count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM data_exchange_jobs", Long::class.java) ?: 0L
            logger.debug("q5r6s7t8 | data_exchange_jobs 表中共有 $count 条记录")
            count
        } catch (e: Exception) {
            logger.error("t8u9v0w1 | 查询 data_exchange_jobs 表记录数时发生错误", e)
            throw e
        }
    }
    
    /**
     * 根据作业ID查询特定的数据交互作业
     * 
     * @param readerJobId 读取作业ID
     * @param writeJobId 写入作业ID
     * @return 数据交互作业，如果不存在则返回null
     */
    fun findDataExchangeJobByIds(readerJobId: String, writeJobId: String): DataExchangeJob? {
        return try {
            val sql = """
                SELECT 
                    reader_job_id,
                    reader_job_name,
                    db_read,
                    reader_table_name,
                    reader_sql,
                    write_job_id,
                    write_job_name,
                    db_w,
                    columns,
                    writer_table_name
                FROM data_exchange_jobs
                WHERE reader_job_id = ? AND write_job_id = ?
            """.trimIndent()
            val jobs = jdbcTemplate.query(sql, DataExchangeJobRowMapper(), readerJobId, writeJobId)
            jobs.firstOrNull()
        } catch (e: DataAccessException) {
            logger.error("f1g2h3i4 | 根据ID查询数据交互作业时发生错误: readerJobId=$readerJobId, writeJobId=$writeJobId", e)
            null
        }
    }
    
    /**
     * DataExchangeJob的RowMapper实现
     * 负责将查询结果映射为DataExchangeJob对象
     */
    private inner class DataExchangeJobRowMapper : RowMapper<DataExchangeJob> {
        
        override fun mapRow(rs: ResultSet, rowNum: Int): DataExchangeJob {
            return try {
                // 解析columns JSON字段
                val columnsJson = rs.getString("columns")
                val columns = parseColumnsFromJson(columnsJson)
                
                DataExchangeJob(
                    readerJobId = rs.getString("reader_job_id") ?: "",
                    readerJobName = rs.getString("reader_job_name") ?: "",
                    dbReader = rs.getString("db_read") ?: "",
                    readerTableName = rs.getString("reader_table_name") ?: "",
                    readerSql = rs.getString("reader_sql")?.trim('"') ?: "",
                    writeJobId = rs.getString("write_job_id") ?: "",
                    writeJobName = rs.getString("write_job_name") ?: "",
                    dbWriter = rs.getString("db_w") ?: "",
                    columns = columns,
                    writerTableName = rs.getString("writer_table_name") ?: ""
                )
            } catch (e: Exception) {
                logger.error("j5k6l7m8 | 映射数据交互作业行数据时发生错误: rowNum=$rowNum", e)
                throw e
            }
        }
        
        /**
         * 从JSON字符串解析列映射配置
         * 
         * @param columnsJson JSON字符串
         * @return 列映射配置列表
         */
        private fun parseColumnsFromJson(columnsJson: String?): List<DataExchangeColumnMapping> {
            if (columnsJson.isNullOrBlank()) {
                logger.warn("n8o9p0q1 | 列映射JSON为空")
                return emptyList()
            }
            
            return try {
                objectMapper.readValue(
                    columnsJson, 
                    object : TypeReference<List<DataExchangeColumnMapping>>() {}
                )
            } catch (e: Exception) {
                logger.error("r2s3t4u5 | 解析列映射JSON时发生错误: $columnsJson", e)
                emptyList()
            }
        }
    }
}

/**
 * 保存结果统计
 */
data class SaveResult(
    val successCount: Int,      // 成功保存的数量
    val failureCount: Int,      // 失败的数量
    val failures: List<JobSaveFailure>  // 失败详情列表
)

/**
 * 作业保存失败详情
 */
data class JobSaveFailure(
    val jobIndex: Int,          // 作业在列表中的索引
    val readerJobId: String,    // 读取作业ID
    val writeJobId: String,     // 写入作业ID
    val errorMessage: String    // 错误信息
)