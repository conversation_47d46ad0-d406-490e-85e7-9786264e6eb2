package com.datayes.hdfs

import org.apache.hadoop.conf.Configuration
import org.apache.hadoop.fs.FileSystem
import org.apache.hadoop.fs.Path
import org.apache.hadoop.security.UserGroupInformation
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.*
import org.slf4j.LoggerFactory
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

/**
 * HDFS Reader Test
 *
 * Tests HDFS connectivity and file reading functionality based on demo.txt configuration
 * Note: These tests require actual HDFS cluster access and proper configuration files
 */
class HdfsReaderTest {

    private val logger = LoggerFactory.getLogger(HdfsReaderTest::class.java)
    private lateinit var configuration: Configuration

    @BeforeEach
    fun setup() {
        // Configure Hadoop for Windows compatibility
        setupHadoopEnvironment()

        configuration = Configuration()

        // TBDS authentication configuration
        configuration.set("hadoop_security_authentication_tbds_username", "ms_urp")
        configuration.set("hadoop_security_authentication_tbds_secureid", "ze2OFe78OHPrGnvc6WsdJ0Gruum1cpqLowhC")
        configuration.set("hadoop_security_authentication_tbds_securekey", "aYE90rxcOX01KhXgzFegiHKRAjsmUPYy")
        configuration.set("fs.hdfs.impl", "org.apache.hadoop.hdfs.DistributedFileSystem")
    }

    @Test
    fun `test HDFS configuration initialization`() {
        // Test that configuration can be created and basic properties are set
        assertNotNull(configuration)
        assertTrue(configuration.get("hadoop_security_authentication_tbds_username") == "ms_urp")
        assertTrue(configuration.get("fs.hdfs.impl") == "org.apache.hadoop.hdfs.DistributedFileSystem")

        logger.info("HDFS configuration initialized successfully")
    }

    @Test
    fun `test HDFS connection and file listing`() {
        try {
            // Add configuration resources if available
            loadConfigurationFiles()

            // Set up authentication
            UserGroupInformation.setConfiguration(configuration)
            UserGroupInformation.loginUserFromSubject(null)

            // Get file system
            val fileSystem = FileSystem.get(configuration)

            // Test root directory listing
            val fileList = fileSystem.listStatus(Path("/"))

            assertNotNull(fileList)
            logger.info("Successfully connected to HDFS and listed {} files in root directory", fileList.size)

            // Log file information
            fileList.forEach { fileStatus ->
                logger.info("HDFS file: {}", fileStatus.toString())
            }

            fileSystem.close()

        } catch (e: Exception) {
            logger.error("HDFS connection test failed", e)
            throw e
        }
    }

    @Test
    fun `test HDFS configuration with mock filesystem`() {
        // Create a mock FileSystem for unit testing without actual HDFS
        val mockFileSystem = mock(FileSystem::class.java)
        val mockFileStatus = mock(org.apache.hadoop.fs.FileStatus::class.java)

        // Configure mock behavior
        `when`(mockFileSystem.listStatus(any(Path::class.java)))
            .thenReturn(arrayOf(mockFileStatus))
        `when`(mockFileStatus.toString())
            .thenReturn("mock_file_status")

        // Test the mock setup
        val fileList = mockFileSystem.listStatus(Path("/"))
        assertNotNull(fileList)
        assertTrue(fileList.isNotEmpty())

        logger.info("Mock HDFS test completed successfully")
    }

    @Test
    fun `test Hadoop environment setup`() {
        val osName = System.getProperty("os.name").lowercase()
        val isWindows = osName.contains("windows")

        if (isWindows) {
            val hadoopHome = System.getProperty("hadoop.home.dir")
            assertNotNull(hadoopHome, "HADOOP_HOME should be set on Windows")
            assertTrue(hadoopHome.endsWith("hadoop"), "HADOOP_HOME should point to hadoop directory")

            logger.info("Windows Hadoop environment configured successfully")
            logger.info("HADOOP_HOME: {}", hadoopHome)
        } else {
            logger.info("Linux/Unix environment detected - system Hadoop configuration will be used")
        }

        // Verify common properties are set
        assertNotNull(System.getProperty("java.security.krb5.realm"))
        assertNotNull(System.getProperty("java.security.krb5.kdc"))
    }

    @Test
    fun `test configuration loading from classpath resources`() {
        // Test loading configuration from classpath resources
        val coreResource = this::class.java.classLoader.getResource("core-site.xml")
        val hdfsResource = this::class.java.classLoader.getResource("hdfs-site.xml")

        // Verify resources exist in classpath
        assertNotNull(coreResource, "core-site.xml should be available in classpath")
        assertNotNull(hdfsResource, "hdfs-site.xml should be available in classpath")

        // Load them into configuration
        configuration.addResource(coreResource)
        configuration.addResource(hdfsResource)

        logger.info("Successfully loaded HDFS configuration files from classpath")
        logger.info("Core site resource: {}", coreResource)
        logger.info("HDFS site resource: {}", hdfsResource)

        assertNotNull(configuration)
    }

    @Test
    fun `test share_ftp directory exists and is not empty`() {
        try {
            // Get file system using utility
            val fileSystem = HdfsUtils.createHdfsConnection()

            // Test /share_ftp directory
            val shareFtpPath = Path("/share_ftp")

            // Check if directory exists
            assertTrue(HdfsUtils.pathExists(fileSystem, shareFtpPath), "/share_ftp directory should exist")

            // List contents of /share_ftp directory
            val fileList = HdfsUtils.listDirectory(fileSystem, shareFtpPath)

            assertNotNull(fileList)
            assertTrue(fileList.isNotEmpty(), "/share_ftp directory should not be empty")

            logger.info("Successfully verified /share_ftp directory exists with {} files/directories", fileList.size)

            // Log directory contents
            fileList.forEach { fileName ->
                logger.info("/share_ftp content: {}", fileName)
            }

            fileSystem.close()

        } catch (e: Exception) {
            logger.error("share_ftp directory test failed", e)
            throw e
        }
    }

    @Test
    fun `test list shell scripts and zip files recursively in share_ftp`() {
        try {
            // Get file system using utility
            val fileSystem = HdfsUtils.createHdfsConnection()

            // Test /share_ftp directory
            val shareFtpPath = Path("/share_ftp")

            // Check if directory exists
            assertTrue(HdfsUtils.pathExists(fileSystem, shareFtpPath), "/share_ftp directory should exist")

            // Recursively find shell scripts and zip files
            val shellAndZipFiles = HdfsUtils.findFilesByExtensions(fileSystem, shareFtpPath, listOf("sh", "zip"))

            logger.info("Found {} shell scripts and zip files in /share_ftp", shellAndZipFiles.size)

            // Log all found files
            shellAndZipFiles.forEach { filePath ->
                logger.info("Found file: {}", filePath)
            }

            // Verify we found some files (assuming there should be some)
            assertNotNull(shellAndZipFiles)

            fileSystem.close()

        } catch (e: Exception) {
            logger.error("Recursive file listing test failed", e)
            throw e
        }
    }

    @Test
    fun `test list shell scripts and zip files without date postfix recursively in share_ftp`() {
        try {
            // Get file system using utility
            val fileSystem = HdfsUtils.createHdfsConnection()

            // Test /share_ftp directory
            val shareFtpPath = Path("/share_ftp")

            // Check if directory exists
            assertTrue(HdfsUtils.pathExists(fileSystem, shareFtpPath), "/share_ftp directory should exist")

            // Recursively find shell scripts and zip files without date postfix
            val shellAndZipFiles =
                HdfsUtils.findFilesByExtensionsWithoutDatePostfix(fileSystem, shareFtpPath, listOf("sh", "zip"))

            logger.info(
                "Found {} shell scripts and zip files (without date postfix) in /share_ftp",
                shellAndZipFiles.size
            )

            // Log all found files
            shellAndZipFiles.forEach { filePath ->
                logger.info("Found file: {}", filePath)
            }

            // Verify we found some files (assuming there should be some)
            assertNotNull(shellAndZipFiles)

            fileSystem.close()

        } catch (e: Exception) {
            logger.error("Recursive file listing (without date postfix) test failed", e)
            throw e
        }
    }

    @Test
    fun `test processZipFilesForShellScripts functionality`() {
        try {
            // Get file system using utility
            val fileSystem = HdfsUtils.createHdfsConnection()

            // Test /share_ftp/public_project directory for more focused results
            // Based on hdfs_path_list_output.txt, this directory contains:
            // - test.zip and test111.zip (should be processed)
            // - Multiple backup files with timestamps (should be filtered out)
            // val publicProjectPath = Path("/share_ftp/public_project")

            val publicProjectPath = Path("/share_ftp/public_project/uatDatasetDetail.zip")

            // Check if directory exists
            // assertTrue(HdfsUtils.pathExists(fileSystem, publicProjectPath), "/share_ftp/public_project directory should exist")
            assertTrue(
                HdfsUtils.pathExists(fileSystem, publicProjectPath),
                "/share_ftp/public_project/uatDatasetDetail.zip directory should exist"
            )

            // Process all ZIP files and extract shell scripts
            val zipProcessingResult = HdfsUtils.processZipFilesForShellScripts(fileSystem, publicProjectPath)

            logger.info(
                "e9f4a2b8 | Processed {} ZIP files, found {} shell scripts, total size: {} bytes",
                zipProcessingResult.totalZipFilesProcessed,
                zipProcessingResult.totalShellScriptsFound,
                zipProcessingResult.totalContentSize
            )

            // Log results for each ZIP file
            zipProcessingResult.results.forEach { zipResult ->
                logger.info(
                    "7c3d5f1e | ZIP file: {} contains {} shell scripts ({} bytes)",
                    zipResult.zipFilePath, zipResult.totalScriptCount, zipResult.totalContentSize
                )

                zipResult.shellScripts.forEach { shellScript ->
                    val contentPreview = if (shellScript.content.length > 100) {
                        shellScript.content.substring(0, 100) + "..."
                    } else {
                        shellScript.content
                    }
                    logger.info(
                        "a8b6e4d2 | Shell script: {} ({} bytes, content preview: {})",
                        shellScript.name, shellScript.sizeBytes, contentPreview
                    )
                }
            }

            // Verify the function returns a non-null result
            assertNotNull(zipProcessingResult)
            assertNotNull(zipProcessingResult.results)

            // Based on hdfs_path_list_output.txt, we expect:
            // - Only non-backup ZIP files to be processed (test.zip, test111.zip)
            // - Backup files with timestamps should be filtered out
            logger.info("f5a8e3c7 | Expected to process only non-backup ZIP files (test.zip, test111.zip)")
            logger.info("d2b9f6e4 | Backup files with timestamps should be automatically filtered out")

            // If there are ZIP files with shell scripts, verify structure
            if (zipProcessingResult.results.isNotEmpty()) {
                zipProcessingResult.results.forEach { zipResult ->
                    // Verify ZIP file path is not empty and points to public_project
                    assertTrue(zipResult.zipFilePath.isNotEmpty(), "ZIP file path should not be empty")
                    assertTrue(
                        zipResult.zipFilePath.contains("/share_ftp/public_project/"),
                        "ZIP file should be in public_project directory"
                    )

                    // Verify that processed files are not backup files (no timestamp patterns)
                    val fileName = zipResult.zipFilePath.substringAfterLast("/")
                    val isBackupFile = fileName.matches(Regex(".*\\.\\d{14}\\.zip$")) ||
                            fileName.matches(Regex(".*\\d{14}\\.zip$"))
                    assertTrue(!isBackupFile, "Processed file should not be a backup file: $fileName")

                    // Verify shell scripts list is not null
                    assertNotNull(zipResult.shellScripts, "Shell scripts list should not be null")

                    // Verify calculated totals match actual data
                    assertTrue(
                        zipResult.totalScriptCount == zipResult.shellScripts.size,
                        "Total script count should match shell scripts list size"
                    )
                    assertTrue(
                        zipResult.totalContentSize == zipResult.shellScripts.sumOf { it.sizeBytes },
                        "Total content size should match sum of individual script sizes"
                    )

                    // If shell scripts exist, verify their structure
                    zipResult.shellScripts.forEach { shellScript ->
                        assertTrue(shellScript.name.endsWith(".sh"), "Script name should end with .sh")
                        assertNotNull(shellScript.content, "Script content should not be null")
                        assertTrue(shellScript.sizeBytes >= 0, "Script size should be non-negative")
                        assertTrue(
                            shellScript.sizeBytes == shellScript.content.toByteArray(Charsets.UTF_8).size,
                            "Script size should match actual content size"
                        )
                    }
                }

                // Verify overall totals
                val expectedTotalScripts = zipProcessingResult.results.sumOf { it.totalScriptCount }
                val expectedTotalSize = zipProcessingResult.results.sumOf { it.totalContentSize }

                assertTrue(
                    zipProcessingResult.totalShellScriptsFound == expectedTotalScripts,
                    "Overall total scripts should match sum of individual ZIP results"
                )
                assertTrue(
                    zipProcessingResult.totalContentSize == expectedTotalSize,
                    "Overall total size should match sum of individual ZIP results"
                )

                logger.info("2f8b9e4a | Successfully validated structure of ZIP processing result")
                logger.info("c8f3a9e2 | Confirmed backup file filtering is working correctly")
            } else {
                logger.info("5d7a3c1f | No ZIP files with shell scripts found in /share_ftp/public_project directory")
            }

            fileSystem.close()

        } catch (e: Exception) {
            logger.error("1b4e8d6c | processZipFilesForShellScripts test failed", e)
            throw e
        }
    }

    @Test
    fun `test filterBackupZipFiles with various timestamp patterns`() {
        // Test data based on actual HDFS file patterns from hdfs_path_list_output.txt
        val testZipFiles = listOf(
            // Backup files with dot separator pattern
            "hdfs://hdfsCluster/share_ftp/public_project/test.20240313213459.zip",
            "hdfs://hdfsCluster/share_ftp/public_project/test.20240313214911.zip",
            "hdfs://hdfsCluster/share_ftp/public_project/test.20240313215736.zip",

            // Backup files with direct concatenation pattern
            "hdfs://hdfsCluster/share_ftp/public_project/test20230914161419.zip",
            "hdfs://hdfsCluster/share_ftp/public_project/test20230914162928.zip",
            "hdfs://hdfsCluster/share_ftp/public_project/test20230914163256.zip",

            // Mixed pattern with different base names
            "hdfs://hdfsCluster/share_ftp/public_project/test11120231009152022.zip",
            "hdfs://hdfsCluster/share_ftp/public_project/test11120231009152854.zip",

            // Non-backup files (should be kept)
            "hdfs://hdfsCluster/share_ftp/public_project/test.zip",
            "hdfs://hdfsCluster/share_ftp/public_project/test111.zip",
            "hdfs://hdfsCluster/share_ftp/public_project/production.zip",
            "hdfs://hdfsCluster/share_ftp/public_project/script123.zip"
        )

        // Test the filter function
        val filteredFiles = HdfsUtils.filterBackupZipFiles(testZipFiles)

        logger.info(
            "8e4f2a9c | Original files count: {}, Filtered files count: {}",
            testZipFiles.size, filteredFiles.size
        )

        // Log filtered results
        filteredFiles.forEach { filePath ->
            logger.info("6d2b8f3e | Kept file: {}", filePath)
        }

        // Verify that backup files are filtered out
        assertNotNull(filteredFiles)

        // Expected non-backup files
        val expectedFiles = listOf(
            "hdfs://hdfsCluster/share_ftp/public_project/test.zip",
            "hdfs://hdfsCluster/share_ftp/public_project/test111.zip",
            "hdfs://hdfsCluster/share_ftp/public_project/production.zip",
            "hdfs://hdfsCluster/share_ftp/public_project/script123.zip"
        )

        // Verify count
        assertTrue(
            filteredFiles.size == expectedFiles.size,
            "Expected ${expectedFiles.size} files, but got ${filteredFiles.size}"
        )

        // Verify that all expected files are present
        expectedFiles.forEach { expectedFile ->
            assertTrue(
                filteredFiles.contains(expectedFile),
                "Expected file should be present: $expectedFile"
            )
        }

        // Verify that backup files are NOT present
        val backupFiles = listOf(
            "hdfs://hdfsCluster/share_ftp/public_project/test.20240313213459.zip",
            "hdfs://hdfsCluster/share_ftp/public_project/test20230914161419.zip",
            "hdfs://hdfsCluster/share_ftp/public_project/test11120231009152022.zip"
        )

        backupFiles.forEach { backupFile ->
            assertTrue(
                !filteredFiles.contains(backupFile),
                "Backup file should be filtered out: $backupFile"
            )
        }

        logger.info("b5c9e7a4 | Successfully validated ZIP file filtering logic")
    }

    @Test
    fun `test filterBackupZipFiles edge cases`() {
        val edgeCaseFiles = listOf(
            // Files with numbers but not timestamp pattern
            "hdfs://hdfsCluster/test123.zip",
            "hdfs://hdfsCluster/file_v2.zip",

            // Files with partial timestamp (should not be filtered)
            "hdfs://hdfsCluster/test202309.zip",  // Only 6 digits
            "hdfs://hdfsCluster/test.202309.zip", // Only 6 digits with dot

            // Files with valid 14-digit timestamp (should be filtered)
            "hdfs://hdfsCluster/script.20230914163459.zip",
            "hdfs://hdfsCluster/data20230914163459.zip",

            // Files with timestamp in middle (should not be filtered)
            "hdfs://hdfsCluster/test20230914163459extra.zip",

            // Empty and special cases
            "hdfs://hdfsCluster/.zip",
            "hdfs://hdfsCluster/normalfile.zip"
        )

        val filteredFiles = HdfsUtils.filterBackupZipFiles(edgeCaseFiles)

        logger.info(
            "c3a8f5b2 | Edge case test - Original: {}, Filtered: {}",
            edgeCaseFiles.size, filteredFiles.size
        )

        // Expected to keep files without valid 14-digit timestamp patterns
        val expectedKeptFiles = listOf(
            "hdfs://hdfsCluster/test123.zip",
            "hdfs://hdfsCluster/file_v2.zip",
            "hdfs://hdfsCluster/test202309.zip",
            "hdfs://hdfsCluster/test.202309.zip",
            "hdfs://hdfsCluster/test20230914163459extra.zip",
            "hdfs://hdfsCluster/.zip",
            "hdfs://hdfsCluster/normalfile.zip"
        )

        // Expected to filter files with valid 14-digit timestamp patterns
        val expectedFilteredFiles = listOf(
            "hdfs://hdfsCluster/script.20230914163459.zip",
            "hdfs://hdfsCluster/data20230914163459.zip"
        )

        // Verify kept files
        expectedKeptFiles.forEach { expectedFile ->
            assertTrue(
                filteredFiles.contains(expectedFile),
                "File should be kept: $expectedFile"
            )
        }

        // Verify filtered files
        expectedFilteredFiles.forEach { expectedFile ->
            assertTrue(
                !filteredFiles.contains(expectedFile),
                "File should be filtered: $expectedFile"
            )
        }

        logger.info("d7f3e9a1 | Successfully validated edge cases for ZIP file filtering")
    }


    /**
     * Setup Hadoop environment for cross-platform compatibility
     * Configures necessary system properties for Windows and Linux
     */
    private fun setupHadoopEnvironment() {
        val osName = System.getProperty("os.name").lowercase()
        val isWindows = osName.contains("windows")

        if (isWindows) {
            // Set HADOOP_HOME for Windows
            val projectRoot = System.getProperty("user.dir")
            val hadoopHome = "$projectRoot/hadoop"

            System.setProperty("hadoop.home.dir", hadoopHome)
            System.setProperty("HADOOP_HOME", hadoopHome)

            // Set PATH to include winutils.exe
            val hadoopBin = "$hadoopHome/bin"
            val currentPath = System.getenv("PATH") ?: ""
            System.setProperty("java.library.path", "$hadoopBin;${System.getProperty("java.library.path", "")}")

            logger.info("Configured Hadoop for Windows environment")
            logger.info("HADOOP_HOME: {}", hadoopHome)
            logger.info("Hadoop bin directory: {}", hadoopBin)
        } else {
            logger.info("Running on Linux/Unix environment - using system Hadoop configuration")
        }

        // Common configuration for all platforms
        System.setProperty("java.security.krb5.realm", "")
        System.setProperty("java.security.krb5.kdc", "")
    }

    private fun loadConfigurationFiles() {
        // Load HDFS configuration from classpath resources
        val coreResource = this::class.java.classLoader.getResource("core-site.xml")
        val hdfsResource = this::class.java.classLoader.getResource("hdfs-site.xml")

        if (coreResource != null) {
            configuration.addResource(coreResource)
            logger.info("Loaded core-site.xml from classpath")
        } else {
            logger.warn("core-site.xml not found in classpath")
        }

        if (hdfsResource != null) {
            configuration.addResource(hdfsResource)
            logger.info("Loaded hdfs-site.xml from classpath")
        } else {
            logger.warn("hdfs-site.xml not found in classpath")
        }
    }
}