package com.datayes.hdfs

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table

/**
 * HDFS数据源映射实体 (HDFS Datasource Mapping Entity)
 *
 * 存储源数据源与目标数据源的配对映射关系
 */
@Table("hdfs_datasource_mapping")
data class HdfsDatasourceMapping(

    @Id
    val id: Long? = null,

    // 源数据源信息
    @Column("source_jdbc_url")
    val sourceJdbcUrl: String,

    // 目标数据源信息
    @Column("target_jdbc_url")
    val targetJdbcUrl: String,

    @Column("is_active")
    val isActive: Boolean = true
)