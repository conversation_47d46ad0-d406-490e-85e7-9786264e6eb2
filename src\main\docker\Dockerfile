FROM registry.minshenglife.com/infrastructure/openjdk:17.0.2-slim-bullseyearm

# 设置时区 (无外网无法安装 tzdata，直接设置 JVM 时区)
ENV TZ=Asia/Shanghai
#RUN apt-get update && apt-get install -y tzdata && \
#    ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 设置默认的主机解析规则，这些规则将由 entrypoint.sh 脚本在容器启动时添加到 /etc/hosts
# 用户可以在 docker run 时通过 -e EXTRA_HOSTS="..." 来覆盖此默认值
ENV EXTRA_HOSTS="*********** tbds-10-9-112-26\n\
*********** tbds-10-9-112-25\n\
*********** tbds-10-9-112-28\n\
*********** tbds-10-9-112-27\n\
*********** tbds-10-9-112-32\n\
*********** tbds-10-9-112-31\n\
*********** tbds-10-9-112-30\n\
*********** tbds-10-9-112-29\n\
*********** worker01\n\
********** master02\n\
********** master03\n\
*********** worker04\n\
*********** master04\n\
*********** worker03\n\
*********** master05\n\
*********** worker02\n\
********** portal01\n\
********** portal02\n\
********** master01\n\
*********** worker05\n\
*********** worker06\n\
*********** worker08\n\
*********** worker09\n\
*********** worker07"

# 设置工作目录
WORKDIR /app

# 添加应用JAR包
ADD target/dgp-lineage-collector-0.0.1-SNAPSHOT.jar /app/dgp-lineage-collector-0.0.1-SNAPSHOT.jar

# 暴露应用端口
EXPOSE 9503

# 复制并设置启动脚本权限
COPY src/main/docker/entrypoint.sh /app/entrypoint.sh
# RUN chmod +x /app/entrypoint.sh

# 设置容器启动命令
CMD ["/bin/sh", "/app/entrypoint.sh"]
