package com.datayes.metadata

import java.time.LocalDateTime

/**
 * 元数据数据源信息DTO (Metadata Data Source DTO)
 */
data class MetadataDataSourceDto(
    val id: Long,
    val sourceName: String,
    val dbType: String,
    val dbDriver: String,
    val dbName: String,
    val dbUrl: String?,
    val dbPort: Int?,
    val dbUsername: String,
    val customJdbcUrl: String?,
    val activeFlag: Boolean,
    val createBy: String?,
    val createTime: LocalDateTime?,
    val updateBy: String?,
    val updateTime: LocalDateTime?,
    val systemId: Long?,
    val description: String?,
    val systemInfo: MetadataSystemInfoDto?,
    val hasBeenCollected: Boolean
)

/**
 * 元数据系统信息DTO (Metadata System Info DTO)
 */
data class MetadataSystemInfoDto(
    val id: Long,
    val systemName: String,
    val systemAbbreviation: String?,
    val systemType: String?,
    val systemModule: String?,
    val moduleOwner: String?,
    val developmentDepartment: String?,
    val activeFlag: Boolean,
    val createBy: String?,
    val createTime: LocalDateTime?,
    val updateBy: String?,
    val updateTime: LocalDateTime?
)

/**
 * 匹配的元数据数据源列表响应 (Matched Metadata Data Sources Response)
 */
data class MatchedMetadataResponse(
    val lineageDatasourceId: Long,
    val matchedDataSources: List<MetadataDataSourceDto>,
    val totalCount: Int
)

/**
 * 分页元数据数据源DTO (Paged Metadata Data Sources DTO)
 */
data class PagedMetadataDataSourcesDto(
    val content: List<MetadataDataSourceDto>,
    val page: Int,
    val size: Int,
    val totalElements: Int,
    val totalPages: Int,
    val hasNext: Boolean,
    val hasPrevious: Boolean,
    val isFirst: Boolean,
    val isLast: Boolean,
    val numberOfElements: Int
)

/**
 * 血缘数据源与匹配的元数据数据源DTO (Lineage Datasource with Matched Metadata DTO)
 */
data class LineageDatasourceWithMetadataDto(
    val id: Long,
    val datasourceName: String,
    val dbType: String,
    val host: String,
    val port: Int,
    val databaseName: String,
    val status: String,
    val systemId: Long?, // 来自匹配的metadata_data_source -> metadata_system_info -> id，如果没有匹配则为null
    val connectionString: String,
    val createdAt: LocalDateTime?,
    val updatedAt: LocalDateTime?,
    val matchedMetadataDataSource: MetadataDataSourceDto? // 匹配的元数据数据源，可能为null
)

/**
 * 分页血缘数据源DTO (Paged Lineage Datasources DTO)
 */
data class PagedLineageDatasourcesDto(
    val content: List<LineageDatasourceWithMetadataDto>,
    val page: Int,
    val size: Int,
    val totalElements: Int,
    val totalPages: Int,
    val hasNext: Boolean,
    val hasPrevious: Boolean,
    val isFirst: Boolean,
    val isLast: Boolean,
    val numberOfElements: Int
)

/**
 * 系统间血缘表DTO (Lineage Table Between Systems DTO)
 */
data class LineageTableBetweenSystemsDto(
    val sourceSystemInfo: MetadataSystemInfoDto,
    val targetSystemInfo: MetadataSystemInfoDto,
    val sourceDatasource: LineageDatasourceDto,
    val targetDatasource: LineageDatasourceDto,
    val relationshipCount: Int,
    val lineageType: String?,
    val confidenceScore: java.math.BigDecimal?,
    val lastUpdated: LocalDateTime?,
    val sourceTable: LineageTableDto,
    val targetTable: LineageTableDto,
    val transformationDescription: String?,
    val sourceSystem: String?
)

/**
 * 血缘数据源DTO (Lineage Datasource DTO)
 */
data class LineageDatasourceDto(
    val id: Long,
    val datasourceName: String,
    val dbType: String,
    val host: String,
    val port: Int,
    val databaseName: String,
    val status: String,
    val systemId: Long?,
    val connectionString: String
)

/**
 * 血缘表DTO (Lineage Table DTO)
 */
data class LineageTableDto(
    val id: Long,
    val datasourceId: Long,
    val schemaName: String?,
    val tableName: String,
    val tableType: String?,
    val chineseName: String?,
    val description: String?,
    val syncFrequency: String?,
    val requirementId: String?,
    val status: String,
    val createdAt: LocalDateTime?,
    val updatedAt: LocalDateTime?
)

