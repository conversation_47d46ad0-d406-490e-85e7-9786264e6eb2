package com.datayes.auth

import org.springframework.data.annotation.Id
import org.springframework.data.jdbc.repository.query.Query
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import org.springframework.data.repository.CrudRepository
import org.springframework.stereotype.Repository

@Table("auth_user")
data class AuthUser(
    @Id
    val id: Long?,
    @Column("code")
    val code: String,
    @Column("name")
    val name: String,
    @Column("active_flag")
    val activeFlag: Boolean
)

@Repository
interface AuthUserRepository : CrudRepository<AuthUser, Long> {

    @Query("SELECT id, code, name, active_flag FROM auth_user WHERE active_flag = 1")
    fun findActiveUsers(): List<AuthUser>

    @Query("SELECT id, code, name, active_flag FROM auth_user WHERE code = :code")
    fun findByCode(code: String): AuthUser?

    @Query("SELECT id, code, name, active_flag FROM auth_user WHERE name = :name")
    fun findByName(name: String): List<AuthUser>

    @Query("SELECT id, code, name, active_flag FROM auth_user WHERE code = :value OR name = :value")
    fun findByCodeOrName(value: String): List<AuthUser>
}