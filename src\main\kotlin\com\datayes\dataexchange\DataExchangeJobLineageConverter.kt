package com.datayes.dataexchange

import com.datayes.lineage.*
import com.datayes.sql.SqlParser
import com.datayes.sql.SqlParsingException
import com.datayes.sql.ColumnReference
import com.datayes.util.TableNameNormalizer

/**
 * 血缘转换器 (Lineage Converter)
 *
 * 提供将数据交互平台作业数据转换为血缘信息的功能
 */
object DataExchangeJobLineageConverter {

    private val log = org.slf4j.LoggerFactory.getLogger(this.javaClass)

    /**
     * 将数据交互平台作业转换为数据血缘信息
     *
     * @param dataExchangeJob 数据交互平台作业数据
     * @return 血缘构建结果
     */
    fun convertToLineage(dataExchangeJob: DataExchangeJob): LineageResult {
        val warnings = mutableListOf<String>()
        val errors = mutableListOf<String>()

        try {
            // 解析源数据库信息
            val sourceDatabase = DatabaseInfo.parseFromJdbcUrl(dataExchangeJob.dbReader)
            if (sourceDatabase == null) {
                errors.add("无法解析源数据库连接字符串: ${dataExchangeJob.dbReader}")
                return LineageResult(null, warnings, errors, false)
            }

            // 解析目标数据库信息
            val targetDatabase = DatabaseInfo.parseFromJdbcUrl(dataExchangeJob.dbWriter)
            if (targetDatabase == null) {
                errors.add("无法解析目标数据库连接字符串: ${dataExchangeJob.dbWriter}")
                return LineageResult(null, warnings, errors, false)
            }

            // 从SQL中解析源表和列信息
            val sqlParseResult = parseTablesAndColumnsFromSql(dataExchangeJob.readerSql, sourceDatabase, warnings)

            // 降级方案：如果SQL解析失败，使用配置中的表名
            val finalSourceTables = sqlParseResult.sourceTables.ifEmpty {
                warnings.add("使用配置中的表名作为降级方案")
                listOf(
                    TableInfo(
                        schema = null,
                        tableName = TableNameNormalizer.normalizeTableName(dataExchangeJob.readerTableName) ?: dataExchangeJob.readerTableName,
                        database = sourceDatabase
                    )
                )
            }

            // 创建目标表信息
            val targetTable = TableInfo(
                schema = null,
                tableName = TableNameNormalizer.normalizeTableName(dataExchangeJob.writerTableName) ?: dataExchangeJob.writerTableName,
                database = targetDatabase
            )

            // 创建表级血缘关系
            val tableLineage = TableLineage(
                sourceTables = finalSourceTables,
                targetTable = targetTable,
                lineageType = determineLineageType(dataExchangeJob.readerSql)
            )

            // 创建列级血缘关系
            val columns = dataExchangeJob.columns
            val columnLineages = createColumnLineages(
                columnMappings = columns,
                sourceTables = finalSourceTables,
                targetTable = targetTable,
                sqlParseResult = sqlParseResult,
                warnings = warnings
            )

            // log warn if dataExchangeJob.columns is empty
            if (columns.isEmpty()) {
                log.warn("69a4f167 | 没有列映射信息，将使用默认的列映射")
            }

            // 验证列映射的完整性
            validateColumnMappings(columns, warnings)

            // 检查所有源表是否来自同一数据库
            val uniqueSourceDatabases = finalSourceTables.map { it.database }.toSet()
            if (uniqueSourceDatabases.size > 1) {
                warnings.add("检测到源表来自不同的数据库: ${uniqueSourceDatabases.map { it.databaseName }}")
            }

            // 创建完整的血缘信息
            val dataLineage = DataLineage(
                jobId = "${dataExchangeJob.readerJobId}-${dataExchangeJob.writeJobId}",
                jobName = "${dataExchangeJob.readerJobName} -> ${dataExchangeJob.writeJobName}",
                tableLineage = tableLineage,
                columnLineages = columnLineages,
                sourceDatabase = sourceDatabase,
                targetDatabase = targetDatabase,
                originalSql = dataExchangeJob.readerSql
            )

            return LineageResult(dataLineage, warnings, errors, true)

        } catch (e: Exception) {
            errors.add("转换过程中发生异常: ${e.message}")
            return LineageResult(null, warnings, errors, false)
        }
    }

    /**
     * SQL解析结果数据结构
     */
    private data class SqlParseInfo(
        val sourceTables: List<TableInfo>,
        val sourceColumns: List<ColumnReference>
    )

    /**
     * 从SQL语句中解析源表和列信息
     */
    private fun parseTablesAndColumnsFromSql(
        sql: String,
        sourceDatabase: DatabaseInfo,
        warnings: MutableList<String>,
    ): SqlParseInfo {
        return try {
            val parseResult = SqlParser.parse(sql)

            // 将解析出的表引用转换为TableInfo对象
            val sourceTables = parseResult.tables.map { tableRef ->
                TableInfo(
                    schema = determineSchemaForDatabaseType(sourceDatabase.dbType, tableRef.schemaOrDatabase),
                    tableName = tableRef.name,
                    database = sourceDatabase
                )
            }.also { tables ->
                if (tables.isEmpty()) {
                    warnings.add("从SQL中未能解析出任何源表")
                }
            }

            SqlParseInfo(sourceTables, parseResult.columns)
        } catch (e: SqlParsingException) {
            warnings.add("SQL解析失败: ${e.message}")
            // 降级方案：返回空结果
            SqlParseInfo(emptyList(), emptyList())
        }
    }

    /**
     * 创建列级血缘关系，使用SQL解析的列-表映射信息
     * 确保每个列引用正确的TableInfo实例，以便在数据库中正确映射到对应的lineage_tables记录
     */
    private fun createColumnLineages(
        columnMappings: List<DataExchangeColumnMapping>,
        sourceTables: List<TableInfo>,
        targetTable: TableInfo,
        sqlParseResult: SqlParseInfo,
        warnings: MutableList<String>
    ): List<ColumnLineage> {
        // 创建表名到TableInfo实例的精确映射
        // 这确保每个表名只对应一个TableInfo实例，避免在数据库中创建重复的lineage_tables记录
        val tableNameToInstanceMap = mutableMapOf<String, TableInfo>()
        val schemaTableNameToInstanceMap = mutableMapOf<String, TableInfo>()
        
        sourceTables.forEach { table ->
            // 使用完全限定名作为主键
            val fullyQualifiedName = table.getFullyQualifiedName()
            tableNameToInstanceMap[fullyQualifiedName] = table
            
            // 同时支持简单表名查找（用于向后兼容）
            tableNameToInstanceMap[table.tableName] = table
            
            // 如果有schema，也添加schema.table映射
            if (table.schema != null) {
                val schemaTableName = "${table.schema}.${table.tableName}"
                schemaTableNameToInstanceMap[schemaTableName] = table
            }
        }
        
        // 构建SQL别名到真实表名的映射，现在可以使用完整的解析信息
        val sqlAliasToTableMap = buildSqlAliasMapping(sqlParseResult, sourceTables)
        
        // 使用SQL解析结果中的源列作为驱动，而不是columnMappings
        return sqlParseResult.sourceColumns.mapIndexed { index, sourceColumnRef ->
            // 跳过通配符列（*），因为它们不能直接映射
            if (sourceColumnRef.isWildcard) {
                return@mapIndexed null
            }
            
            // 为每个列找到其对应的确切TableInfo实例
            val sourceTable = findExactSourceTableInstance(
                sourceColumnRef.name,
                sourceTables,
                sqlParseResult.sourceColumns,
                tableNameToInstanceMap,
                schemaTableNameToInstanceMap,
                sqlAliasToTableMap,
                warnings
            )

            // 尝试从columnMappings中找到对应的映射信息（用于获取类型和目标列信息）
            val correspondingMapping = findCorrespondingMapping(sourceColumnRef, columnMappings)
            
            val sourceColumn = ColumnInfo(
                columnName = cleanColumnName(sourceColumnRef.name),
                dataType = correspondingMapping?.srcColumnType ?: "UNKNOWN",
                comment = correspondingMapping?.columnRemark,
                table = sourceTable  // 使用确切的TableInfo实例
            )

            val targetColumn = ColumnInfo(
                columnName = correspondingMapping?.dstColumnName ?: sourceColumnRef.alias ?: sourceColumnRef.name,
                dataType = correspondingMapping?.dstColumnType ?: "UNKNOWN",
                comment = correspondingMapping?.columnRemark,
                table = targetTable
            )

            val transformation = correspondingMapping?.let { createTransformation(it) }

            ColumnLineage(
                sourceColumn = sourceColumn,
                targetColumn = targetColumn,
                transformation = transformation,
                columnIndex = correspondingMapping?.columnIndex ?: index
            )
        }.filterNotNull()  // 过滤掉null值（通配符列）
    }

    /**
     * 查找SQL列引用对应的配置映射信息
     * 用于获取类型信息和目标列名
     */
    private fun findCorrespondingMapping(
        sourceColumnRef: ColumnReference,
        columnMappings: List<DataExchangeColumnMapping>
    ): DataExchangeColumnMapping? {
        val cleanSourceColumnName = cleanColumnName(sourceColumnRef.name)
        
        return columnMappings.find { mapping ->
            val cleanMappingColumnName = cleanColumnName(mapping.srcColumnName)
            
            // 匹配策略1: 清理后的列名完全匹配
            cleanMappingColumnName == cleanSourceColumnName ||
            // 匹配策略2: 原始表达式匹配
            sourceColumnRef.originalExpression.equals(mapping.srcColumnName, ignoreCase = true) ||
            // 匹配策略3: 考虑表前缀的匹配
            (sourceColumnRef.tablePrefix != null && 
             mapping.srcColumnName.equals("${sourceColumnRef.tablePrefix}.${sourceColumnRef.name}", ignoreCase = true))
        }
    }

    /**
     * 构建SQL别名到真实表的映射
     * 使用完整的SQL解析结果来建立准确的映射关系
     */
    private fun buildSqlAliasMapping(
        sqlParseResult: SqlParseInfo,
        sourceTables: List<TableInfo>
    ): Map<String, TableInfo> {
        val aliasToTableMap = mutableMapOf<String, TableInfo>()
        
        log.debug("开始构建SQL别名映射，可用表: ${sourceTables.map { "${it.tableName} (${it.getFullyQualifiedName()})" }}")
        
        // 首先从解析出的表信息中建立别名映射，这比从列引用推断更准确
        // 注意：这需要我们能够访问到解析出的表引用信息，包括别名
        // 但是目前的SqlParseInfo只包含TableInfo，没有原始的TableReference
        // 所以我们需要从列引用中推断
        
        // 从列引用中提取所有的表前缀
        val tablePrefixes = sqlParseResult.sourceColumns.mapNotNull { it.tablePrefix }.distinct()
        
        log.debug("从SQL列引用中提取的表前缀: $tablePrefixes")
        
        tablePrefixes.forEach { prefix ->
            // 尝试多种匹配策略找到对应的TableInfo实例
            val matchedTable = sourceTables.find { table ->
                // 策略1: 直接匹配表名（处理无别名情况或者别名就是表名）
                table.tableName.equals(prefix, ignoreCase = true) ||
                // 策略2: 匹配schema.table格式
                (table.schema != null && "${table.schema}.${table.tableName}".equals(prefix, ignoreCase = true)) ||
                // 策略3: 匹配database.schema.table格式
                table.getFullyQualifiedName().equals(prefix, ignoreCase = true) ||
                // 策略4: 匹配database.table格式（无schema）
                "${table.database.databaseName}.${table.tableName}".equals(prefix, ignoreCase = true)
            }
            
            if (matchedTable != null) {
                aliasToTableMap[prefix] = matchedTable
                log.info("映射表别名/前缀 '$prefix' -> ${matchedTable.getFullyQualifiedName()}")
            } else {
                log.warn("无法找到表前缀 '$prefix' 对应的表")
                log.warn("可用的源表: ${sourceTables.map { it.tableName }}")
                
                // 尝试模糊匹配作为最后手段
                val fuzzyMatchedTable = sourceTables.find { table ->
                    table.tableName.contains(prefix, ignoreCase = true) ||
                    prefix.contains(table.tableName, ignoreCase = true)
                }
                
                if (fuzzyMatchedTable != null) {
                    aliasToTableMap[prefix] = fuzzyMatchedTable
                    log.warn("使用模糊匹配：表前缀 '$prefix' -> ${fuzzyMatchedTable.tableName}")
                }
            }
        }
        
        log.debug("构建的别名映射: $aliasToTableMap")
        return aliasToTableMap
    }

    /**
     * 找到列对应的确切TableInfo实例
     * 这个方法确保返回的TableInfo实例与sourceTables中的实例完全一致
     */
    private fun findExactSourceTableInstance(
        srcColumnName: String,
        sourceTables: List<TableInfo>,
        sqlColumnReferences: List<ColumnReference>,
        tableNameToInstanceMap: Map<String, TableInfo>,
        schemaTableNameToInstanceMap: Map<String, TableInfo>,
        sqlAliasToTableMap: Map<String, TableInfo>,
        warnings: MutableList<String>
    ): TableInfo {
        log.debug("查找列 '$srcColumnName' 的源表，可用表: ${sourceTables.map { it.tableName }}")
        log.debug("SQL列引用: ${sqlColumnReferences.map { "${it.name} (tablePrefix: ${it.tablePrefix})" }}")
        log.debug("SQL别名映射: $sqlAliasToTableMap")
        
        // 1. 优先级最高：从SQL解析的列信息中精确匹配
        val cleanSrcColumnName = cleanColumnName(srcColumnName)
        
        // 改进列匹配逻辑：支持多种匹配方式
        val matchingColumnRef = sqlColumnReferences.find { col: ColumnReference ->
            // 匹配策略1: 清理后的列名完全匹配
            cleanColumnName(col.name) == cleanSrcColumnName ||
            // 匹配策略2: 原始表达式包含列名（处理别名和函数）
            col.originalExpression.contains(cleanSrcColumnName, ignoreCase = true) ||
            // 匹配策略3: srcColumnName本身就包含表前缀，如 "u.id"
            (srcColumnName.contains(".") && col.originalExpression.equals(srcColumnName, ignoreCase = true)) ||
            // 匹配策略4: 匹配列名部分（不考虑表前缀）
            col.name.equals(cleanSrcColumnName, ignoreCase = true)
        }

        if (matchingColumnRef?.tablePrefix != null) {
            // 从SQL别名映射中查找确切的TableInfo实例
            val tableFromAlias = sqlAliasToTableMap[matchingColumnRef.tablePrefix]
            if (tableFromAlias != null) {
                log.info("列 '$srcColumnName' 通过SQL解析匹配到表: ${tableFromAlias.tableName} (别名: ${matchingColumnRef.tablePrefix})")
                return tableFromAlias
            }
            
            // 如果别名映射失败，尝试直接查找表名
            val tableFromName = tableNameToInstanceMap[matchingColumnRef.tablePrefix]
            if (tableFromName != null) {
                log.info("列 '$srcColumnName' 通过表名匹配到表: ${tableFromName.tableName}")
                return tableFromName
            }
            
            warnings.add("SQL解析找到表前缀 '${matchingColumnRef.tablePrefix}' 但无法映射到具体表: $srcColumnName")
        } else if (matchingColumnRef != null) {
            // 找到了列但没有表前缀，可能是单表查询
            if (sourceTables.size == 1) {
                log.info("列 '$srcColumnName' 在单表查询中匹配到表: ${sourceTables[0].tableName}")
                return sourceTables[0]
            } else {
                warnings.add("列 '$srcColumnName' 在SQL中没有表前缀，多表情况下无法确定所属表")
            }
        }

        // 2. 第二优先级：从列名前缀匹配（如 table1.column_name）
        if (srcColumnName.contains(".")) {
            val tablePrefix = srcColumnName.substringBefore(".")
            log.debug("尝试通过列名前缀 '$tablePrefix' 查找表")
            
            // 尝试SQL别名映射
            val tableFromAlias = sqlAliasToTableMap[tablePrefix]
            if (tableFromAlias != null) {
                log.info("列 '$srcColumnName' 通过列名前缀别名匹配到表: ${tableFromAlias.tableName}")
                return tableFromAlias
            }
            
            // 尝试schema.table匹配
            val tableFromSchemaName = schemaTableNameToInstanceMap[tablePrefix]
            if (tableFromSchemaName != null) {
                log.info("列 '$srcColumnName' 通过schema.table前缀匹配到表: ${tableFromSchemaName.getFullyQualifiedName()}")
                return tableFromSchemaName
            }
            
            // 尝试简单表名匹配
            val tableFromSimpleName = tableNameToInstanceMap[tablePrefix]
            if (tableFromSimpleName != null) {
                log.info("列 '$srcColumnName' 通过表名前缀匹配到表: ${tableFromSimpleName.tableName}")
                return tableFromSimpleName
            }
            
            warnings.add("列名前缀 '$tablePrefix' 无法匹配到任何表: $srcColumnName")
        }

        // 3. 最后降级方案：使用第一个源表实例
        if (sourceTables.isNotEmpty()) {
            val defaultTable = sourceTables[0]
            if (sourceTables.size > 1) {
                warnings.add("无法确定列 '$srcColumnName' 所属表，默认使用第一个表: ${defaultTable.getFullyQualifiedName()}")
            } else {
                log.info("列 '$srcColumnName' 使用唯一的源表: ${defaultTable.tableName}")
            }
            return defaultTable
        }

        // 4. 异常情况：没有任何源表
        throw IllegalStateException("没有可用的源表用于列: $srcColumnName")
    }


    /**
     * 清理列名，去除表前缀和引号
     */
    private fun cleanColumnName(columnName: String): String {
        var clean = columnName.trim()
        
        // 去除表前缀（如果存在）
        if (clean.contains(".")) {
            clean = clean.substringAfter(".")
        }
        
        // 去除引号和反引号
        clean = clean.removeSurrounding("`").removeSurrounding("\"").removeSurrounding("'")
        
        return clean
    }

    /**
     * 根据SQL语句确定血缘类型
     */
    private fun determineLineageType(sql: String): LineageType {
        val normalizedSql = sql.trim().lowercase()

        return when {
            normalizedSql.contains("join") -> LineageType.JOIN
            normalizedSql.contains("group by") ||
                    normalizedSql.contains("count(") ||
                    normalizedSql.contains("sum(") ||
                    normalizedSql.contains("avg(") -> LineageType.AGGREGATION

            normalizedSql.contains("where") -> LineageType.FILTER
            normalizedSql.contains("case when") ||
                    normalizedSql.contains("substring") ||
                    normalizedSql.contains("concat") -> LineageType.COMPLEX_TRANSFORMATION

            normalizedSql.startsWith("select") &&
                    normalizedSql.contains("from") -> LineageType.SQL_QUERY

            else -> LineageType.DIRECT_COPY
        }
    }

    /**
     * 创建数据转换信息
     */
    private fun createTransformation(mapping: DataExchangeColumnMapping): DataTransformation? {
        // 检查是否存在类型转换
        if (mapping.srcColumnType != mapping.dstColumnType) {
            return DataTransformation(
                transformationType = TransformationType.TYPE_CAST,
                description = "类型转换: ${mapping.srcColumnType} -> ${mapping.dstColumnType}",
                expression = null
            )
        }

        // 检查是否存在列名变化
        if (mapping.srcColumnName != mapping.dstColumnName) {
            return DataTransformation(
                transformationType = TransformationType.NONE,
                description = "列名映射: ${mapping.srcColumnName} -> ${mapping.dstColumnName}",
                expression = null
            )
        }

        // 无转换
        return null
    }

    /**
     * 验证列映射的完整性并添加警告
     */
    private fun validateColumnMappings(columns: List<DataExchangeColumnMapping>, warnings: MutableList<String>) {
        // 检查列索引是否连续
        val indices = columns.map { it.columnIndex }.sorted()
        val expectedIndices = columns.indices.toList()

        if (indices != expectedIndices) {
            warnings.add("列索引不连续或不从0开始: $indices")
        }

        // 检查是否有重复的源列名
        val duplicateSourceColumns = columns.groupBy { it.srcColumnName }
            .filterValues { it.size > 1 }
            .keys

        if (duplicateSourceColumns.isNotEmpty()) {
            warnings.add("发现重复的源列名: $duplicateSourceColumns")
        }

        // 检查是否有重复的目标列名
        val duplicateTargetColumns = columns.groupBy { it.dstColumnName }
            .filterValues { it.size > 1 }
            .keys

        if (duplicateTargetColumns.isNotEmpty()) {
            warnings.add("发现重复的目标列名: $duplicateTargetColumns")
        }
    }

    /**
     * 根据数据库类型确定实际的schema值
     * 默认情况下，a.b中的a被视为数据库名，不是schema
     * PostgreSQL和Oracle明确支持schema概念时才保留作为schema
     */
    private fun determineSchemaForDatabaseType(dbType: String, schemaOrDatabase: String?): String? {
        return when (dbType.lowercase()) {
            "postgresql", "oracle" -> {
                // PostgreSQL和Oracle明确支持schema概念，保持原有逻辑
                schemaOrDatabase
            }
            else -> {
                // 其他所有数据库类型（Hive/MySQL等），默认将a.b中的a视为数据库名
                null
            }
        }
    }
} 