package com.datayes.lineage

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.LocalDateTime
import java.util.concurrent.ConcurrentHashMap

/**
 * 任务状态管理器 (Job Status Manager)
 *
 * 基于内存的任务状态管理，防止同一系统类型的任务重复执行
 */
@Service
class JobStatusManager {

    private val logger = LoggerFactory.getLogger(JobStatusManager::class.java)
    
    private val runningJobs = ConcurrentHashMap<String, JobInfo>()

    /**
     * 检查指定系统类型的任务是否正在运行
     *
     * @param systemType 系统类型
     * @return true 如果任务正在运行，false 如果没有运行
     */
    fun isJobRunning(systemType: String): Boolean {
        val isRunning = runningJobs.containsKey(systemType)
        logger.debug("b8f4c21a | 检查任务状态: systemType=$systemType, isRunning=$isRunning")
        return isRunning
    }

    /**
     * 注册新的运行任务
     *
     * @param systemType 系统类型
     * @param executedBy 执行者
     * @return true 如果成功注册，false 如果任务已在运行
     */
    fun registerRunningJob(systemType: String, executedBy: String): Boolean {
        val jobInfo = JobInfo(
            systemType = systemType,
            executedBy = executedBy,
            startTime = LocalDateTime.now()
        )
        
        val previousJob = runningJobs.putIfAbsent(systemType, jobInfo)
        val registered = previousJob == null
        
        if (registered) {
            logger.info("3e7a9f2d | 任务已注册为运行状态: systemType=$systemType, executedBy=$executedBy")
        } else {
            logger.warn("7c4b5e8f | 任务注册失败，已有任务在运行: systemType=$systemType, 当前执行者=$executedBy, 已运行任务=${previousJob?.executedBy}")
        }
        
        return registered
    }

    /**
     * 取消注册运行任务
     *
     * @param systemType 系统类型
     */
    fun unregisterRunningJob(systemType: String) {
        val removedJob = runningJobs.remove(systemType)
        if (removedJob != null) {
            val duration = java.time.Duration.between(removedJob.startTime, LocalDateTime.now())
            logger.info("9a2c6d1e | 任务已取消注册: systemType=$systemType, executedBy=${removedJob.executedBy}, 运行时长=${duration.toMinutes()}分钟")
        } else {
            logger.debug("5f8d3a7b | 尝试取消注册不存在的任务: systemType=$systemType")
        }
    }

    /**
     * 获取当前正在运行的任务信息
     *
     * @param systemType 系统类型
     * @return 任务信息，如果不存在返回null
     */
    fun getRunningJobInfo(systemType: String): JobInfo? {
        return runningJobs[systemType]
    }

    /**
     * 获取所有正在运行的任务
     *
     * @return 所有正在运行的任务信息
     */
    fun getAllRunningJobs(): Map<String, JobInfo> {
        return runningJobs.toMap()
    }

    /**
     * 清理所有运行状态 (主要用于测试或紧急情况)
     */
    fun clearAllRunningJobs() {
        val count = runningJobs.size
        runningJobs.clear()
        logger.warn("f2e8b4c7 | 已清理所有运行状态: 清理任务数量=$count")
    }
}

/**
 * 任务信息
 */
data class JobInfo(
    val systemType: String,
    val executedBy: String,
    val startTime: LocalDateTime
)