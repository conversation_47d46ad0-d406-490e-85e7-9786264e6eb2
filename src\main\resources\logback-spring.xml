<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

<!--    <property name="LOG_PATTERN"-->
<!--              value="%d{HH:mm:ss.SSS} [%thread] [%X{requestId}] %-5level %logger{36} [%file:%line] - %msg%n"/>-->
    <property name="LOG_PATTERN"
              value="%d{HH:mm:ss.SSS} [%thread] [%X{requestId}] %-5level [%file:%line] - %msg%n"/>

    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <appender name="debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>./logs/debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>./logs/debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <charset>UTF-8</charset>
            <pattern>%date [%thread] [%level] %green(%X{traceId}) %logger{5} Line:%L - %msg %n</pattern>
        </encoder>
    </appender>

    <appender name="MEMORY" class="com.datayes.util.InMemoryLogAppender">
        <!-- 设置调用者数据，这样可以获取到方法名和行号 -->
        <includeCallerData>true</includeCallerData>
    </appender>

    <logger name="com.datayes" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="MEMORY"/>
        <appender-ref ref="debug"/>
    </logger>

    <root level="WARN">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="MEMORY"/>
    </root>
</configuration>
