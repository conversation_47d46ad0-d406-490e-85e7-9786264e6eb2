package com.datayes.task

import com.fasterxml.jackson.databind.JsonNode
import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.time.LocalDateTime
import java.util.*

/**
 * 手动触发血缘任务实体 (Manual Lineage Trigger Task Entity)
 *
 * 使用 Spring Data JDBC 映射到 manual_lineage_trigger_tasks 表
 */
@Table("manual_lineage_trigger_tasks")
data class ManualLineageTriggerTask(

    @Id
    val id: Long = 0,

    @Column("task_uuid")
    val taskUuid: String = UUID.randomUUID().toString(),

    @Column("datasource_id")
    val datasourceId: Long,

    @Column("trigger_user")
    val triggerUser: String? = null,

    @Column("task_status")
    val taskStatus: ManualTaskStatus = ManualTaskStatus.PENDING,

    @Column("started_at")
    val startedAt: LocalDateTime? = null,

    @Column("completed_at")
    val completedAt: LocalDateTime? = null,

    @Column("execution_time_ms")
    val executionTimeMs: Long? = null,

    @Column("success_count")
    val successCount: Int = 0,

    @Column("failure_count")
    val failureCount: Int = 0,

    @Column("total_count")
    val totalCount: Int = 0,

    @Column("error_message")
    val errorMessage: String? = null,

    @Column("error_details")
    val errorDetails: JsonNode? = null,

    @Column("success_results")
    val successResults: JsonNode? = null,

    @Column("failure_results")
    val failureResults: JsonNode? = null,

    @Column("created_at")
    val createdAt: LocalDateTime = LocalDateTime.now(),

    @Column("updated_at")
    val updatedAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 手动任务状态枚举 (Manual Task Status Enum)
 */
enum class ManualTaskStatus {
    PENDING,    // 待执行
    RUNNING,    // 执行中
    SUCCESS,    // 执行成功
    FAILED,     // 执行失败
    CANCELLED   // 已取消
}

/**
 * 手动触发血缘任务请求 (Manual Lineage Trigger Request)
 */
data class ManualLineageTriggerRequest(
    val datasourceId: Long,
    val triggerUser: String? = null
)

/**
 * 批量手动触发血缘任务请求 (Batch Manual Lineage Trigger Request)
 */
data class BatchManualLineageTriggerRequest(
    val datasourceIds: List<Long>,
    val triggerUser: String? = null
)

/**
 * 手动触发血缘任务响应 (Manual Lineage Trigger Response)
 */
data class ManualLineageTriggerResponse(
    val taskUuid: String,
    val datasourceId: Long,
    val taskStatus: ManualTaskStatus,
    val message: String,
    val createdAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 批量手动触发血缘任务响应 (Batch Manual Lineage Trigger Response)
 */
data class BatchManualLineageTriggerResponse(
    val taskUuids: List<String>,
    val datasourceIds: List<Long>,
    val triggerUser: String?,
    val totalTasks: Int,
    val message: String,
    val createdAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 任务结果项 (Task Result Item)
 */
data class TaskResultItem(
    val itemType: String,      // TABLE, SCRIPT, JOB等
    val itemId: String,        // 具体项目的标识
    val itemName: String,      // 项目名称
    val message: String,       // 处理消息
    val processedAt: LocalDateTime = LocalDateTime.now()
)

/**
 * 手动任务查询条件 (Manual Task Query Criteria)
 */
data class ManualTaskQueryCriteria(
    val datasourceId: Long? = null,
    val triggerUser: String? = null,
    val taskStatus: ManualTaskStatus? = null,
    val startDate: LocalDateTime? = null,
    val endDate: LocalDateTime? = null,
    val page: Int = 0,
    val size: Int = 20
)

/**
 * 手动任务详情响应 (Manual Task Detail Response)
 */
data class ManualTaskDetailResponse(
    val id: Long,
    val taskUuid: String,
    val datasourceId: Long,
    val datasourceName: String? = null,
    val triggerUser: String?,
    val taskStatus: ManualTaskStatus,
    val startedAt: LocalDateTime?,
    val completedAt: LocalDateTime?,
    val executionTimeMs: Long?,
    val successCount: Int,
    val failureCount: Int,
    val totalCount: Int,
    val errorMessage: String?,
    val successResults: List<TaskResultItem> = emptyList(),
    val failureResults: List<TaskResultItem> = emptyList(),
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime
)

/**
 * 手动任务列表响应 (Manual Task List Response)
 */
data class ManualTaskListResponse(
    val tasks: List<ManualTaskSummary>,
    val totalElements: Long,
    val totalPages: Int,
    val currentPage: Int,
    val size: Int
)

/**
 * 手动任务摘要 (Manual Task Summary)
 */
data class ManualTaskSummary(
    val id: Long,
    val taskUuid: String,
    val datasourceId: Long,
    val datasourceName: String? = null,
    val triggerUser: String?,
    val taskStatus: ManualTaskStatus,
    val successCount: Int,
    val failureCount: Int,
    val totalCount: Int,
    val executionTimeMs: Long?,
    val createdAt: LocalDateTime,
    val completedAt: LocalDateTime?
)

/**
 * 增强的手动任务触发响应 (Enhanced Manual Task Trigger Response)
 * 
 * 包含任务摘要和实际执行的任务详情
 */
data class ManualTaskTriggerResponse(
    val taskSummary: ManualTaskSummary,
    val executedTask: ManualLineageTriggerTask,
    val executedLineageTasks: List<ExecutedLineageTaskDetail> = emptyList(),
    val message: String = "任务执行完成"
)

/**
 * 执行的血缘任务详情 (Executed Lineage Task Detail)
 */
data class ExecutedLineageTaskDetail(
    val taskId: Long,
    val taskName: String,
    val taskType: String,
    val jobKey: String?,
    val sourceType: String,
    val sourceIdentifier: String?,
    val taskStatus: String,
    val processingTimeMs: Long?,
    val hasChanges: Boolean? = false,
    val errorMessage: String? = null,
    val executionId: String? = null,
    val executedAt: LocalDateTime?,
    val completedAt: LocalDateTime?
)