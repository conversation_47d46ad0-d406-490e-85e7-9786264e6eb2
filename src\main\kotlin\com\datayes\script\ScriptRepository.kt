package com.datayes.script

import org.springframework.dao.EmptyResultDataAccessException
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.jdbc.core.RowMapper
import org.springframework.jdbc.support.GeneratedKeyHolder
import org.springframework.stereotype.Repository
import java.sql.PreparedStatement
import java.sql.Statement
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * 脚本数据访问层 (Script Repository)
 *
 * 负责上传脚本的数据库操作
 */
@Repository
class ScriptRepository(private val jdbcTemplate: JdbcTemplate) {

    private val rowMapper = RowMapper<UploadedScript> { rs, _ ->
        UploadedScript(
            id = rs.getLong("id"),
            scriptName = rs.getString("script_name"),
            scriptType = ScriptType.valueOf(rs.getString("script_type")),
            filePath = rs.getString("file_path"),
            fileSize = rs.getLong("file_size").takeIf { !rs.wasNull() },
            fileHash = rs.getString("file_hash"),
            scriptContent = rs.getString("script_content"),
            uploadUser = rs.getString("upload_user"),
            analysisStatus = AnalysisStatus.valueOf(rs.getString("analysis_status")),
            analysisResult = rs.getString("analysis_result"),
            temporaryLineageId = rs.getString("temporary_lineage_id"),
            createdAt = rs.getTimestamp("created_at").toLocalDateTime(),
            updatedAt = rs.getTimestamp("updated_at").toLocalDateTime()
        )
    }

    /**
     * 保存上传的脚本 (Save uploaded script)
     */
    fun save(script: UploadedScript): UploadedScript {
        val sql = """
            INSERT INTO uploaded_scripts (
                script_name, script_type, file_path, file_size, file_hash,
                script_content, upload_user, analysis_status, analysis_result,
                temporary_lineage_id, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """.trimIndent()

        val keyHolder = GeneratedKeyHolder()

        jdbcTemplate.update({ connection ->
            val ps: PreparedStatement = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)
            ps.setString(1, script.scriptName)
            ps.setString(2, script.scriptType.name)
            ps.setString(3, script.filePath)
            script.fileSize?.let { ps.setLong(4, it) } ?: ps.setNull(4, java.sql.Types.BIGINT)
            ps.setString(5, script.fileHash)
            ps.setString(6, script.scriptContent)
            ps.setString(7, script.uploadUser)
            ps.setString(8, script.analysisStatus.name)
            ps.setString(9, script.analysisResult)
            ps.setString(10, script.temporaryLineageId)
            ps.setTimestamp(11, java.sql.Timestamp.valueOf(script.createdAt))
            ps.setTimestamp(12, java.sql.Timestamp.valueOf(script.updatedAt))
            ps
        }, keyHolder)

        val generatedId = keyHolder.key?.toLong()
            ?: throw RuntimeException("保存脚本失败，无法获取生成的ID (Failed to save script, could not get generated ID)")

        return script.copy(id = generatedId)
    }

    /**
     * 根据ID查找脚本 (Find script by ID)
     */
    fun findById(id: Long): UploadedScript? {
        val sql = """
            SELECT id, script_name, script_type, file_path, file_size, file_hash,
                   script_content, upload_user, analysis_status, analysis_result,
                   temporary_lineage_id, created_at, updated_at
            FROM uploaded_scripts 
            WHERE id = ?
        """.trimIndent()

        return jdbcTemplate.queryForObject(sql, rowMapper, id)
    }

    /**
     * 分页查询脚本列表 (Find scripts with pagination and filtering)
     */
    fun findScripts(
        scriptName: String? = null,
        uploadUser: String? = null,
        startDate: LocalDateTime? = null,
        endDate: LocalDateTime? = null,
        scriptType: ScriptType? = null,
        analysisStatus: AnalysisStatus? = null,
        page: Int = 0,
        size: Int = 20
    ): ScriptQueryResult {
        val conditions = mutableListOf<String>()
        val params = mutableListOf<Any>()

        // 构建动态查询条件 (Build dynamic query conditions)
        scriptName?.let {
            conditions.add("script_name LIKE ?")
            params.add("%$it%")
        }

        uploadUser?.let {
            conditions.add("upload_user = ?")
            params.add(it)
        }

        startDate?.let {
            conditions.add("created_at >= ?")
            params.add(it)
        }

        endDate?.let {
            conditions.add("created_at <= ?")
            params.add(it)
        }

        scriptType?.let {
            conditions.add("script_type = ?")
            params.add(it.name)
        }

        analysisStatus?.let {
            conditions.add("analysis_status = ?")
            params.add(it.name)
        }

        val whereClause = if (conditions.isNotEmpty()) {
            "WHERE ${conditions.joinToString(" AND ")}"
        } else ""

        // 查询总数 (Query total count)
        val countSql = "SELECT COUNT(*) FROM uploaded_scripts $whereClause"
        val totalElements = jdbcTemplate.queryForObject(countSql, Long::class.java, *params.toTypedArray()) ?: 0L

        // 分页查询数据 (Query paginated data)
        val dataSql = """
            SELECT id, script_name, script_type, file_path, file_size, file_hash,
                   script_content, upload_user, analysis_status, analysis_result,
                   temporary_lineage_id, created_at, updated_at
            FROM uploaded_scripts 
            $whereClause
            ORDER BY created_at DESC
            LIMIT ? OFFSET ?
        """.trimIndent()

        val offset = page * size
        val scripts = jdbcTemplate.query(dataSql, rowMapper, *(params + listOf(size, offset)).toTypedArray())

        return ScriptQueryResult(
            content = scripts,
            page = page,
            size = size,
            totalElements = totalElements,
            totalPages = ((totalElements + size - 1) / size).toInt()
        )
    }

    /**
     * 根据文件哈希查找脚本 (Find script by file hash)
     */
    fun findByFileHash(fileHash: String): UploadedScript? {
        val sql = """
            SELECT id, script_name, script_type, file_path, file_size, file_hash,
                   script_content, upload_user, analysis_status, analysis_result,
                   temporary_lineage_id, created_at, updated_at
            FROM uploaded_scripts 
            WHERE file_hash = ?
            ORDER BY created_at DESC
            LIMIT 1
        """.trimIndent()

        return try {
            jdbcTemplate.queryForObject(sql, rowMapper, fileHash)
        } catch (e: EmptyResultDataAccessException) {
            null
        }
    }

    /**
     * 更新脚本分析状态 (Update script analysis status)
     */
    fun updateAnalysisStatus(id: Long, status: AnalysisStatus, result: String? = null): Boolean {
        val sql = """
            UPDATE uploaded_scripts 
            SET analysis_status = ?, analysis_result = ?, updated_at = ?
            WHERE id = ?
        """.trimIndent()

        val rowsUpdated = jdbcTemplate.update(
            sql,
            status.name,
            result,
            LocalDateTime.now(),
            id
        )

        return rowsUpdated > 0
    }

    /**
     * 根据临时血缘ID查找脚本 (Find script by temporary lineage ID)
     */
    fun findByTemporaryLineageId(temporaryLineageId: String): UploadedScript? {
        val sql = """
            SELECT id, script_name, script_type, file_path, file_size, file_hash,
                   script_content, upload_user, analysis_status, analysis_result,
                   temporary_lineage_id, created_at, updated_at
            FROM uploaded_scripts 
            WHERE temporary_lineage_id = ?
        """.trimIndent()

        return try {
            jdbcTemplate.queryForObject(sql, rowMapper, temporaryLineageId)
        } catch (e: EmptyResultDataAccessException) {
            null
        }
    }

    /**
     * 删除脚本 (Delete script)
     */
    fun deleteById(id: Long): Boolean {
        val sql = "DELETE FROM uploaded_scripts WHERE id = ?"
        val rowsDeleted = jdbcTemplate.update(sql, id)
        return rowsDeleted > 0
    }
}

/**
 * 脚本查询结果 (Script Query Result)
 */
data class ScriptQueryResult(
    val content: List<UploadedScript>,
    val page: Int,
    val size: Int,
    val totalElements: Long,
    val totalPages: Int
) 