package com.datayes.task

import com.datayes.lineage.*
import com.datayes.metadata.MetadataService
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.apache.poi.ss.usermodel.*
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.apache.poi.hssf.usermodel.HSSFWorkbook
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.time.LocalDateTime

/**
 * 手动血缘导入服务 (Manual Lineage Import Service)
 *
 * 负责解析上传的文件并将其转换为DataLineage实体
 * 支持JSON、CSV和Excel格式的血缘数据文件
 */
@Service
class ManualLineageImportService(
    private val objectMapper: ObjectMapper,
    private val lineageService: LineageService,
    private val metadataService: MetadataService
) {

    private val logger = LoggerFactory.getLogger(ManualLineageImportService::class.java)

    // 支持的数据库类型列表 (Supported database types)
    private val supportedDbTypes = setOf(
        "mysql", "oracle", "postgresql", "postgres", "sqlserver", "mssql",
        "hive2", " hive", " spark", " clickhouse", " mongodb", " redis", " elasticsearch",
        "tidb", "oceanbase", "dm", "kingbase", "gbase", "oscar", "tdsql"
    )

    /**
     * 解析上传的血缘文件 (Parse uploaded lineage file)
     *
     * @param file 上传的文件
     * @return 解析的血缘数据列表
     */
    fun parseLineageFile(file: MultipartFile): List<DataLineage> {
        validateUploadedFile(file)

        val fileName = file.originalFilename!!

        logger.info("4b7e92c1 | 开始解析血缘文件: fileName=$fileName, size=${file.size}")

        return when {
            fileName.endsWith(".json", ignoreCase = true) -> {
                val content = file.inputStream.bufferedReader().use { it.readText() }
                if (content.isBlank()) {
                    throw IllegalArgumentException("文件内容不能为空")
                }
                parseJsonFile(content, fileName)
            }

            fileName.endsWith(".csv", ignoreCase = true) -> {
                val content = file.inputStream.bufferedReader().use { it.readText() }
                if (content.isBlank()) {
                    throw IllegalArgumentException("文件内容不能为空")
                }
                parseCsvFile(content, fileName)
            }

            fileName.endsWith(".xlsx", ignoreCase = true) || fileName.endsWith(".xls", ignoreCase = true) -> {
                parseExcelFile(file, fileName)
            }

            else -> throw IllegalArgumentException("不支持的文件格式: $fileName. 仅支持 .json、.csv、.xlsx 和 .xls 格式")
        }
    }

    /**
     * 验证上传的文件 (Validate uploaded file)
     */
    private fun validateUploadedFile(file: MultipartFile) {
        if (file.isEmpty) {
            throw IllegalArgumentException("上传文件不能为空")
        }

        val fileName = file.originalFilename
        if (fileName.isNullOrBlank()) {
            throw IllegalArgumentException("文件名不能为空")
        }

        val maxFileSize = 10 * 1024 * 1024 // 10MB
        if (file.size > maxFileSize) {
            throw IllegalArgumentException("文件大小不能超过10MB，当前文件大小: ${file.size / 1024 / 1024}MB")
        }

        val supportedExtensions = listOf(".json", ".csv", ".xlsx", ".xls")
        val hasValidExtension = supportedExtensions.any { fileName.endsWith(it, ignoreCase = true) }
        if (!hasValidExtension) {
            throw IllegalArgumentException("不支持的文件格式: $fileName. 仅支持 ${supportedExtensions.joinToString(", ")} 格式")
        }

        logger.info("d3a8f5e1 | 文件验证通过: fileName=$fileName, size=${file.size}")
    }

    /**
     * 解析JSON格式的血缘文件 (Parse JSON lineage file)
     */
    private fun parseJsonFile(content: String, fileName: String): List<DataLineage> {
        return try {
            val jsonNode = objectMapper.readTree(content)

            when {
                jsonNode.isArray -> {
                    jsonNode.map { parseJsonLineage(it, fileName) }
                }

                jsonNode.isObject -> {
                    if (jsonNode.has("lineages") && jsonNode["lineages"].isArray) {
                        jsonNode["lineages"].map { parseJsonLineage(it, fileName) }
                    } else {
                        listOf(parseJsonLineage(jsonNode, fileName))
                    }
                }

                else -> throw IllegalArgumentException("JSON格式错误: 根节点必须是对象或数组")
            }
        } catch (e: Exception) {
            logger.error("8f3a5d67 | 解析JSON文件失败: fileName=$fileName", e)
            throw IllegalArgumentException("JSON文件解析失败: ${e.message}", e)
        }
    }

    /**
     * 解析单个JSON血缘对象 (Parse single JSON lineage object)
     */
    private fun parseJsonLineage(jsonNode: JsonNode, fileName: String): DataLineage {
        try {
            val jobId = jsonNode.get("jobId")?.asText()
                ?: jsonNode.get("job_id")?.asText()
                ?: "manual_import_${System.currentTimeMillis()}"

            val jobName = jsonNode.get("jobName")?.asText()
                ?: jsonNode.get("job_name")?.asText()
                ?: "手动导入-$fileName"

            // 解析源数据库
            val sourceDbNode = jsonNode.get("sourceDatabase") ?: jsonNode.get("source_database")
            ?: throw IllegalArgumentException("缺少源数据库信息 (sourceDatabase)")
            val sourceDatabase = parseJsonDatabaseInfo(sourceDbNode)

            // 解析目标数据库
            val targetDbNode = jsonNode.get("targetDatabase") ?: jsonNode.get("target_database")
            ?: throw IllegalArgumentException("缺少目标数据库信息 (targetDatabase)")
            val targetDatabase = parseJsonDatabaseInfo(targetDbNode)

            // 解析表血缘
            val tableLineageNode = jsonNode.get("tableLineage") ?: jsonNode.get("table_lineage")
            ?: throw IllegalArgumentException("缺少表血缘信息 (tableLineage)")
            val tableLineage = parseJsonTableLineage(tableLineageNode, sourceDatabase, targetDatabase)

            // 解析列血缘
            val columnLineagesNode = jsonNode.get("columnLineages") ?: jsonNode.get("column_lineages")
            val columnLineages = if (columnLineagesNode?.isArray == true) {
                columnLineagesNode.mapIndexed { index, node ->
                    parseJsonColumnLineage(node, tableLineage.sourceTables, tableLineage.targetTable, index)
                }
            } else {
                emptyList()
            }

            val originalSql = jsonNode.get("originalSql")?.asText()
                ?: jsonNode.get("original_sql")?.asText()
                ?: "-- 手动导入的血缘数据"

            return DataLineage(
                jobId = jobId,
                jobName = jobName,
                tableLineage = tableLineage,
                columnLineages = columnLineages,
                sourceDatabase = sourceDatabase,
                targetDatabase = targetDatabase,
                originalSql = originalSql,
                createdAt = LocalDateTime.now()
            )

        } catch (e: Exception) {
            logger.error("9a2c8e14 | 解析JSON血缘对象失败", e)
            throw IllegalArgumentException("JSON血缘对象解析失败: ${e.message}", e)
        }
    }

    /**
     * 解析JSON数据库信息 (Parse JSON database info)
     */
    private fun parseJsonDatabaseInfo(dbNode: JsonNode): DatabaseInfo {
        val dbType = dbNode.get("dbType")?.asText() ?: dbNode.get("db_type")?.asText() ?: "unknown"
        val host = dbNode.get("host")?.asText() ?: "unknown"
        val port = dbNode.get("port")?.asInt() ?: 3306
        val databaseName = dbNode.get("databaseName")?.asText() ?: dbNode.get("database_name")?.asText() ?: "unknown"
        val connectionString = dbNode.get("originalConnectionString")?.asText()
            ?: dbNode.get("original_connection_string")?.asText()
            ?: "jdbc:$dbType://$host:$port/$databaseName"

        return DatabaseInfo(
            dbType = dbType,
            host = host,
            port = port,
            databaseName = databaseName,
            originalConnectionString = connectionString
        )
    }

    /**
     * 解析JSON表血缘 (Parse JSON table lineage)
     */
    private fun parseJsonTableLineage(
        tableNode: JsonNode,
        sourceDatabase: DatabaseInfo,
        targetDatabase: DatabaseInfo
    ): TableLineage {
        val sourceTablesNode = tableNode.get("sourceTables") ?: tableNode.get("source_tables")
        ?: throw IllegalArgumentException("缺少源表信息 (sourceTables)")

        val sourceTables = if (sourceTablesNode.isArray) {
            sourceTablesNode.map { parseJsonTableInfo(it, sourceDatabase) }
        } else {
            listOf(parseJsonTableInfo(sourceTablesNode, sourceDatabase))
        }

        val targetTableNode = tableNode.get("targetTable") ?: tableNode.get("target_table")
        ?: throw IllegalArgumentException("缺少目标表信息 (targetTable)")
        val targetTable = parseJsonTableInfo(targetTableNode, targetDatabase)

        val lineageTypeStr = tableNode.get("lineageType")?.asText()
            ?: tableNode.get("lineage_type")?.asText()
            ?: "DIRECT_COPY"
        val lineageType = try {
            LineageType.valueOf(lineageTypeStr.uppercase())
        } catch (e: IllegalArgumentException) {
            LineageType.DIRECT_COPY
        }

        return TableLineage(
            sourceTables = sourceTables,
            targetTable = targetTable,
            lineageType = lineageType
        )
    }

    /**
     * 解析JSON表信息 (Parse JSON table info)
     */
    private fun parseJsonTableInfo(tableNode: JsonNode, database: DatabaseInfo): TableInfo {
        val tableName = tableNode.get("tableName")?.asText()
            ?: tableNode.get("table_name")?.asText()
            ?: tableNode.asText()
            ?: throw IllegalArgumentException("缺少表名")

        val schema = tableNode.get("schema")?.asText()

        return TableInfo(
            schema = schema,
            tableName = tableName,
            database = database
        )
    }

    /**
     * 解析JSON列血缘 (Parse JSON column lineage)
     */
    private fun parseJsonColumnLineage(
        columnNode: JsonNode,
        sourceTables: List<TableInfo>,
        targetTable: TableInfo,
        index: Int
    ): ColumnLineage {
        val sourceColumnNode = columnNode.get("sourceColumn") ?: columnNode.get("source_column")
        ?: throw IllegalArgumentException("缺少源列信息 (sourceColumn)")
        val sourceColumn = parseJsonColumnInfo(sourceColumnNode, sourceTables)

        val targetColumnNode = columnNode.get("targetColumn") ?: columnNode.get("target_column")
        ?: throw IllegalArgumentException("缺少目标列信息 (targetColumn)")
        val targetColumn = parseJsonColumnInfo(targetColumnNode, listOf(targetTable))

        val transformationNode = columnNode.get("transformation")
        val transformation = if (transformationNode != null) {
            parseJsonTransformation(transformationNode)
        } else null

        return ColumnLineage(
            sourceColumn = sourceColumn,
            targetColumn = targetColumn,
            transformation = transformation,
            columnIndex = index
        )
    }

    /**
     * 解析JSON列信息 (Parse JSON column info)
     */
    private fun parseJsonColumnInfo(columnNode: JsonNode, tables: List<TableInfo>): ColumnInfo {
        val columnName = columnNode.get("columnName")?.asText()
            ?: columnNode.get("column_name")?.asText()
            ?: columnNode.asText()
            ?: throw IllegalArgumentException("缺少列名")

        val dataType = columnNode.get("dataType")?.asText() ?: columnNode.get("data_type")?.asText() ?: "VARCHAR"
        val comment = columnNode.get("comment")?.asText()

        val tableName = columnNode.get("tableName")?.asText() ?: columnNode.get("table_name")?.asText()
        val table = if (tableName != null) {
            tables.find { it.tableName == tableName }
                ?: throw IllegalArgumentException("找不到表: $tableName")
        } else {
            tables.firstOrNull() ?: throw IllegalArgumentException("无法确定列所属的表")
        }

        return ColumnInfo(
            columnName = columnName,
            dataType = dataType,
            comment = comment,
            table = table
        )
    }

    /**
     * 解析JSON转换信息 (Parse JSON transformation)
     */
    private fun parseJsonTransformation(transformationNode: JsonNode): DataTransformation {
        val typeStr = transformationNode.get("transformationType")?.asText()
            ?: transformationNode.get("transformation_type")?.asText()
            ?: "NONE"

        val transformationType = try {
            TransformationType.valueOf(typeStr.uppercase())
        } catch (e: IllegalArgumentException) {
            TransformationType.NONE
        }

        val description = transformationNode.get("description")?.asText() ?: "手动导入的转换"
        val expression = transformationNode.get("expression")?.asText()

        return DataTransformation(
            transformationType = transformationType,
            description = description,
            expression = expression
        )
    }

    /**
     * 解析CSV格式的血缘文件 (Parse CSV lineage file)
     */
    private fun parseCsvFile(content: String, fileName: String): List<DataLineage> {
        logger.info("6e8d9a42 | 开始解析CSV格式血缘文件: fileName=$fileName")

        val lines = content.split("\n").map { it.trim() }.filter { it.isNotEmpty() }
        if (lines.isEmpty()) {
            throw IllegalArgumentException("CSV文件为空")
        }

        if (lines.size < 2) {
            throw IllegalArgumentException("CSV文件必须包含标题行和至少一行数据")
        }

        val header = lines.first().split(",").map { it.trim() }
        if (header.isEmpty()) {
            throw IllegalArgumentException("CSV文件标题行不能为空")
        }

        val dataLines = lines.drop(1)
        if (dataLines.isEmpty()) {
            throw IllegalArgumentException("CSV文件必须包含至少一行数据")
        }

        validateCsvHeaders(header)

        logger.info("2f5b7c83 | CSV文件解析: 标题行=${header.joinToString(",")}, 数据行数=${dataLines.size}")

        return dataLines.mapIndexed { index, line ->
            try {
                parseCsvLineage(line, header, fileName, index)
            } catch (e: Exception) {
                logger.error("1a3e8f95 | 解析CSV第${index + 2}行失败: line=$line", e)
                throw IllegalArgumentException("CSV第${index + 2}行解析失败: ${e.message}", e)
            }
        }
    }

    /**
     * 验证CSV标题行 (Validate CSV headers)
     */
    private fun validateCsvHeaders(headers: List<String>) {
        val requiredHeaders = listOf("source_table", "target_table")
        val missingHeaders = requiredHeaders.filter { required ->
            headers.none {
                it.equals(required, ignoreCase = true) || it.equals(
                    required.replace("_", ""),
                    ignoreCase = true
                )
            }
        }

        if (missingHeaders.isNotEmpty()) {
            throw IllegalArgumentException(
                "CSV文件缺少必需的列: ${missingHeaders.joinToString(", ")}。" +
                        "必需列: ${requiredHeaders.joinToString(", ")}"
            )
        }

        logger.info("8e3b4c71 | CSV标题验证通过: headers=${headers.joinToString(",")}")
    }

    /**
     * 解析单行CSV血缘数据 (Parse single CSV lineage row)
     */
    private fun parseCsvLineage(line: String, header: List<String>, fileName: String, index: Int): DataLineage {
        val values = line.split(",").map { it.trim().removeSurrounding("\"") }

        if (values.size != header.size) {
            throw IllegalArgumentException("列数不匹配: 期望${header.size}列，实际${values.size}列")
        }

        val data = header.zip(values).toMap()

        val jobId = data["job_id"] ?: data["jobId"] ?: "csv_import_${System.currentTimeMillis()}_$index"
        val jobName = data["job_name"] ?: data["jobName"] ?: "CSV导入-$fileName-行${index + 2}"

        // 构建简单的数据库信息
        val sourceDbName = data["source_database"] ?: data["sourceDatabase"] ?: "unknown_source"
        val targetDbName = data["target_database"] ?: data["targetDatabase"] ?: "unknown_target"

        val sourceDatabase = DatabaseInfo(
            dbType = data["source_db_type"] ?: "mysql",
            host = data["source_host"] ?: "unknown",
            port = data["source_port"]?.toIntOrNull() ?: 3306,
            databaseName = sourceDbName,
            originalConnectionString = "***************************************"
        )

        val targetDatabase = DatabaseInfo(
            dbType = data["target_db_type"] ?: "mysql",
            host = data["target_host"] ?: "unknown",
            port = data["target_port"]?.toIntOrNull() ?: 3306,
            databaseName = targetDbName,
            originalConnectionString = "***************************************"
        )

        // 构建表血缘
        val sourceTableName = data["source_table"] ?: data["sourceTable"]
        ?: throw IllegalArgumentException("缺少源表名 (source_table)")
        val targetTableName = data["target_table"] ?: data["targetTable"]
        ?: throw IllegalArgumentException("缺少目标表名 (target_table)")

        val sourceTable = TableInfo(
            schema = data["source_schema"],
            tableName = sourceTableName,
            database = sourceDatabase
        )

        val targetTable = TableInfo(
            schema = data["target_schema"],
            tableName = targetTableName,
            database = targetDatabase
        )

        val tableLineage = TableLineage(
            sourceTables = listOf(sourceTable),
            targetTable = targetTable,
            lineageType = LineageType.DIRECT_COPY
        )

        // 构建列血缘（如果有列信息）
        val columnLineages = mutableListOf<ColumnLineage>()
        val sourceColumn = data["source_column"] ?: data["sourceColumn"]
        val targetColumn = data["target_column"] ?: data["targetColumn"]

        if (sourceColumn != null && targetColumn != null) {
            val sourceColumnInfo = ColumnInfo(
                columnName = sourceColumn,
                dataType = data["source_column_type"] ?: "VARCHAR",
                comment = null,
                table = sourceTable
            )

            val targetColumnInfo = ColumnInfo(
                columnName = targetColumn,
                dataType = data["target_column_type"] ?: "VARCHAR",
                comment = null,
                table = targetTable
            )

            columnLineages.add(
                ColumnLineage(
                    sourceColumn = sourceColumnInfo,
                    targetColumn = targetColumnInfo,
                    transformation = null,
                    columnIndex = 0
                )
            )
        }

        val originalSql = data["sql"] ?: data["original_sql"] ?: "-- CSV导入的血缘数据"

        return DataLineage(
            jobId = jobId,
            jobName = jobName,
            tableLineage = tableLineage,
            columnLineages = columnLineages,
            sourceDatabase = sourceDatabase,
            targetDatabase = targetDatabase,
            originalSql = originalSql,
            createdAt = LocalDateTime.now()
        )
    }

    /**
     * 解析Excel格式的血缘文件 (Parse Excel lineage file)
     *
     * 支持查找包含指定列标题的行，并从该行开始解析血缘数据
     * 预期的列标题：源数据源信息、源Schema、源表名、源字段名、目标数据源信息、目标Schema、目标表名、目标字段名
     */
    private fun parseExcelFile(file: MultipartFile, fileName: String): List<DataLineage> {
        logger.info("7a4b5e8f | 开始解析Excel格式血缘文件: fileName=$fileName")

        try {
            val workbook: Workbook = file.inputStream.use { inputStream ->
                when {
                    fileName.endsWith(".xlsx", ignoreCase = true) -> XSSFWorkbook(inputStream)
                    fileName.endsWith(".xls", ignoreCase = true) -> HSSFWorkbook(inputStream)
                    else -> throw IllegalArgumentException("不支持的Excel文件格式: $fileName")
                }
            }

            // 获取第一个工作表
            val sheet = workbook.getSheetAt(0)
                ?: throw IllegalArgumentException("Excel文件中没有找到工作表")

            logger.info("2d8f9c3a | Excel工作表解析: sheetName=${sheet.sheetName}, 总行数=${sheet.physicalNumberOfRows}")

            // 查找包含指定列标题的行
            val expectedHeaders = listOf(
                "源数据库类型",
                "源数据源信息",
                "源Schema",
                "源表名",
                "源字段名",
                "目标数据库类型",
                "目标数据源信息",
                "目标Schema",
                "目标表名",
                "目标字段名"
            )
            val headerRowInfo = findExcelHeaderRow(sheet, expectedHeaders)

            if (headerRowInfo == null) {
                throw IllegalArgumentException("Excel文件中没有找到包含标题行: ${expectedHeaders.joinToString(", ")}")
            }

            val (headerRowIndex, headerMapping) = headerRowInfo
            logger.info("5f1e4b7c | 找到Excel标题行: 行号=${headerRowIndex + 1}, 列映射=$headerMapping")

            // 从标题行下一行开始解析数据
            val dataRows = mutableListOf<Row>()
            for (rowIndex in (headerRowIndex + 1) until sheet.physicalNumberOfRows) {
                val row = sheet.getRow(rowIndex)
                if (row != null && !isExcelRowEmpty(row)) {
                    dataRows.add(row)
                }
            }

            if (dataRows.isEmpty()) {
                throw IllegalArgumentException("Excel文件中没有找到数据行")
            }

            logger.info("9c3a6e2d | Excel数据解析: 数据行数=${dataRows.size}")

            // 解析每一行数据
            val excelRows = mutableListOf<ExcelLineageRow>()
            dataRows.forEachIndexed { index, row ->
                try {
                    val excelRow = parseExcelLineage(row, headerMapping, fileName, index)
                    if (excelRow != null) {
                        excelRows.add(excelRow)
                    }
                } catch (e: Exception) {
                    logger.error("4e8a2f5b | 解析Excel第${row.rowNum + 1}行失败", e)
                    throw IllegalArgumentException("Excel第${row.rowNum + 1}行解析失败: ${e.message}", e)
                }
            }

            // 按表分组转换为DataLineage
            val result = convertExcelRowsToDataLineage(excelRows, fileName)

            logger.info("1b7d9f4e | Excel文件解析完成: fileName=$fileName, 解析出血缘数=${result.size}")
            workbook.close()

            return result

        } catch (e: Exception) {
            logger.error("8f2e5a7c | 解析Excel文件失败: fileName=$fileName", e)
            throw IllegalArgumentException("Excel文件解析失败: ${e.message}", e)
        }
    }

    /**
     * 查找Excel标题行 (Find Excel header row)
     */
    private fun findExcelHeaderRow(sheet: Sheet, expectedHeaders: List<String>): Pair<Int, Map<String, Int>>? {
        for (rowIndex in 0 until minOf(sheet.physicalNumberOfRows, 20)) { // 只查找前20行
            val row = sheet.getRow(rowIndex) ?: continue

            val headerMapping = mutableMapOf<String, Int>()
            var matchCount = 0

            for (cellIndex in 0 until row.lastCellNum) {
                val cell = row.getCell(cellIndex)
                if (cell != null) {
                    val cellValue = getCellValueAsString(cell).trim()
                    expectedHeaders.forEach { expectedHeader ->
                        if (cellValue.equals(expectedHeader, ignoreCase = true)) {
                            headerMapping[expectedHeader] = cellIndex
                            matchCount++
                        }
                    }
                }
            }

            // 如果找到了所有必需的列标题，返回这一行
            if (matchCount >= expectedHeaders.size) {
                return Pair(rowIndex, headerMapping)
            }
        }

        return null
    }

    /**
     * 检查Excel行是否为空 (Check if Excel row is empty)
     */
    private fun isExcelRowEmpty(row: Row): Boolean {
        for (cellIndex in 0 until row.lastCellNum) {
            val cell = row.getCell(cellIndex)
            if (cell != null && getCellValueAsString(cell).trim().isNotEmpty()) {
                return false
            }
        }
        return true
    }

    /**
     * 获取单元格值作为字符串 (Get cell value as string)
     */
    private fun getCellValueAsString(cell: Cell): String {
        return when (cell.cellType) {
            CellType.STRING -> cell.stringCellValue
            CellType.NUMERIC -> {
                if (DateUtil.isCellDateFormatted(cell)) {
                    cell.dateCellValue.toString()
                } else {
                    cell.numericCellValue.toString()
                }
            }

            CellType.BOOLEAN -> cell.booleanCellValue.toString()
            CellType.FORMULA -> cell.cellFormula
            CellType.BLANK -> ""
            else -> cell.toString()
        }
    }

    /**
     * 解析Excel行血缘数据 (Parse Excel lineage row)
     */
    private fun parseExcelLineage(
        row: Row,
        headerMapping: Map<String, Int>,
        fileName: String,
        rowIndex: Int
    ): ExcelLineageRow? {
        try {
            val sourceDbType = getCellValue(row, headerMapping, "源数据库类型")?.trim()
            val sourceDatasource = getCellValue(row, headerMapping, "源数据源信息")?.trim()
            val sourceSchema = getCellValue(row, headerMapping, "源Schema")?.trim()
            val sourceTable = getCellValue(row, headerMapping, "源表名")?.trim()
            val sourceColumn = getCellValue(row, headerMapping, "源字段名")?.trim()
            val targetDbType = getCellValue(row, headerMapping, "目标数据库类型")?.trim()
            val targetDatasource = getCellValue(row, headerMapping, "目标数据源信息")?.trim()
            val targetSchema = getCellValue(row, headerMapping, "目标Schema")?.trim()
            val targetTable = getCellValue(row, headerMapping, "目标表名")?.trim()
            val targetColumn = getCellValue(row, headerMapping, "目标字段名")?.trim()

            // 验证必填字段
            if (sourceDatasource.isNullOrBlank() || sourceTable.isNullOrBlank() ||
                targetDatasource.isNullOrBlank() || targetTable.isNullOrBlank()
            ) {
                logger.warn("6c5a8e2f | Excel第${row.rowNum + 1}行跳过: 缺少必填字段")
                return null
            }

            // 验证数据库类型 (如果提供了数据库类型，则验证是否为支持的类型)
            if (!sourceDbType.isNullOrBlank() && !isValidDbType(sourceDbType)) {
                logger.warn("7d2a9f4b | Excel第${row.rowNum + 1}行跳过: 不支持的源数据库类型 '$sourceDbType'")
                return null
            }

            if (!targetDbType.isNullOrBlank() && !isValidDbType(targetDbType)) {
                logger.warn("8e3b1c5d | Excel第${row.rowNum + 1}行跳过: 不支持的目标数据库类型 '$targetDbType'")
                return null
            }

            return ExcelLineageRow(
                sourceDbType = sourceDbType,
                sourceDatasource = sourceDatasource,
                sourceSchema = sourceSchema,
                sourceTable = sourceTable,
                sourceColumn = sourceColumn,
                targetDbType = targetDbType,
                targetDatasource = targetDatasource,
                targetSchema = targetSchema,
                targetTable = targetTable,
                targetColumn = targetColumn,
                rowNumber = row.rowNum + 1
            )

        } catch (e: Exception) {
            logger.error("3f9b4e7a | 解析Excel行失败: rowNum=${row.rowNum + 1}", e)
            throw e
        }
    }

    /**
     * 获取单元格值 (Get cell value)
     */
    private fun getCellValue(row: Row, headerMapping: Map<String, Int>, columnName: String): String? {
        val columnIndex = headerMapping[columnName] ?: return null
        val cell = row.getCell(columnIndex) ?: return null
        return getCellValueAsString(cell)
    }

    /**
     * 从数据源名称解析数据库类型 (Parse database type from datasource name)
     */
    private fun parseDbTypeFromDatasource(datasource: String): String {
        return when {
            datasource.contains("mysql", ignoreCase = true) -> "mysql"
            datasource.contains("oracle", ignoreCase = true) -> "oracle"
            datasource.contains("postgresql", ignoreCase = true) -> "postgresql"
            datasource.contains("hive", ignoreCase = true) -> "hive"
            datasource.contains("sqlserver", ignoreCase = true) -> "sqlserver"
            else -> "unknown"
        }
    }

    /**
     * 解析数据源信息获取主机和端口 (Parse datasource to extract host and port)
     *
     * 支持的格式：
     * - hostname:port
     * - hostname:port/database
     * - ***********************************
     * - 简单名称（使用默认值）
     */
    private fun parseDatasourceHostAndPort(datasource: String): Pair<String, Int> {
        return try {
            when {
                // JDBC URL格式: ***********************************
                datasource.startsWith("jdbc:", ignoreCase = true) -> {
                    val regex = Regex("jdbc:[^:]+://([^:/]+)(?::(\\d+))?")
                    val matchResult = regex.find(datasource)
                    if (matchResult != null) {
                        val host = matchResult.groupValues[1]
                        val port =
                            matchResult.groupValues[2].takeIf { it.isNotEmpty() }?.toIntOrNull() ?: getDefaultPort(
                                datasource
                            )
                        Pair(host, port)
                    } else {
                        Pair("unknown", 3306)
                    }
                }
                // hostname:port或hostname:port/database格式
                datasource.contains(":") -> {
                    val parts = datasource.split(":")
                    if (parts.size >= 2) {
                        val host = parts[0]
                        val portPart = parts[1].split("/")[0] // 移除可能的数据库名
                        val port = portPart.toIntOrNull() ?: getDefaultPort(datasource)
                        Pair(host, port)
                    } else {
                        Pair("unknown", getDefaultPort(datasource))
                    }
                }
                // 简单名称，使用默认值
                else -> {
                    Pair(datasource, getDefaultPort(datasource))
                }
            }
        } catch (e: Exception) {
            logger.warn("解析数据源失败，使用默认值: datasource=$datasource", e)
            Pair("unknown", 3306)
        }
    }

    /**
     * 根据数据源类型获取默认端口 (Get default port by datasource type)
     */
    private fun getDefaultPort(datasource: String): Int {
        return when {
            datasource.contains("mysql", ignoreCase = true) -> 3306
            datasource.contains("oracle", ignoreCase = true) -> 1521
            datasource.contains("postgresql", ignoreCase = true) -> 5432
            datasource.contains("sqlserver", ignoreCase = true) -> 1433
            datasource.contains("hive", ignoreCase = true) -> 10000
            else -> 3306
        }
    }

    /**
     * 验证数据库类型是否支持 (Validate if database type is supported)
     */
    private fun isValidDbType(dbType: String): Boolean {
        return supportedDbTypes.contains(dbType.lowercase().trim())
    }

    /**
     * 从数据源字符串中提取数据库名称 (Extract database name from datasource string)
     *
     * 支持的格式：
     * - **********:1521/lisdb -> lisdb
     * - **********:1521,**********/lisdb.lisdb -> lisdb.lisdb
     * - *********************************** -> database
     * - 简单名称 -> 使用原始值
     */
    private fun extractDatabaseNameFromDatasource(datasource: String): String {
        return try {
            when {
                // JDBC URL格式: ***********************************
                datasource.startsWith("jdbc:", ignoreCase = true) -> {
                    val regex = Regex("jdbc:[^:]+://[^/]+/([^?]+)")
                    val matchResult = regex.find(datasource)
                    matchResult?.groupValues?.get(1) ?: datasource
                }
                // Oracle格式: host:port/database 或 host:port,host2/database
                datasource.contains("/") -> {
                    val parts = datasource.split("/")
                    if (parts.size >= 2) {
                        parts.last().trim()
                    } else {
                        datasource
                    }
                }
                // 简单名称，直接返回
                else -> datasource
            }
        } catch (e: Exception) {
            logger.warn("9f8e7a2c | 从数据源提取数据库名称失败，使用原始值: datasource=$datasource", e)
            datasource
        }
    }

    /**
     * 将Excel行转换为DataLineage (Convert Excel rows to DataLineage)
     */
    private fun convertExcelRowsToDataLineage(excelRows: List<ExcelLineageRow>, fileName: String): List<DataLineage> {
        // 按表级关系分组
        val tableGroups = excelRows.groupBy {
            "${it.sourceDatasource}:${it.sourceSchema}:${it.sourceTable} -> ${it.targetDatasource}:${it.targetSchema}:${it.targetTable}"
        }

        return tableGroups.map { (tableKey, rows) ->
            val firstRow = rows.first()
            val jobId = "manual_excel_${fileName}_${System.currentTimeMillis()}_${tableKey.hashCode()}"
            val jobName = "Excel导入-$fileName-${firstRow.sourceTable}-${firstRow.targetTable}"

            // 解析源数据源的主机和端口
            val (sourceHost, sourcePort) = parseDatasourceHostAndPort(firstRow.sourceDatasource)

            // 构建源数据库信息
            val sourceDatabase = DatabaseInfo(
                dbType = firstRow.sourceDbType ?: parseDbTypeFromDatasource(firstRow.sourceDatasource),
                host = sourceHost,
                port = sourcePort,
                databaseName = extractDatabaseNameFromDatasource(firstRow.sourceDatasource),
                originalConnectionString = firstRow.sourceDatasource
            )

            // 解析目标数据源的主机和端口
            val (targetHost, targetPort) = parseDatasourceHostAndPort(firstRow.targetDatasource)

            // 构建目标数据库信息
            val targetDatabase = DatabaseInfo(
                dbType = firstRow.targetDbType ?: parseDbTypeFromDatasource(firstRow.targetDatasource),
                host = targetHost,
                port = targetPort,
                databaseName = extractDatabaseNameFromDatasource(firstRow.targetDatasource),
                originalConnectionString = firstRow.targetDatasource
            )

            // 构建源表信息 - 空白schema使用null
            val sourceTableInfo = TableInfo(
                tableName = firstRow.sourceTable,
                schema = firstRow.sourceSchema?.takeIf { it.isNotBlank() },
                database = sourceDatabase
            )

            // 构建目标表信息 - 空白schema使用null
            val targetTableInfo = TableInfo(
                tableName = firstRow.targetTable,
                schema = firstRow.targetSchema?.takeIf { it.isNotBlank() },
                database = targetDatabase
            )

            // 构建表血缘
            val tableLineage = TableLineage(
                sourceTables = listOf(sourceTableInfo),
                targetTable = targetTableInfo
            )

            // 构建列血缘 - 只处理非空的列映射
            val columnLineages = rows.mapIndexedNotNull { index, row ->
                if (!row.sourceColumn.isNullOrBlank() && !row.targetColumn.isNullOrBlank()) {
                    val sourceColumnInfo = ColumnInfo(
                        columnName = row.sourceColumn,
                        dataType = "unknown",
                        comment = null,
                        table = sourceTableInfo
                    )
                    val targetColumnInfo = ColumnInfo(
                        columnName = row.targetColumn,
                        dataType = "unknown",
                        comment = null,
                        table = targetTableInfo
                    )

                    ColumnLineage(
                        sourceColumn = sourceColumnInfo,
                        targetColumn = targetColumnInfo,
                        transformation = null,
                        columnIndex = index
                    )
                } else null
            }

            DataLineage(
                jobId = jobId,
                jobName = jobName,
                tableLineage = tableLineage,
                columnLineages = columnLineages,
                sourceDatabase = sourceDatabase,
                targetDatabase = targetDatabase,
                originalSql = "-- Excel手动导入的血缘数据",
                createdAt = LocalDateTime.now()
            )
        }
    }

    /**
     * 验证血缘数据 (Validate lineage data)
     */
    fun validateLineageData(lineages: List<DataLineage>) {
        if (lineages.isEmpty()) {
            throw IllegalArgumentException("解析到的血缘数据为空")
        }

        lineages.forEachIndexed { index, lineage ->
            try {
                validateSingleLineage(lineage)
            } catch (e: Exception) {
                throw IllegalArgumentException("第${index + 1}个血缘记录验证失败: ${e.message}", e)
            }
        }

        // 验证数据源是否存在
        try {
            validateDatasourcesExist(lineages)
        } catch (e: Exception) {
            throw IllegalArgumentException("数据源验证失败: ${e.message}", e)
        }

        logger.info("5c8f2a69 | 血缘数据验证完成: 共${lineages.size}条记录")
    }

    /**
     * 验证单个血缘数据 (Validate single lineage data)
     */
    private fun validateSingleLineage(lineage: DataLineage) {
        if (lineage.jobId.isBlank()) {
            throw IllegalArgumentException("jobId不能为空")
        }

        if (lineage.jobName.isBlank()) {
            throw IllegalArgumentException("jobName不能为空")
        }

        if (lineage.tableLineage.sourceTables.isEmpty()) {
            throw IllegalArgumentException("源表列表不能为空")
        }

        if (lineage.tableLineage.targetTable.tableName.isBlank()) {
            throw IllegalArgumentException("目标表名不能为空")
        }

        // 验证列血缘中的表引用
        lineage.columnLineages.forEach { columnLineage ->
            val sourceTableFound = lineage.tableLineage.sourceTables.any {
                it.tableName == columnLineage.sourceColumn.table.tableName
            }
            if (!sourceTableFound) {
                throw IllegalArgumentException(
                    "列血缘中的源表 '${columnLineage.sourceColumn.table.tableName}' 不在表血缘的源表列表中"
                )
            }

            if (columnLineage.targetColumn.table.tableName != lineage.tableLineage.targetTable.tableName) {
                throw IllegalArgumentException(
                    "列血缘中的目标表 '${columnLineage.targetColumn.table.tableName}' 与表血缘的目标表不匹配"
                )
            }
        }
    }

    /**
     * 验证数据源是否存在于metadata_data_source表中 (Validate datasources exist in metadata_data_source table)
     */
    private fun validateDatasourcesExist(lineages: List<DataLineage>) {
        logger.info("2e4a9f3b | 开始验证数据源是否存在于metadata_data_source表中")
        
        // 获取所有活跃的元数据数据源
        val activeDataSources = metadataService.getAllActiveMetadataDataSources()
        
        // 构建数据源标识符集合，用于快速查找
        val dataSourceIdentifiers = buildDataSourceIdentifierSet(activeDataSources)
        
        // 收集所有血缘数据中的数据源
        val lineageDataSources = mutableSetOf<String>()
        
        lineages.forEach { lineage ->
            // 添加源数据库数据源
            lineageDataSources.add(buildDatabaseIdentifier(lineage.sourceDatabase))
            
            // 添加目标数据库数据源
            lineageDataSources.add(buildDatabaseIdentifier(lineage.targetDatabase))
        }
        
        // 检查每个数据源是否存在
        val nonExistentDataSources = mutableSetOf<String>()
        
        lineageDataSources.forEach { dataSourceId ->
            if (!dataSourceIdentifiers.contains(dataSourceId)) {
                nonExistentDataSources.add(dataSourceId)
            }
        }
        
        if (nonExistentDataSources.isNotEmpty()) {
            val errorMessage = "以下数据源不存在于metadata_data_source表中: ${nonExistentDataSources.sorted().joinToString(", ")}"
            logger.warn("7c8d5e2f | $errorMessage")
            throw IllegalArgumentException(errorMessage)
        }
        
        logger.info("3a9b7c4d | 数据源验证通过: 共验证${lineageDataSources.size}个数据源")
    }

    /**
     * 构建数据源标识符集合 (Build datasource identifier set)
     */
    private fun buildDataSourceIdentifierSet(dataSources: List<com.datayes.metadata.MetadataDataSourceDto>): Set<String> {
        val identifiers = mutableSetOf<String>()
        
        dataSources.forEach { dataSource ->
            // 使用SOURCE_NAME作为主要标识符
            identifiers.add(dataSource.sourceName)
            
            // 同时使用 host:port/database 格式作为备选标识符（处理null值）
            if (dataSource.dbUrl != null && dataSource.dbPort != null) {
                val hostPortDb = "${dataSource.dbUrl}:${dataSource.dbPort}/${dataSource.dbName}"
                identifiers.add(hostPortDb)
            }
            
            // 如果有自定义JDBC URL，也提取其标识符
            dataSource.customJdbcUrl?.let { customUrl ->
                com.datayes.lineage.DatabaseInfo.parseFromJdbcUrl(customUrl)?.let { dbInfo ->
                    identifiers.add("${dbInfo.host}:${dbInfo.port}/${dbInfo.databaseName}")
                }
            }
        }
        
        return identifiers
    }

    /**
     * 构建数据库标识符 (Build database identifier)
     */
    private fun buildDatabaseIdentifier(databaseInfo: DatabaseInfo): String {
        return "${databaseInfo.host}:${databaseInfo.port}/${databaseInfo.databaseName}"
    }
}

/**
 * Excel血缘行数据类 (Excel Lineage Row Data Class)
 *
 * 表示Excel文件中的一行血缘数据
 */
data class ExcelLineageRow(
    val sourceDbType: String?,
    val sourceDatasource: String,
    val sourceSchema: String?,
    val sourceTable: String,
    val sourceColumn: String?,
    val targetDbType: String?,
    val targetDatasource: String,
    val targetSchema: String?,
    val targetTable: String,
    val targetColumn: String?,
    val rowNumber: Int
)