package com.datayes.lineage

import java.time.LocalDateTime

/**
 * 作业处理历史数据类 (Job Processing History Data Class)
 */
data class JobLineageHashHistory(
    val id: Long = 0,
    val jobKey: String,
    val jobType: JobType,
    val readerJobId: String? = null,
    val writeJobId: String? = null,
    val processedAt: LocalDateTime = LocalDateTime.now(),
    val processingResult: ProcessingResult,
    val changesDetected: Boolean,
    val processingDurationMs: Long,
    val lineageHash: String,
    val errorMessage: String? = null
)

/**
 * 作业类型枚举 (Job Type Enum)
 */
enum class JobType {
    DATA_EXCHANGE,      // 数据交换作业
    HDFS_SHELL_SCRIPT   // HDFS Shell脚本作业
}

/**
 * 处理结果枚举 (Processing Result Enum)
 */
enum class ProcessingResult {
    NO_CHANGE,  // 无变更
    UPDATED,    // 已更新
    FAILED      // 处理失败
}