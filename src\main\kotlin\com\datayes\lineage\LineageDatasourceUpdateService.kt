package com.datayes.lineage

import com.datayes.metadata.MetadataService
import org.slf4j.LoggerFactory
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

/**
 * 血缘数据源更新服务 (Lineage Datasource Update Service)
 * 
 * 负责匹配并更新lineage_datasources表中的metadata_data_source_id和system_id字段
 */
@Service
class LineageDatasourceUpdateService(
    private val jdbcTemplate: JdbcTemplate,
    private val metadataService: MetadataService
) {
    
    private val logger = LoggerFactory.getLogger(LineageDatasourceUpdateService::class.java)
    
    /**
     * 更新所有lineage_datasources行的metadata_data_source_id和system_id
     * 
     * @return 更新结果统计
     */
    @Transactional
    fun updateAllDatasourcesMetadata(): DatasourceUpdateResult {
        logger.info("a1b2c3d4 | 开始更新所有血缘数据源的元数据信息")
        
        // 查询所有需要更新的血缘数据源
        val lineageDatasources = queryAllLineageDatasources()
        
        var successCount = 0
        var errorCount = 0
        var noMatchCount = 0
        val errors = mutableListOf<String>()
        
        lineageDatasources.forEach { datasource ->
            try {
                val updateResult = updateSingleDatasourceMetadata(datasource)
                when (updateResult) {
                    is DatasourceUpdateResult.SingleResult.Success -> successCount++
                    is DatasourceUpdateResult.SingleResult.NoMatch -> noMatchCount++
                    is DatasourceUpdateResult.SingleResult.Error -> {
                        errorCount++
                        errors.add("ID ${datasource.id}: ${updateResult.message}")
                    }
                }
            } catch (e: Exception) {
                errorCount++
                errors.add("ID ${datasource.id}: ${e.message}")
                logger.error("e5f6g7h8 | 更新血缘数据源失败: datasourceId=${datasource.id}", e)
            }
        }
        
        val result = DatasourceUpdateResult(
            totalProcessed = lineageDatasources.size,
            successCount = successCount,
            noMatchCount = noMatchCount,
            errorCount = errorCount,
            errors = errors,
            processedAt = LocalDateTime.now()
        )
        
        logger.info("i9j0k1l2 | 血缘数据源更新完成: ${result.summarize()}")
        return result
    }
    
    /**
     * 更新单个血缘数据源的元数据信息
     */
    private fun updateSingleDatasourceMetadata(datasource: LineageDatasourceInfo): DatasourceUpdateResult.SingleResult {
        logger.debug("m3n4o5p6 | 开始更新单个血缘数据源: datasourceId=${datasource.id}")
        
        // 使用现有的MetadataService查找匹配的元数据数据源
        val matchResult = metadataService.findMatchedMetadataDataSources(datasource.id)
        
        if (matchResult.matchedDataSources.isEmpty()) {
            logger.info("909ff952 | 未找到匹配的元数据数据源: datasourceId=${datasource.id}")
            return DatasourceUpdateResult.SingleResult.NoMatch(datasource.id)
        }
        
        // 如果有多个匹配结果，选择第一个并记录警告
        val matchedDataSource = matchResult.matchedDataSources.first()
        if (matchResult.matchedDataSources.size > 1) {
            logger.warn("u1v2w3x4 | 找到多个匹配的元数据数据源，选择第一个: datasourceId=${datasource.id}, 匹配数量=${matchResult.matchedDataSources.size}, 选择的metadataDataSourceId=${matchedDataSource.id}")
        }
        
        // 更新lineage_datasources表
        val updateCount = updateLineageDatasourceFields(
            lineageDatasourceId = datasource.id,
            metadataDataSourceId = matchedDataSource.id
        )
        
        if (updateCount > 0) {
            logger.debug("y5z6a7b8 | 更新血缘数据源成功: datasourceId=${datasource.id}, metadataDataSourceId=${matchedDataSource.id}")
            return DatasourceUpdateResult.SingleResult.Success(
                lineageDatasourceId = datasource.id,
                metadataDataSourceId = matchedDataSource.id
            )
        } else {
            val message = "数据库更新失败，影响行数为0"
            logger.error("c9d0e1f2 | $message: datasourceId=${datasource.id}")
            return DatasourceUpdateResult.SingleResult.Error(datasource.id, message)
        }
    }
    
    /**
     * 更新lineage_datasources表的metadata_data_source_id字段
     */
    private fun updateLineageDatasourceFields(
        lineageDatasourceId: Long,
        metadataDataSourceId: Long
    ): Int {
        val sql = """
            UPDATE lineage_datasources 
            SET metadata_data_source_id = ?,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        """.trimIndent()
        
        return jdbcTemplate.update(sql, metadataDataSourceId, lineageDatasourceId)
    }
    
    /**
     * 查询所有血缘数据源
     */
    private fun queryAllLineageDatasources(): List<LineageDatasourceInfo> {
        logger.info("g3h4i5j6 | 查询所有血缘数据源")
        
        val sql = """
            SELECT id, datasource_name, db_type, host, port, database_name, system_id, status,
                   metadata_data_source_id
            FROM lineage_datasources
            WHERE status = 'ACTIVE'
            ORDER BY id
        """.trimIndent()
        
        return jdbcTemplate.query(sql) { rs, _ ->
            LineageDatasourceInfo(
                id = rs.getLong("id"),
                datasourceName = rs.getString("datasource_name"),
                dbType = rs.getString("db_type"),
                host = rs.getString("host"),
                port = rs.getInt("port"),
                databaseName = rs.getString("database_name"),
                systemId = rs.getObject("system_id") as? Long,
                status = rs.getString("status"),
                metadataDataSourceId = rs.getObject("metadata_data_source_id") as? Long
            )
        }
    }
    
    /**
     * 获取血缘数据源元数据填充统计信息
     */
    fun getDatasourceMetadataStats(): DatasourceMetadataStats {
        logger.info("h7i8j9k0 | 查询血缘数据源元数据统计信息")
        
        val sql = """
            SELECT 
                COUNT(*) as total_datasources,
                SUM(CASE WHEN metadata_data_source_id IS NOT NULL THEN 1 ELSE 0 END) as with_metadata_data_source_id
            FROM lineage_datasources
            WHERE status = 'ACTIVE'
        """.trimIndent()
        
        return jdbcTemplate.queryForObject(sql) { rs, _ ->
            val total = rs.getInt("total_datasources")
            val withMetadataDataSourceId = rs.getInt("with_metadata_data_source_id")
            
            DatasourceMetadataStats(
                totalDatasources = total,
                withMetadataDataSourceId = withMetadataDataSourceId,
                unmatchedCount = total - withMetadataDataSourceId
            )
        } ?: DatasourceMetadataStats(0, 0, 0)
    }
}

/**
 * 血缘数据源信息 (用于内部查询)
 */
data class LineageDatasourceInfo(
    val id: Long,
    val datasourceName: String,
    val dbType: String,
    val host: String,
    val port: Int,
    val databaseName: String,
    val systemId: Long?,
    val status: String,
    val metadataDataSourceId: Long? = null
)

/**
 * 数据源更新结果 (Datasource Update Result)
 */
data class DatasourceUpdateResult(
    val totalProcessed: Int,
    val successCount: Int,
    val noMatchCount: Int,
    val errorCount: Int,
    val errors: List<String>,
    val processedAt: LocalDateTime
) {
    fun summarize(): String {
        return "总计处理: $totalProcessed, 成功: $successCount, 无匹配: $noMatchCount, 失败: $errorCount"
    }
    
    sealed class SingleResult {
        data class Success(
            val lineageDatasourceId: Long,
            val metadataDataSourceId: Long
        ) : SingleResult()
        
        data class NoMatch(val lineageDatasourceId: Long) : SingleResult()
        
        data class Error(
            val lineageDatasourceId: Long,
            val message: String
        ) : SingleResult()
    }
}

/**
 * 血缘数据源元数据统计信息
 */
data class DatasourceMetadataStats(
    val totalDatasources: Int,
    val withMetadataDataSourceId: Int,
    val unmatchedCount: Int
) {
    fun getMetadataDataSourceIdFillRate(): Double {
        return if (totalDatasources > 0) withMetadataDataSourceId.toDouble() / totalDatasources else 0.0
    }
}